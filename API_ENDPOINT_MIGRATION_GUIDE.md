# Agent Role Policies API Endpoint Migration Guide

## Overview

The Agent Role Policies API has been enhanced with new flat endpoints alongside the existing nested endpoints for better consistency and usability. Both endpoint patterns are supported for backward compatibility.

## Endpoint Comparison

### Legacy Nested Endpoints (Still Supported)
- `GET /api/v1/agents/:id/role-policies` - List role-scoped policies for agent
- `POST /api/v1/agents/:id/role-policies` - Add single role-scoped policy
- `POST /api/v1/agents/:id/role-policies/bulk` - Add multiple role-scoped policies
- `DELETE /api/v1/agents/:id/role-policies` - Remove role-scoped policy

### New Flat Endpoints (Recommended)
- `GET /api/v1/agent-role-policies/:agentId` - List role-scoped policies for agent
- `POST /api/v1/agent-role-policies` - Add single role-scoped policy
- `POST /api/v1/agent-role-policies/bulk` - Add multiple role-scoped policies
- `DELETE /api/v1/agent-role-policies` - Remove role-scoped policy

## Request Format Differences

### Legacy Nested Format
```javascript
// URL parameter for agent ID
POST /api/v1/agents/123e4567-e89b-12d3-a456-************/role-policies

// Request body (agentId inferred from URL)
{
  "roleId": "456e7890-e89b-12d3-a456-************",
  "groupId": "789e0123-e89b-12d3-a456-************", 
  "policyId": "012e3456-e89b-12d3-a456-************"
}
```

### New Flat Format (Recommended)
```javascript
// No URL parameters needed
POST /api/v1/agent-role-policies

// Request body (explicit agentId)
{
  "agentId": "123e4567-e89b-12d3-a456-************",
  "roleId": "456e7890-e89b-12d3-a456-************",
  "groupId": "789e0123-e89b-12d3-a456-************",
  "policyId": "012e3456-e89b-12d3-a456-************"
}
```

## Migration Benefits

### Consistency
- Flat endpoints follow standard REST conventions
- Consistent request body format across all operations
- No URL parameter dependencies

### Flexibility
- Easier to use in batch operations
- Better for API clients that prefer explicit parameters
- Simplified request routing and middleware handling

### Testing
- E2E tests have been updated to use flat endpoints
- Better test isolation and maintainability

## Migration Timeline

### Phase 1 (Current) - Dual Support
- Both endpoint patterns are fully supported
- No breaking changes to existing clients
- New implementations should use flat endpoints

### Phase 2 (Future) - Deprecation Notice
- Legacy nested endpoints will be marked as deprecated
- 6-month deprecation notice will be provided
- Documentation will clearly indicate preferred endpoints

### Phase 3 (Future) - Legacy Removal
- Legacy nested endpoints will be removed
- Only flat endpoints will be supported
- Major version bump to indicate breaking changes

## Implementation Details

### Backend Changes
All endpoints are implemented in `/enhanced-api-project/src/api/agentRolePolicies.js`:

- Lines 8-111: Legacy nested endpoints
- Lines 117-219: New flat endpoints
- Identical business logic and validation
- Same database operations and error handling

### Database Schema
No database changes required - both endpoint patterns use the same:
- `agent_role_policies` table
- Same validation logic for group-policy relationships
- Same conflict resolution (`ON CONFLICT DO NOTHING`)

### Testing Updates
E2E tests in `/tests/e2e-policy-lifecycle-test.js` have been updated to use flat endpoints for better maintainability and consistency with the recommended API patterns.

## Recommendations

### For New Development
- Use flat endpoints (`/api/v1/agent-role-policies`) for all new implementations
- Explicit parameter passing is more maintainable
- Better alignment with REST API best practices

### For Existing Code
- Legacy endpoints remain fully functional
- No immediate migration required
- Consider migrating during next major refactor

### Best Practices
- Always validate group-policy relationships before assignment
- Use bulk endpoints for multiple policy assignments
- Implement proper error handling for validation failures
- Follow the established request/response patterns

## Support

For questions about the migration or endpoint usage, refer to:
- API documentation in `/enhanced-api-project/src/api/agentRolePolicies.js`
- E2E test examples in `/tests/e2e-policy-lifecycle-test.js`
- Database schema in `/scripts/00-create-complete-postgres-schema-consolidated.sql`