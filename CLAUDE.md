# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the Vitea.ai Policy Management System - a HIPAA-compliant healthcare policy management platform with enhanced validation, audit logging, and dynamic enum management. The system is part of a larger distributed microservices ecosystem designed for enterprise policy management, AI evaluation, and regulatory compliance.

## Distributed System Architecture

This is **ONE SERVICE** in a multi-repository distributed system. The complete system includes:

### External Dependencies (Other Repositories)
- **Testing Platform** (`../testing-observability-platform`): AI evaluation API on port 9000/9002
- **Policy Runtime** (`../policy_runtime`): Policy execution engine on port 8000  
- **PII Guard** (`../PiiGuard`): PII protection service on port 8080
- **Envoy Proxy** (`../envoy_fhir_proxy`): FHIR proxy on ports 8081/9901
- **MCP Server** (`../vitea-mcp-server`): Model Context Protocol server on port 8002

### This Repository's Services (Pilot)
- **Enhanced API** (`enhanced-api-project/`): Node.js/Express.js backend with PostgreSQL (port 8001)
- **Admin UI** (`admin-ui-project/`): React policy management interface (port 3000 dev)
- **Frontend** (`frontend-project/`): Main application frontend with Azure AD auth (port 3001)
- **Database**: PostgreSQL 15+ with consolidated schema and stored procedures (port 5432)

### Key Technologies
- **Backend**: Node.js 18+ with ES modules (`"type": "module"`), Express.js, AJV validation
- **Frontend**: React 18 with Monaco Editor, Redux Toolkit, tailwindcss
- **Database**: PostgreSQL 15+ with JSON schema validation, audit logging, soft deletes
- **Integration**: Azure OpenAI, Azure AD/MSAL, Docker containerization
- **Testing**: Jest (API), React Testing Library (UI), Cypress (E2E)

## Development Commands

### Distributed System (Recommended)
```bash
# Start entire distributed system (all repositories)
./start-all-services.sh                 # Start services that aren't running
./start-all-services.sh --force-restart # Restart all containers
./start-all-services.sh --force-build   # Rebuild images and restart

# Stop entire distributed system  
./stop-all-services.sh

# Check system status
docker ps -a | grep -E "(pilot|mcp|policy|pii|envoy|eval)"
```

### Local Development (This Repository Only)
```bash
# Install all dependencies
npm install

# Start only pilot services (missing external dependencies)
docker-compose up
```

### Database Management
```bash
# Deploy consolidated schema (production-ready)
psql -h localhost -U dbadmin -d vitea_db -f scripts/00-create-complete-postgres-schema-consolidated.sql

# Load demo data for testing
./scripts/load-hipaa-demo-data.sh

# Check database health
docker exec pilot-postgres psql -U dbadmin -d vitea_db -c "SELECT version();"
```

### Enhanced API (`enhanced-api-project/`)
```bash
cd enhanced-api-project
npm install
npm start          # Production start
npm run dev        # Development with nodemon
npm test           # Run Jest tests
```

### Admin UI (`admin-ui-project/`)
```bash
cd admin-ui-project
npm install
npm start          # Development server (port 3000)
npm run build      # Production build
npm test           # Run React tests
```

### Frontend (`frontend-project/`)
```bash
cd frontend-project
npm install
npm start          # Development server
npm run build      # Production build
npm test           # Run React tests
```

## Database Architecture

### Consolidated Schema
The database uses a **consolidated migration approach** with a single, production-ready schema file:
- **Primary Schema**: `scripts/00-create-complete-postgres-schema-consolidated.sql` (1,500+ lines)
- **Sample Data**: `scripts/load-hipaa-sample-data-fixed.sql` (HIPAA-compliant test data)
- **Deprecated Files**: All individual migration files in `configs/` are marked `.deprecated`

### Key Database Features
- **34+ tables** with full audit logging and soft delete patterns
- **16+ stored procedures** for complex policy operations (search, validation, cloning)
- **12+ triggers** for automatic timestamp updates and audit trail
- **Dynamic enum management** system with categories and values
- **JSON schema validation** at database level using PostgreSQL JSON functions
- **HIPAA compliance** with comprehensive audit logging and data encryption

## Environment Configuration

### Environment Files Structure
- `.env.example` - Template with all required variables (committed)
- `.env` - Local development (git-ignored)
- `.env.dev` - Development VM deployment (git-ignored)
- `.env.production` - Production deployment (git-ignored)

### Docker Compose with Different Environments
```bash
# Local development
docker-compose up

# Development VM
docker-compose --env-file .env.dev up

# Production
docker-compose --env-file .env.production -f docker-compose.prod.yml up
```

## Key Architecture Patterns

### API Structure
- **RESTful APIs** with `/api/v1/` prefix and OpenAPI documentation
- **Express.js middleware stack**: CORS, helmet, rate limiting, compression
- **Database integration**: PostgreSQL with connection pooling and stored procedures
- **Validation**: AJV schema validation with custom error formatting
- **Authentication**: JWT tokens with Azure AD integration

### Database Design Patterns
- **Soft delete** pattern across all entities (`deleted_at` columns)
- **Audit logging** for HIPAA compliance with automatic triggers
- **Optimistic concurrency** control with version columns
- **JSON schema validation** at database level using PostgreSQL CHECK constraints
- **Dynamic enum management** with `enum_categories` and `enum_values` tables
- **Search optimization** with GIN indexes and full-text search functions

### Frontend Architecture
- **Component-based React** with functional components and hooks
- **State management**: Redux Toolkit for global state, local state for UI
- **Monaco Editor integration** with JSON schema autocomplete and validation
- **Real-time validation** using AJV with debounced input handling
- **Responsive design** with Tailwind CSS and mobile-first approach

### Inter-Service Communication
- **HTTP REST APIs** for synchronous communication
- **Feature flags** for gradual rollout and A/B testing  
- **Circuit breaker pattern** for external service resilience
- **Webhook integration** for external system notifications
- **MCP protocol** for AI model context sharing

## Important Development Notes

### Schema Management Crisis (Migration Context)
The system originally had **dual `policy_schemas.json` files** causing inconsistencies:
- `enhanced-api-project/configs/policy_schemas.json` 
- `admin-ui-project/src/policy_schemas.json`

This is being resolved through a **centralized schema registry migration** using sophisticated agent-based planning located in `.claude/agents/`. The 13-agent system orchestrates zero-downtime migration with feature flags and progressive rollout.

### Critical File Locations
- **Main Schema**: `scripts/00-create-complete-postgres-schema-consolidated.sql` (production-ready)
- **API Schema Config**: `enhanced-api-project/configs/policy_schemas.json`
- **UI Schema Config**: `admin-ui-project/src/policy_schemas.json` 
- **Migration Agents**: `.claude/agents/` (13 specialized migration agents)

### Development Patterns
- **ES Modules**: All Node.js code uses `"type": "module"` and import/export syntax
- **Environment Variables**: React variables (`REACT_APP_*`) are build-time, backend are runtime
- **Error Handling**: Comprehensive error boundaries with user-friendly messages
- **Validation**: Dual-layer validation (client AJV + database constraints)
- **Testing**: Jest (API), React Testing Library (UI), Cypress (E2E), pg-mem (database)

### HIPAA Compliance Requirements
- **Audit logging**: All policy operations automatically logged with user attribution
- **Data encryption**: Sensitive fields encrypted at rest and in transit
- **Access control**: Role-based permissions with fine-grained access controls
- **Data retention**: Configurable retention policies with automated cleanup

## Port Configuration

### This Repository (Pilot Services)
- **Enhanced API**: 8000 (container), 8001 (host) - `http://localhost:8001/health`
- **Admin UI**: 3000 (development) - `http://localhost:3000`
- **Frontend**: 80 (container), 3001 (host) - `http://localhost:3001`
- **PostgreSQL**: 5432 - `pilot-postgres:5432`

### External Services (Other Repositories)
- **Eval API**: 9000/9002 - `http://localhost:9000/health`
- **Policy Runtime**: 8000 - `http://localhost:8000/api/v1/health`
- **PII Guard**: 8080 - `http://localhost:8080/health`  
- **MCP Server**: 8002 - `http://localhost:8002/health`
- **Envoy Proxy**: 8081 - `http://localhost:8081`
- **Envoy Admin**: 9901 - `http://localhost:9901/ready`

## Common Development Tasks

### Running Tests
```bash
# API tests (Jest)
cd enhanced-api-project && npm test

# UI tests (React Testing Library)  
cd admin-ui-project && npm test

# E2E tests (Cypress)
cd admin-ui-project && npm run cy:run

# Single test file
cd enhanced-api-project && npm test -- policies.test.js
```

### Database Operations
```bash
# Access database directly
docker exec -it pilot-postgres psql -U dbadmin -d vitea_db

# Run specific migration
psql -h localhost -U dbadmin -d vitea_db -f configs/specific-migration.sql

# Check table structure
docker exec pilot-postgres psql -U dbadmin -d vitea_db -c "\d+ policies"

# View recent audit logs
docker exec pilot-postgres psql -U dbadmin -d vitea_db -c "SELECT * FROM audit_log ORDER BY created_at DESC LIMIT 10"
```

### Troubleshooting
```bash
# Check all containers
docker ps -a

# View API logs
docker logs pilot-api

# View database logs  
docker logs pilot-postgres

# Restart specific service
docker-compose restart pilot-api

# Force rebuild specific service
docker-compose up --build pilot-api
```