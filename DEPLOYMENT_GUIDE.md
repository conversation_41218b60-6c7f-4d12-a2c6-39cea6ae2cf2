# Quick Deployment Guide

## 🚀 IMPORTANT: Deployment Strategy

**This repository uses a temporary feature branch strategy for MVP external integration features.**

### **Which Branch to Deploy?**

| **Purpose** | **Branch** | **Contains** |
|-------------|------------|--------------|
| **Production** | `main` | ✅ Stable code + HIPAA features |
| **MVP/Demo** | `feature/external-integration-temp-dev` | ✅ Latest main + External Integration |

### **Quick Commands**

**For Production:**
```bash
git checkout main
git pull origin main
# Deploy main branch
```

**For MVP/External Integration Demo:**
```bash
git checkout feature/external-integration-temp-dev
git pull origin feature/external-integration-temp-dev
# Deploy temp-dev branch
```

### **📖 Full Documentation**
See [DEPLOYMENT_STRATEGY_TEMP_DEV.md](docs/DEPLOYMENT_STRATEGY_TEMP_DEV.md) for complete details.

### **🧪 Testing**
- **Main branch**: `npm test` + HIPAA validation
- **Feature branch**: Above + `./run-external-integration-test.sh`

### **🆘 Emergency Rollback**
```bash
git checkout main
git pull origin main
# Deploy main to rollback external integration features
```

---

**⚠️ CRITICAL: Main branch contains production-ready code. Feature branch contains experimental MVP features. Choose deployment target carefully!**