# Vitea Docker Setup

Complete Docker containerization for the Vitea AI Assistant platform.

## Architecture

- **PostgreSQL Database**: Containerized with persistent storage
- **API Server**: Node.js backend (`enhanced-api-project`)
- **Frontend**: Nginx serving both React apps (chatbot + admin UI)
- **External**: Azure OpenAI (remains as external managed service)

## Quick Start

1. **Configure environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your Azure OpenAI credentials
   ```

2. **Start all services:**
   ```bash
   docker-compose up -d
   ```

3. **Access applications:**
   - Main Chatbot: http://localhost:3000
   - Admin UI: http://localhost:3000/admin
   - API Health: http://localhost:8000/health

## Services

### Database (PostgreSQL)
- **Port**: 5432
- **Database**: vitea_db
- **User**: dbadmin
- **Password**: Set via `DB_PASSWORD` env var
- **Volumes**: `postgres_data` for persistence

### API Server
- **Port**: 8000
- **Health Check**: `/health`
- **Environment**: development (configurable)
- **Dependencies**: PostgreSQL

### Frontend (Nginx)
- **Port**: 80 (mapped to 3000)
- **Routes**:
  - `/` → Main chatbot interface
  - `/admin` → Policy management UI
  - `/api/*` → Proxied to API server

## Environment Variables

Required in `.env` file:

```bash
# Database
DB_PASSWORD=vitea123

# Azure OpenAI (External)
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your-api-key
AZURE_OPENAI_DEPLOYMENT_NAME=your-deployment-name
```

**Important**: Verify your Azure OpenAI deployment name is correct. Common names include:
- `gpt-4`
- `gpt-35-turbo` 
- `gpt-4-turbo`

If you get "DeploymentNotFound" errors, check your Azure OpenAI resource to confirm the exact deployment name.

## Commands

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down

# Rebuild and restart
docker-compose up --build -d

# Clean up (removes volumes)
docker-compose down -v
```

## Database Initialization

Database schema is automatically loaded on first startup:
- Base schema: `configs/database-schema.sql`
- Enhanced schema: `configs/enhanced-database-schema.sql`

## Production Deployment

For production:
1. Update `NODE_ENV=production` in docker-compose.yml
2. Configure Azure Key Vault variables
3. Use managed PostgreSQL instead of container
4. Set up proper SSL/TLS termination
5. Configure container orchestration (AKS, etc.)