# Docker Configuration Guide for Vitea Multi-Repository Setup

## Overview

This guide explains the standardized Docker configuration for the Vitea system across three repositories:
- **pilot** - Main application with PostgreSQL, API, and frontend
- **vitea-mcp-server** - MCP server for FHIR operations  
- **envoy_fhir_proxy** - FHIR proxy with policy enforcement

## Environment Configuration

### Environment Files Structure

Each repository now has standardized environment files:

```
.env.local      # Local development (this machine)
.env.dev        # Azure VM deployment  
```

### Usage

**For Local Development:**
```bash
# Copy .env.local to .env in each repository
cp .env.local .env
docker-compose up
```

**For Azure VM Deployment:**
```bash
# Copy .env.dev to .env and update IP addresses
cp .env.dev .env
# Edit .env and update the VM IP addresses for your setup
docker-compose up
```

## Service Communication

### Local Development URLs
- **Pilot API**: http://localhost:8001 (Health: /health)
- **MCP Server**: http://localhost:8002 (Health: /)  
- **Envoy Proxy**: http://localhost:8081 (FHIR endpoint)
- **Envoy Admin**: http://localhost:9901 (Health: /ready)

**Note**: The Pilot API container uses `host.docker.internal` to communicate with MCP Server and Envoy Proxy running on the host machine.

### Azure VM URLs (Update IPs as needed)
- **Pilot API**: http://********:8001
- **MCP Server**: http://********:8002
- **Envoy Proxy**: http://********:8081

## Health Checks

### Standardized Health Check Endpoints
- **Pilot API**: `/health` - Returns service status and database connectivity
- **MCP Server**: `/` - Returns MCP server status and capabilities  
- **Envoy Proxy**: `/ready` on admin port 9901 - Returns Envoy readiness status
- **Policy Check**: `/health` - Returns policy service status

### Docker Health Checks
All services now have proper Docker health checks configured:
- 30-second intervals
- 10-second timeouts  
- 3 retry attempts

## Environment Variables

### Pilot Repository (.env.local)
```bash
# Database (Local PostgreSQL)
DB_HOST=postgres
DB_PASSWORD=vitea123

# Service Communication (container-to-host)
MCP_SERVER_HOST=host.docker.internal:8002
ENVOY_FHIR_PROXY_URL=http://host.docker.internal:8081

# Azure OpenAI
AZURE_OPENAI_ENDPOINT=https://...
AZURE_OPENAI_KEY=...
```

### MCP Server (.env.local)
```bash
# Server Configuration
API_KEY=mvp_key_123
ENVOY_FHIR_PROXY_URL=http://localhost:8081  # MCP server runs on host
```

### Envoy Proxy (.env.local)
```bash
# Database (Separate PostgreSQL)
POSTGRES_USER=fgp_user
POSTGRES_PASSWORD=fgp_password

# FHIR Configuration
FHIR_UPSTREAM=https://hapi.fhir.org/baseR4
```

## Database Architecture

Each repository maintains its own PostgreSQL instance:
- **Pilot**: Port 5432 (vitea_db)
- **Envoy**: Port 5433 (fgp)

This maintains microservices independence while enabling communication via HTTP APIs.

## Running the System

### Option 1: Individual Services
Start each repository independently:
```bash
# Terminal 1 - Envoy Proxy
cd envoy_fhir_proxy/deploy/docker-compose
cp ../../.env.local .env
docker-compose up

# Terminal 2 - MCP Server  
cd vitea-mcp-server
cp .env.local .env
docker-compose up

# Terminal 3 - Pilot
cd pilot
cp .env.local .env  
docker-compose up
```

### Option 2: Orchestrated Startup
Use the enhanced start-all-services.sh script:
```bash
cd pilot
./start-all-services.sh
```

## Testing

### Health Check Testing
```bash
# Test all health endpoints
curl http://localhost:8001/health    # Pilot API
curl http://localhost:8002/         # MCP Server  
curl http://localhost:9901/ready     # Envoy Admin
curl http://localhost:8000/health    # Policy Check
```

### E2E Testing
```bash
cd pilot
python3 e2e-test-distributed.py
```

## Troubleshooting

### Common Issues

1. **Services can't communicate**: Verify .env file URLs match your deployment
2. **Health checks failing**: Check if services have started completely
3. **Database connection errors**: Ensure PostgreSQL containers are healthy
4. **Port conflicts**: Verify no other services are using the required ports

### Debugging Commands
```bash
# Check Docker container status
docker ps

# View container logs
docker logs vitea-api
docker logs vitea-mcp-server

# Test network connectivity
docker network ls
docker network inspect pilot_vitea-network
```

## Future Scaling

This configuration supports:
- **Container Orchestration**: Kubernetes/Docker Swarm
- **Service Mesh**: Istio for advanced networking  
- **Cloud Deployment**: Azure Container Apps, EKS
- **Load Balancing**: NGINX, Traefik for production routing

The environment variable approach ensures seamless migration across deployment environments.