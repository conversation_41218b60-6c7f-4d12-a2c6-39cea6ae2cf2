# Multi-stage build for React applications
FROM node:18-alpine as builder

# Set working directory
WORKDIR /app

# Build Admin UI
COPY admin-ui-project/package*.json ./admin-ui/
WORKDIR /app/admin-ui
RUN npm ci
COPY admin-ui-project/ .
RUN npm run build

# Build Frontend (Chatbot)
WORKDIR /app
COPY frontend-project/package*.json ./frontend/
WORKDIR /app/frontend
RUN npm ci
COPY frontend-project/ .
RUN npm run build

# Production stage - Nginx
FROM nginx:alpine

# Copy custom Nginx configuration
COPY <<EOF /etc/nginx/conf.d/default.conf
server {
    listen 80;
    server_name localhost;

    # Admin UI - served at /admin
    location /admin/ {
        alias /usr/share/nginx/html/admin/;
        try_files \$uri \$uri/ /admin/index.html;
        index index.html;
    }

    # Admin UI redirect
    location = /admin {
        return 301 /admin/;
    }

    # Admin UI static assets
    location ~ ^/admin/static/(.*)$ {
        alias /usr/share/nginx/html/admin/static/\$1;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Main chatbot interface - served at root
    location / {
        root /usr/share/nginx/html/frontend;
        try_files \$uri \$uri/ /index.html;
    }

    # API proxy
    location /api/ {
        proxy_pass http://vitea-api:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://vitea-api:8000/health;
    }
}
EOF

# Copy built applications
COPY --from=builder /app/admin-ui/build /usr/share/nginx/html/admin
COPY --from=builder /app/frontend/build /usr/share/nginx/html/frontend

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]