# Vitea.ai Policy Management System

## 🚀 Enhanced Policy Management System

This repository contains the enhanced policy management system for Vitea.ai, featuring advanced policy creation, validation, and management capabilities.

## 📋 Features

### 🎯 Core Features
- **Advanced Policy Creation**: Create, clone, and edit policies with real-time validation
- **Schema-Based Validation**: JSON schema enforcement for data integrity
- **Monaco Editor Integration**: Professional code editor with syntax highlighting and autocomplete
- **Real-time Validation**: Immediate feedback on form fields and JSON structure
- **Policy Cloning**: Clone existing policies with new names and descriptions
- **Audit Logging**: HIPAA-compliant audit trail for all operations
- **Enum Management**: Dynamic management of enum values through web interface

### 🔧 Technical Features
- **Multi-service Architecture**: Node.js/Express.js API, React Frontend, PostgreSQL database
- **Database-Driven Enums**: Store and manage enum values dynamically
- **Soft Delete**: Mark policies as deleted instead of permanent removal
- **Search and Pagination**: Advanced policy search with filtering and pagination
- **Admin Role Management**: Role-based access control for policy operations

## 🏗️ Architecture

### Services
- **Enhanced API** (`enhanced-api-project/`): Node.js/Express.js backend with policy management APIs
- **Admin UI** (`admin-ui-project/`): React-based admin interface for policy management
- **Frontend** (`frontend-project/`): Main application frontend
- **Database**: PostgreSQL with enhanced schema and functions

### Key Components
- **Policy Management**: Create, edit, clone, and delete policies
- **Schema Validation**: JSON schema-based validation system
- **Enum Management**: Dynamic enum value management
- **Audit System**: Comprehensive audit logging
- **Search & Filter**: Advanced policy search capabilities

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 17+
- npm or yarn

### Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd vitea-app-deployment
   ```

2. **Install dependencies**:
   ```bash
   # Install root dependencies
   npm install
   
   # Install API dependencies
   cd enhanced-api-project
   npm install
   
   # Install Admin UI dependencies
   cd ../admin-ui-project
   npm install
   ```

3. **Set up environment variables**:
   ```bash
   # Copy the example environment file and customize it
   cp .env.example .env
   
   # Edit .env with your specific values:
   # - Database credentials
   # - Azure OpenAI configuration
   # - Azure AD client credentials
   # - Service URLs
   ```

4. **Set up the database**:
   ```bash
   # Run database migrations
   psql -h your-db-host -U your-username -d your-database -f configs/enhanced-database-schema.sql
   psql -h your-db-host -U your-username -d your-database -f configs/20250716_06__policy_cloning_enhancements.sql
   psql -h your-db-host -U your-username -d your-database -f configs/20250716_07__enum_management.sql
   psql -h your-db-host -U your-username -d your-database -f configs/policy_templates.sql
   ```

4. **Configure environment variables**:
   ```bash
   # In enhanced-api-project/.env
   DB_HOST=your-db-host
   DB_USER=your-username
   DB_PASSWORD=your-password
   DB_NAME=your-database
   DB_PORT=5432
   ```

5. **Start the services**:
   ```bash
   # Start the API server
   cd enhanced-api-project
   npm start
   
   # Start the Admin UI (in a new terminal)
   cd admin-ui-project
   npm start
   ```

## 📚 Documentation

### Core Documentation
- **[Policy Templates](./configs/README_policy_templates.md)**: Comprehensive guide to policy templates and schemas
- **[API Documentation](./docs/API_DOCUMENTATION.md)**: Complete API reference
- **[Database Schema](./docs/DATABASE_SCHEMA.md)**: Database structure and functions
- **[Component Guide](./docs/COMPONENT_GUIDE.md)**: React component documentation
- **[Validation System](./docs/VALIDATION_SYSTEM.md)**: Schema validation and error handling

### Development Guides
- **[Contributing](./docs/CONTRIBUTING.md)**: Development guidelines and best practices
- **[Testing](./docs/TESTING.md)**: Testing strategies and examples
- **[Deployment](./docs/DEPLOYMENT.md)**: Deployment instructions and configuration

## 🔧 Configuration

### Environment Variables

#### API Server (`enhanced-api-project/.env`)
```env
DB_HOST=localhost
DB_USER=postgres
DB_PASSWORD=your-password
DB_NAME=vitea_db
DB_PORT=5432
NODE_ENV=development
PORT=8000
```

#### Admin UI (`admin-ui-project/.env`)
```env
REACT_APP_API_BASE_URL=http://localhost:8000
REACT_APP_ENVIRONMENT=development
```

### Database Configuration
- **Host**: PostgreSQL server hostname
- **Database**: Database name (default: `vitea_db`)
- **User**: Database username
- **Password**: Database password
- **SSL**: Required for Azure PostgreSQL

## 🎯 Usage

### Policy Management

1. **Create a New Policy**:
   - Click "+ New Policy" in the Admin UI
   - Select a category to load the appropriate template
   - Fill in the required fields
   - The JSON editor will provide real-time validation
   - Click "Create Policy" to save

2. **Clone an Existing Policy**:
   - Click "+ New Policy" → "Clone Existing Policy"
   - Select a policy from the searchable list
   - Modify the name and description
   - Edit the policy definition as needed
   - Click "Clone Policy" to save

3. **Edit a Policy**:
   - Click "Edit" on any policy in the list
   - Modify the policy definition
   - Real-time validation will guide your changes
   - Click "Update Policy" to save

### Enum Management

1. **Access Enum Management**:
   - Click "Manage Enums" in the Admin UI header
   - View existing enum categories and values

2. **Add New Enum Values**:
   - Select a category or create a new one
   - Add enum values with descriptions
   - Values will be available in autocomplete

## 🔍 API Endpoints

### Policy Management
```
GET    /api/v1/policies                    # List policies with search/filter
POST   /api/v1/policies                    # Create new policy
GET    /api/v1/policies/:id                # Get specific policy
PUT    /api/v1/policies/:id                # Update policy
DELETE /api/v1/policies/:id                # Soft delete policy
GET    /api/v1/policies/templates/:category # Get template (legacy)
GET    /api/v1/policies/types              # Get available policy types
```

### Enum Management
```
GET    /api/v1/enums/values/:policyType/:fieldPath    # Get enum values
GET    /api/v1/enums/fields/:policyType               # Get all enum fields
GET    /api/v1/enums/categories                       # Get enum categories
POST   /api/v1/enums/categories                       # Create enum category
POST   /api/v1/enums/categories/:categoryId/values    # Add enum value
PUT    /api/v1/enums/values/:valueId                  # Update enum value
DELETE /api/v1/enums/values/:valueId                  # Delete enum value
```

## 🧪 Testing

### API Testing
```bash
cd enhanced-api-project
npm test
```

### Frontend Testing
```bash
cd admin-ui-project
npm test
```

### Database Testing
```bash
# Test database connection
psql -h your-db-host -U your-username -d your-database -c "SELECT version();"

# Test policy functions
psql -h your-db-host -U your-username -d your-database -c "SELECT * FROM search_policies(NULL, NULL, NULL, NULL, 5, 0);"
```

## ⚙️ Environment Configuration

### Environment Files
The project uses environment-specific configuration files:

- **`.env.example`** - Template file with all required variables (committed to git)
- **`.env`** - Local development configuration (git-ignored)
- **`.env.dev`** - Development VM configuration (git-ignored)
- **`.env.production`** - Production configuration (git-ignored)

### Setup Environment Variables
```bash
# For local development
cp .env.example .env
# Edit .env with your local values

# For development VM deployment
cp .env.example .env.dev
# Edit .env.dev with VM-specific values

# For production deployment
cp .env.example .env.production
# Edit .env.production with production values
```

### Docker Compose Usage
```bash
# Local development (uses .env by default)
docker-compose up

# Development VM deployment
docker-compose --env-file .env.dev up

# Production deployment
docker-compose --env-file .env.production -f docker-compose.prod.yml up
```

### Important Notes
- **React variables** (`REACT_APP_*`) are build-time variables - they get baked into the JavaScript bundle
- **Backend variables** are runtime variables - they can be changed by restarting containers
- All `.env` files except `.env.example` are git-ignored for security

## 🚀 Deployment

### Development
```bash
# Start all services in development mode
npm run dev
```

### Production
```bash
# Build and start production services
npm run build
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature`
3. Make your changes
4. Add tests for new functionality
5. Commit your changes: `git commit -am 'Add feature'`
6. Push to the branch: `git push origin feature/your-feature`
7. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Check the [documentation](./docs/)
- Review the [API documentation](./docs/API_DOCUMENTATION.md)
- Open an issue on GitHub

## 🔄 Changelog

### v2.0.0 - Enhanced Policy Management
- ✨ Added schema-based validation system
- ✨ Integrated Monaco editor with autocomplete
- ✨ Implemented real-time form validation
- ✨ Added policy cloning functionality
- ✨ Enhanced audit logging system
- ✨ Added enum management system
- ✨ Improved search and pagination
- ✨ Added soft delete functionality

### v1.0.0 - Initial Release
- Basic policy management
- Simple JSON templates
- Basic CRUD operations
