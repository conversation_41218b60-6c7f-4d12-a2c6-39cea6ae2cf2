# SQL Files Deprecation Notice

## ⚠️ IMPORTANT: Schema Consolidation Complete

As of August 29, 2025, all SQL schema files have been consolidated into a single source of truth. Most SQL files in this project are now **DEPRECATED** and should not be used.

## ✅ ACTIVE FILES (Use These)

### Primary Schema File
```
scripts/00-create-complete-postgres-schema-consolidated-v2.sql
```
**This is the ONLY schema file you need for deployment.**

### Supporting Scripts (Still Active)
- `scripts/deploy-template-management-docker.sh` - Deployment automation
- `scripts/validate-template-deployment.sh` - Validation script  
- `scripts/rollback-template-deployment.sh` - Emergency rollback
- `scripts/test-template-management.sh` - Test suite

## ❌ DEPRECATED FILES (Do Not Use)

All files marked with `.deprecated` extension are no longer needed:

### `/scripts/` Directory (Deprecated)
- `00-create-complete-postgres-schema-consolidated.sql.deprecated` - Old V1 schema (replaced by V2)
- `migrate-templates-to-schemas.sql.deprecated` - One-time migration (already applied)
- `migrate-templates-to-schemas-fixed.sql.deprecated` - One-time migration (already applied)
- `load-hipaa-sample-data-fixed.sql.deprecated` - Old data loading script
- `create-complete-schema.sql.deprecated` - Old partial schema
- `hipaa-sample-data*.sql.deprecated` - Old sample data files
- `testing-observability-sample-data.sql.deprecated` - Old test data

### `/configs/` Directory (All Deprecated)
All SQL files in `/configs/` are marked `.deprecated`:
- `database-schema.sql.deprecated`
- `enhanced-database-schema.sql.deprecated`
- `20250716_*.sql.deprecated` - Old migration files
- `20250806_*.sql.deprecated` - Old migration files
- `20250811_*.sql.deprecated` - Old migration files
- `20250812_*.sql.deprecated` - Old migration files
- `fix_search_policies_function.sql.deprecated`

### `/docs/` Directory (Deprecated)
- `vitea_tables_ddl.sql.deprecated` - Old DDL documentation

## 📋 Migration Guide

### For New Deployments
Use only the V2 consolidated schema:
```bash
psql -h localhost -U dbadmin -d vitea_db -f scripts/00-create-complete-postgres-schema-consolidated-v2.sql
```

### For Existing Deployments
Your database has already been migrated. No action needed.

### For Developers
- **DO NOT** modify any `.deprecated` files
- **DO NOT** create new migration files
- **DO** make all schema changes in `00-create-complete-postgres-schema-consolidated-v2.sql`
- **DO** document changes in the file header comments

## 🗂️ What's Included in V2 Schema

The consolidated V2 schema (`00-create-complete-postgres-schema-consolidated-v2.sql`) includes:

1. **All Tables (34 total)**
   - Core tables (users, policies, audit_log, etc.)
   - Policy management (policy_schemas with templates, policy_groups)
   - Agent management (agents, agent_groups, agent_roles)
   - Role management (roles, privileges, user_roles)
   - Integration tables (external_integrations, webhooks, events)
   - Enum management (enum_categories, enum_values)
   - Testing tables (testing_sessions, evaluation_results)

2. **All Functions (17+ total)**
   - `generate_default_template()` - Template auto-generation
   - `search_policies()` - Policy search with filters
   - `get_agent_effective_policies()` - Agent policy resolution
   - `clone_policy()` - Policy cloning
   - `get_role_hierarchy()` - Role hierarchy traversal
   - `check_webhook_delivery_status()` - Webhook monitoring
   - And more...

3. **All Triggers (14+ total)**
   - `auto_generate_template` - Auto-generate templates on schema changes
   - `update_*_updated_at` - Timestamp updates for all tables
   - And more...

4. **All Indexes (40+ total)**
   - Performance indexes for all foreign keys
   - Template-specific indexes
   - Search optimization indexes

5. **All Enums (6 total)**
   - `access_level_enum`
   - `severity_level_enum`
   - `lifecycle_status_enum`
   - `link_type_enum`
   - `user_status_enum`
   - `agent_status_enum`

## 🔄 Template Management Integration

The V2 schema fully integrates the template management system:
- Templates stored in `policy_schemas.default_template`
- Auto-generation via `generate_default_template()` function
- Template source tracking (`auto_generated`, `manual_override`, etc.)
- Automatic triggers for template generation
- No more `policy_templates` table (removed)

## ⏰ Retention Policy

Deprecated files will be retained for:
- **3 months** for reference and rollback purposes
- **After 3 months** (November 29, 2025), all `.deprecated` files can be safely deleted

## 🚨 Action Required

### For DevOps Team
1. Update all deployment scripts to use V2 schema
2. Remove references to deprecated files from CI/CD pipelines
3. Update documentation to reference V2 schema only

### For Development Team  
1. Stop using any `.deprecated` files immediately
2. Make all future schema changes in V2 file
3. Update local development setup to use V2 schema

### For Database Administrators
1. Ensure all environments are using V2 schema
2. Verify template management system is working
3. Plan to archive `.deprecated` files after retention period

## 📝 Version History

- **V1** (deprecated): Original scattered migration files
- **V2** (current): Fully consolidated schema with template management

## ❓ Questions or Issues

If you need to reference deprecated files:
1. Check this deprecation notice first
2. Look for the equivalent in V2 schema
3. Contact the development team if unclear

---

**Deprecation Date:** August 29, 2025  
**Retention Until:** November 29, 2025  
**Status:** All non-V2 SQL files are DEPRECATED