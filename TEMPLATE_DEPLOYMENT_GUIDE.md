# Template Management System - Deployment Guide

## Quick Deployment

For automated deployment with all safety checks:

```bash
# Run automated deployment script
./scripts/deploy-template-management.sh

# Validate deployment
./scripts/validate-template-deployment.sh
```

## Manual Deployment Steps

### 1. Pre-Deployment Checklist

- [ ] All services running (`docker-compose ps`)
- [ ] Database accessible (`psql -U dbadmin -d vitea_db`)
- [ ] API health check passing (`curl http://localhost:8001/health`)
- [ ] Recent backup exists (`ls -la backups/`)

### 2. Database Migration

```bash
# Create backup
docker exec pilot-postgres pg_dump -U dbadmin -d vitea_db > backups/pre_migration_$(date +%Y%m%d).sql

# Apply migration
docker exec pilot-postgres psql -U dbadmin -d vitea_db -f /scripts/migrate-templates-to-schemas.sql

# Verify migration
docker exec pilot-postgres psql -U dbadmin -d vitea_db -c "
    SELECT schema_name, template_source, default_template IS NOT NULL as has_template
    FROM policy_schemas WHERE is_active = true;"
```

### 3. Service Deployment

```bash
# Restart API to load new code
docker-compose restart pilot-api

# Monitor logs
docker logs -f pilot-api | grep -i template
```

### 4. Validation

```bash
# Run validation suite
./scripts/validate-template-deployment.sh

# Manual testing
./scripts/test-template-management.sh
```

## Rollback Procedure

If issues occur during or after deployment:

```bash
# Automated rollback
./scripts/rollback-template-deployment.sh

# Follow prompts to select backup and complete rollback
```

## Deployment Scripts

### deploy-template-management.sh
- **Purpose**: Automated deployment with safety checks
- **Features**:
  - Pre-flight database and API checks
  - Automatic backup creation
  - Migration execution with progress tracking
  - API endpoint testing
  - Performance validation
  - Comprehensive deployment report

### validate-template-deployment.sh
- **Purpose**: Post-deployment validation
- **Checks**:
  - Database structure modifications
  - Data integrity and template population
  - API endpoint functionality
  - Performance benchmarks
  - Security controls
  - Audit trail logging

### rollback-template-deployment.sh
- **Purpose**: Emergency rollback procedure
- **Features**:
  - Service shutdown for safety
  - Database restore from backup
  - Service restart
  - Rollback verification
  - Code rollback instructions

### test-template-management.sh
- **Purpose**: Comprehensive functionality testing
- **Tests**:
  - Template retrieval
  - Manual override
  - Auto-generation
  - Cache behavior
  - API compatibility
  - Performance metrics

## Environment Variables

Required for deployment scripts:

```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=vitea_db
export DB_USER=dbadmin
export DB_PASSWORD=your_password
export API_HOST=localhost
export API_PORT=8001
```

## Monitoring Post-Deployment

### Health Checks

```bash
# API health
curl http://localhost:8001/health

# Template status
curl -H "Authorization: Bearer admin-token" \
  http://localhost:8001/api/v1/schemas/templates/status

# Database status
docker exec pilot-postgres psql -U dbadmin -d vitea_db -c "
  SELECT COUNT(*) as total_schemas,
         COUNT(default_template) as templates_configured
  FROM policy_schemas WHERE is_active = true;"
```

### Log Monitoring

```bash
# API logs for template operations
docker logs pilot-api 2>&1 | grep -i template | tail -20

# Database logs
docker logs pilot-postgres 2>&1 | tail -50

# Audit trail
docker exec pilot-postgres psql -U dbadmin -d vitea_db -c "
  SELECT * FROM audit_log 
  WHERE action LIKE '%TEMPLATE%' 
  ORDER BY created_at DESC LIMIT 10;"
```

## Troubleshooting

### Common Issues

#### Templates Not Generated
```bash
# Manually trigger generation
curl -X POST http://localhost:8001/api/v1/schemas/regenerate-templates \
  -H "Authorization: Bearer admin-token"
```

#### API Not Responding
```bash
# Check container status
docker ps -a | grep pilot-api

# Restart if needed
docker-compose restart pilot-api

# Check logs for errors
docker logs pilot-api --tail 100
```

#### Database Connection Issues
```bash
# Test connection
docker exec pilot-postgres psql -U dbadmin -d vitea_db -c "SELECT 1"

# Check PostgreSQL status
docker exec pilot-postgres pg_isready

# Restart if needed
docker-compose restart pilot-postgres
```

### Performance Issues

```bash
# Check cache status
curl http://localhost:8001/api/v1/schemas/templates/cache-stats \
  -H "Authorization: Bearer admin-token"

# Analyze slow queries
docker exec pilot-postgres psql -U dbadmin -d vitea_db -c "
  SELECT query, calls, mean_exec_time 
  FROM pg_stat_statements 
  WHERE query LIKE '%template%' 
  ORDER BY mean_exec_time DESC LIMIT 10;"
```

## Success Criteria

Deployment is successful when:

1. ✅ All validation checks pass
2. ✅ Templates auto-generate for new schemas
3. ✅ Manual overrides are preserved
4. ✅ API response times < 100ms (cached)
5. ✅ No errors in production logs
6. ✅ Policy creation works in UI
7. ✅ Audit trail captures all operations

## Support

For deployment issues:

1. Check deployment logs: `deployment_*.log`
2. Run validation: `./scripts/validate-template-deployment.sh`
3. Review this guide and troubleshooting section
4. Check system documentation: `docs/TEMPLATE_MANAGEMENT_SYSTEM.md`

## Next Steps After Deployment

1. **Monitor for 24-48 hours** for stability
2. **Review performance metrics** daily
3. **After 1-2 weeks**, remove hardcoded templates:
   - Update `templateGenerationService.js`
   - Remove fallback code from `schemaValidator.js`
4. **After verification**, drop old table:
   ```sql
   DROP TABLE IF EXISTS policy_templates CASCADE;
   ```

---

**Last Updated:** August 29, 2025
**Version:** 1.0.0
**Status:** Ready for Production Deployment