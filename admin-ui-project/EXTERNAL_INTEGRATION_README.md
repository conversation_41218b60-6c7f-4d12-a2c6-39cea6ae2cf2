# External System Integration - Development Setup

## Quick Setup (Frontend Approach)

This implementation provides **immediate external system integration** with **full debugging visibility** during development.

### 1. Environment Configuration

Create or update your `.env.development` file in `admin-ui-project/`:

```bash
# External System Integration - Development Configuration
REACT_APP_EXTERNAL_SYSTEM_BASE_URL=https://your-hgcs-domain.com
REACT_APP_EXTERNAL_SYSTEM_TOKEN=YOUR_TOKEN

# Enable debug mode for curl command display
REACT_APP_EXTERNAL_INTEGRATION_DEBUG=true

# External API Endpoints (defaults provided)
REACT_APP_EXTERNAL_POLICY_UPDATE_ENDPOINT=/api/v1/policy/update_policy
REACT_APP_EXTERNAL_AGENT_POLICY_UPDATE_ENDPOINT=/api/v1/policy/update_agent_policy

# Internal API (your existing setup)
REACT_APP_API_BASE_URL=http://localhost:8000/api/v1
```

### 2. Integration Points

The system integrates at **3 key points**:

#### A. Global Policy Toggle (Policy Configuration Page)
- **When**: User toggles policy active/inactive
- **External API**: `PUT /api/v1/policy/update_policy`
- **Impact**: Global policy change affecting ALL agents

#### B. Agent Policy Deletion (Agent Registry Page)  
- **When**: User deletes policy from specific agent
- **External API**: `PUT /api/v1/policy/update_agent_policy`
- **Impact**: Agent-specific policy removal

#### C. Agent Status Toggle (Agent Registry Page)
- **When**: User activates/deactivates agent
- **External API**: `PUT /api/v1/policy/update_agent_policy`
- **Impact**: All policies for that agent

### 3. What You'll See During Development

#### Confirmation Dialog
```
┌─────────────────────────────────────────┐
│ Global Policy Update              [DEV] │
├─────────────────────────────────────────┤
│ Deactivate "DRG - Policy 1" globally?  │
│ This will affect ALL agents with this  │
│ policy.                                 │
│                                         │
│ ℹ️  This will trigger an external       │
│    system integration call. The curl   │
│    command and response will be shown  │
│    below for debugging.                 │
│                                         │
│ [Cancel] [Confirm & Send to External]  │
└─────────────────────────────────────────┘
```

#### During Request
```
⚡ Sending request to external system...
```

#### Success with Debug Info
```
✅ External system updated successfully (200)

🔧 Debug Information
📤 Request Payload:
{
  "policy_id": "5ce919b4-7fb2-4ecf-af56-ac103f3701d6",
  "is_active": false
}

💻 Curl Command:
curl -X PUT "https://your-hgcs-domain.com/api/v1/policy/update_policy" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
  "policy_id": "5ce919b4-7fb2-4ecf-af56-ac103f3701d6",
  "is_active": false
}'

📥 Response: [200 OK]
{
  "success": true,
  "message": "Successfully updated policy",
  "policy_id": "5ce919b4-7fb2-4ecf-af56-ac103f3701d6"
}
```

### 4. Implementation in Your Components

#### Option A: Replace Existing Components
```javascript
// In your PolicyConfiguration component file
import PolicyConfigurationWithIntegration from './examples/PolicyConfigurationWithIntegration';
export default PolicyConfigurationWithIntegration;
```

#### Option B: Integrate into Existing Components
```javascript
// Add to your existing component
import useExternalIntegration from './hooks/useExternalIntegration';
import ExternalIntegrationDialog from './components/ExternalIntegrationDialog';

const YourExistingComponent = () => {
  const { dialogState, closeDialog, confirmOperation, handlePolicyToggle } = useExternalIntegration();
  
  const onPolicyToggle = (policy) => {
    handlePolicyToggle(
      policy.policy_id,
      !policy.is_active, 
      policy.name,
      async () => {
        // Your existing Vitea update logic here
        await updatePolicyInVitea(policy.policy_id, !policy.is_active);
      }
    );
  };
  
  return (
    <div>
      {/* Your existing UI */}
      <ExternalIntegrationDialog
        open={dialogState.open}
        onClose={closeDialog}
        onConfirm={confirmOperation}
        title={dialogState.title}
        message={dialogState.message}
        isLoading={dialogState.isLoading}
        integrationResult={dialogState.integrationResult}
      />
    </div>
  );
};
```

### 5. Testing the Integration

1. **Start your services**:
   ```bash
   # Terminal 1: Start enhanced API
   cd enhanced-api-project
   npm start  # localhost:8000
   
   # Terminal 2: Start admin UI
   cd admin-ui-project  
   npm start  # localhost:3002
   ```

2. **Update environment variables** with your external system details

3. **Test scenarios**:
   - Toggle a policy on Policy Configuration page
   - Delete a policy from an agent on Agent Registry 
   - Toggle agent active status

4. **Check browser console** for detailed integration logs

### 6. Expected External API Calls

#### Global Policy Update
```bash
curl -X PUT "https://your-hgcs-domain.com/api/v1/policy/update_policy" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "policy_id": "5ce919b4-7fb2-4ecf-af56-ac103f3701d6",
    "is_active": false
  }'
```

#### Agent Policy Update (After Deletion)
```bash
curl -X PUT "https://your-hgcs-domain.com/api/v1/policy/update_agent_policy" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "agent_id": "89c0d4ee-b4f0-4494-a8fc-3fe6e79de729",
    "agent_active": true,
    "policy_ids": [
      "597df3ba-ae0d-4e38-ba40-aab0f84beb88",
      "1cad0241-8fff-4fa2-aa2c-bed60f28d67f"
    ]
  }'
```

#### Agent Deactivation
```bash
curl -X PUT "https://your-hgcs-domain.com/api/v1/policy/update_agent_policy" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "agent_id": "89c0d4ee-b4f0-4494-a8fc-3fe6e79de729",
    "agent_active": false,
    "policy_ids": []
  }'
```

### 7. Development Benefits

✅ **Immediate Testing**: See exact curl commands and responses  
✅ **No Backend Changes**: Pure frontend implementation  
✅ **Full Debugging**: Complete request/response visibility  
✅ **Easy Configuration**: Environment variable based  
✅ **Throw-away Code**: Easy to replace with production implementation  

### 8. Production Migration Path

When ready for production:
1. Move external calls to backend dispatcher
2. Remove debug displays
3. Add proper error handling and retries
4. Implement webhook-based notifications
5. Add audit logging

This development approach gives you **immediate external system integration** with **complete visibility** into what's happening!