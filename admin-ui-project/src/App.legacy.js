import React, { useState, useEffect } from 'react';
import {
  AlertTriangle,
  Shield,
  Users,
  Activity,
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  XCircle,
  Loader2,
  Clock,
  FileText,
  ClipboardCheck,
  Settings,
  X,
  Menu,
  ChevronDown,
  Home
} from 'lucide-react';
import EnhancedPolicyModal from './EnhancedPolicyModal';
import { Link } from 'react-router-dom';
import MultiSelectDropdown from './components/MultiSelectDropdown';
import AssignmentTypeBadge from './components/AssignmentTypeBadge';
import { listAgents } from './api/agents';
import { listPolicyGroups } from './api/policyGroups';
import { isPolicyGroupFeatureEnabled } from './utils/featureFlags';
import EnumManagement from './components/EnumManagement';

// Get API base URL safely - use nginx routing
const getApiBaseUrl = () => {
  // Use relative URLs to allow nginx to handle routing
  return `${window.location.protocol}//${window.location.host}`;
};

const getProxyApiBaseUrl = () => {
  // Use relative URLs to allow nginx to handle routing
  return `${window.location.protocol}//${window.location.host}`;
};

const API_BASE_URL = getApiBaseUrl();

console.log('API_BASE_URL:', API_BASE_URL);

function ToggleSwitch({ isActive, onToggle, isLoading }) {
  if (isLoading) {
    return <Loader2 className="animate-spin text-gray-500 mx-auto" />;
  }

  return (
    <label className="relative inline-flex items-center cursor-pointer">
      <input
        type="checkbox"
        checked={isActive}
        onChange={onToggle}
        className="sr-only peer"
        disabled={isLoading}
      />
      <div className="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-500"></div>
    </label>
  );
}

function PolicyAdminDashboard() {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [govOpen, setGovOpen] = useState(true);
  const [policies, setPolicies] = useState([]);
  const [violations, setViolations] = useState([]);
  const [metrics, setMetrics] = useState({});
  const [loading, setLoading] = useState(true);
  const [selectedPolicy, setSelectedPolicy] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedPolicyForEdit, setSelectedPolicyForEdit] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showEnumManagement, setShowEnumManagement] = useState(false);
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [loadingStatusId, setLoadingStatusId] = useState(null);

  // Agents & Groups filter state
  const [agents, setAgents] = useState([]);
  const [groups, setGroups] = useState([]);
  const [selectedAgents, setSelectedAgents] = useState([]);
  const [selectedGroups, setSelectedGroups] = useState([]);

  useEffect(() => {
    fetchData();
  }, []);

  // Fetch agents and policy groups lists for dropdowns
  useEffect(() => {
    async function loadFilters() {
      try {
        const [agentsData, groupsData] = await Promise.all([
          listAgents(),
          listPolicyGroups('all'),
        ]);
        setAgents(agentsData);
        setGroups(groupsData);
      } catch (err) {
        console.error('Failed to load agents or policy groups', err);
      }
    }
    loadFilters();
  }, []);

  useEffect(() => {
    // Keep dashboard stats in sync with local data without needing to re-fetch
    setMetrics(prev => ({
      ...prev,
      current_stats: {
        ...(prev.current_stats || {}),
        total_policies: policies.length,
        active_policies: policies.filter(p => p && p.is_active).length,
        critical_policies: policies.filter(p => p && p.severity === 'critical').length,
        total_violations: violations.length
      }
    }));
  }, [policies, violations]);

  // Re-fetch policies whenever selected agents or groups change
  useEffect(() => {
    fetchData();
  }, [selectedAgents, selectedGroups]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (selectedAgents.length > 0) params.append('agentId', selectedAgents[0]);
      if (selectedGroups.length > 0) params.append('groupId', selectedGroups[0]);
      const policiesUrl = `${API_BASE_URL}/api/v1/policies${params.toString() ? `?${params.toString()}` : ''}`;

      const [policiesRes, violationsRes, metricsRes] = await Promise.all([
        fetch(policiesUrl, {
          headers: { 'Authorization': 'Bearer admin-token' }
        }),
        fetch(`${API_BASE_URL}/api/v1/policies/violations?limit=100`, {
          headers: { 'Authorization': 'Bearer admin-token' }
        }),
        fetch(`${API_BASE_URL}/api/v1/metrics`, {
          headers: { 'Authorization': 'Bearer admin-token' }
        })
      ]);

      if (policiesRes.ok) {
        const policiesData = await policiesRes.json();
        setPolicies(policiesData.policies || policiesData);
      }
      if (violationsRes.ok) setViolations(await violationsRes.json());
      if (metricsRes.ok) setMetrics(await metricsRes.json());
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (policyId) => {
    if (window.confirm('Are you sure you want to delete this policy?')) {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/policies/${policyId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': 'Bearer admin-token'
          }
        });

        if (response.ok) {
          fetchData(); // Refresh data after successful deletion
        } else {
          const errorData = await response.json();
          console.error('Failed to delete policy:', errorData);
          alert(`Error: ${errorData.error}`);
        }
      } catch (error) {
        console.error('Error deleting policy:', error);
        alert('An unexpected error occurred while deleting the policy.');
      }
    }
  };

  const handleSaveNewPolicy = async () => {
    try {
      // The EnhancedPolicyModal already performs the API call for both create and update.
      // Simply refresh the table data and close the modal.
      fetchData(); // Refresh data
      setShowCreateModal(false);
      setSelectedPolicyForEdit(null);
    } catch (error) {
      console.error('Error saving policy:', error);
      alert('An unexpected error occurred while saving the policy.');
    }
  };

  const handleUpdatePolicy = async (updatedPolicy) => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/api/v1/policies/${updatedPolicy.policy_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token',
        },
        body: JSON.stringify(updatedPolicy),
      });

      if (response.ok) {
        setSelectedPolicy(null); // Close modal on success
        fetchData(); // Refresh data
      } else {
        const errorData = await response.json();
        console.error('Failed to update policy:', errorData);
        alert(`Error: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error updating policy:', error);
      alert('An unexpected error occurred while updating the policy.');
    }
  };

  const handleEditPolicy = (policy) => {
    setSelectedPolicyForEdit(policy);
    setShowCreateModal(true);
  };

  const handleToggleStatus = async (policy) => {
    // Skip if policy is undefined or null
    if (!policy) return;
    
    // Parse the current definition to update the enabled field
    let updatedDefinition = policy.definition;
    try {
      if (typeof policy.definition === 'string') {
        updatedDefinition = JSON.parse(policy.definition);
      } else {
        updatedDefinition = { ...policy.definition };
      }
      // Update the enabled field to match the new is_active status
      updatedDefinition.enabled = !policy.is_active;
    } catch (error) {
      console.error('Error parsing policy definition:', error);
      // Fallback to original definition if parsing fails
      updatedDefinition = policy.definition;
    }
    
    const updatedPolicy = { 
      ...policy, 
      is_active: !policy.is_active,
      definition: updatedDefinition
    };
    setLoadingStatusId(policy.policy_id);

    // Load feature flag mapping
    let featureFlag = null;
    try {
      // Dynamically import config from local src directory
      featureFlag = (await import('./featureFlags.json')).default.find(
        (f) => f.policy_id === String(policy.policy_id) || f.policy_name === policy.name
      );
      console.log('Loaded featureFlag mapping:', featureFlag);
    } catch (e) {
      // Fallback: no mapping
      featureFlag = null;
      console.error('Error loading featureFlag mapping:', e);
    }

    let policyUpdateSuccess = false;
    let featureFlagUpdateSuccess = false;
    let errorMsg = '';

    try {
      // 1. Update policy status
      console.log('Toggling policy:', policy);
      const response = await fetch(`${getApiBaseUrl()}/api/v1/policies/${policy.policy_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(updatedPolicy)
      });
      console.log('Policy update response:', response);
      if (response.ok) {
        policyUpdateSuccess = true;
        const updated = await response.json();
        setPolicies((prev) =>
          prev.map((p) => (p.policy_id === updated.policy_id ? updated : p))
        );
      } else {
        errorMsg = 'Failed to update policy status.';
        console.error(errorMsg);
      }
    } catch (err) {
      errorMsg = 'Error toggling policy status.';
      console.error(errorMsg, err);
    }

    // 2. Update feature flag if mapped and not placeholder
    if (
      featureFlag &&
      featureFlag.feature_flag_endpoint !== 'placeholder' &&
      featureFlag.feature_flag_name !== 'placeholder'
    ) {
      try {
        const flagValue = updatedPolicy.is_active ? 'true' : 'false';
        const description = `${policy.name} - ${updatedPolicy.is_active ? 'Enabled' : 'Disabled'}`;
        const flagResponse = await fetch(`${getProxyApiBaseUrl()}${featureFlag.feature_flag_endpoint}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer admin-secret-key-123'
          },
          body: JSON.stringify({ flag_value: flagValue, description })
        });
        console.log('Feature flag update response:', flagResponse);
        if (flagResponse.ok) {
          featureFlagUpdateSuccess = true;
        } else {
          errorMsg = 'Failed to update feature flag.';
          console.error(errorMsg);
        }
      } catch (err) {
        errorMsg = 'Error updating feature flag.';
        console.error(errorMsg, err);
      }
    } else {
      // No feature flag mapping, treat as success
      featureFlagUpdateSuccess = true;
      console.log('No feature flag mapping, skipping feature flag update.');
    }

    // 3. Finalize UI state
    if (policyUpdateSuccess && featureFlagUpdateSuccess) {
      // Success: nothing to do, UI already updated
      console.log('Policy and feature flag update succeeded.');
    } else {
      // Failure: revert UI state and show error
      alert(errorMsg || 'Failed to update policy or feature flag.');
      // Optionally, re-fetch data to restore correct state
      fetchData();
      console.error('Policy or feature flag update failed:', errorMsg);
    }
    setLoadingStatusId(null);
  };

  const filteredPolicies = policies.filter(policy => {
    // Skip undefined or null policies
    if (!policy) return false;
    
    const matchesSearch = policy.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         policy.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterCategory === 'all' || policy.category === filterCategory;
    const matchesStatus = filterStatus === 'all' ||
      (filterStatus === 'active' && policy.is_active) ||
      (filterStatus === 'inactive' && !policy.is_active);

    return matchesSearch && matchesCategory && matchesStatus;
  });

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'content_safety': return 'bg-blue-100 text-blue-800';
      case 'data_masking': return 'bg-purple-100 text-purple-800';
      case 'access_control': return 'bg-green-100 text-green-800';
      case 'medical_privacy': return 'bg-pink-100 text-pink-800';
      case 'compliance': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };


  // Helper for status badges (moved outside JSX)
  const getStatusBadge = (status, type) => {
    let color = 'bg-gray-100 text-gray-800';
    if (type === 'rego') {
      if (status === 'generated') color = 'bg-green-100 text-green-800';
      else if (status === 'pending') color = 'bg-yellow-100 text-yellow-800';
      else if (status === 'failed') color = 'bg-red-100 text-red-800';
    } else if (type === 'blob') {
      if (status === 'uploaded') color = 'bg-blue-100 text-blue-800';
      else if (status === 'deleted') color = 'bg-gray-200 text-gray-600';
    }
    return <span className={`inline-block px-2 py-1 rounded-full text-xs font-semibold ${color}`}>{status}</span>;
  };

  // Action handler stubs
  const [generatingRegoId, setGeneratingRegoId] = useState(null);
  const handleGenerateRego = async (policyId) => {
    setGeneratingRegoId(policyId);
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/policies/${policyId}/generate-rego`, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer admin-token',
          'Content-Type': 'application/json'
        }
      });
      if (response.ok) {
        // Optionally show success message
        fetchData(); // Refresh policies to update status
      } else {
        const errorData = await response.json();
        alert(`Failed to generate Rego: ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      alert('An unexpected error occurred while generating Rego.');
      console.error(error);
    } finally {
      setGeneratingRegoId(null);
    }
  };
  const handleUploadRego = (policyId) => {
    // Fetch the generated rego code from backend, then upload it
    fetch(`${API_BASE_URL}/api/v1/policies/${policyId}/rego`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer admin-token'
      }
    })
      .then(async (response) => {
        if (!response.ok) {
          const errorText = await response.text();
          alert(`Failed to fetch generated rego: ${errorText || response.statusText}`);
          return;
        }
        const data = await response.json();
        const regoCode = data.regoCode || data.rego_code || data.code;
        if (!regoCode) {
          alert('No generated rego code found for this policy.');
          return;
        }
        // Now upload the rego code
        fetch(`${API_BASE_URL}/api/v1/policies/${policyId}/upload-blob`, {
          method: 'POST',
          headers: {
            'Authorization': 'Bearer admin-token',
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ regoCode })
        })
          .then(async (uploadResponse) => {
            const result = await uploadResponse.json();
            if (uploadResponse.ok && result.success) {
              alert('Rego uploaded successfully!');
              fetchData(); // Refresh policies to update status
            } else {
              alert(`Failed to upload rego: ${result.error || 'Unknown error'}`);
            }
          })
          .catch((err) => {
            alert('Error uploading rego: ' + err.message);
          });
      })
      .catch((err) => {
        alert('Error fetching generated rego: ' + err.message);
      });
  };
  const handleDownloadRego = (policyId) => {
    // Download rego file from backend and trigger browser download
    fetch(`${API_BASE_URL}/api/v1/policies/${policyId}/blob`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer admin-token'
      }
    })
      .then(async (response) => {
        if (!response.ok) {
          const errorText = await response.text();
          alert(`Failed to download rego: ${errorText || response.statusText}`);
          return;
        }
        // Try to get filename from Content-Disposition header
        const disposition = response.headers.get('Content-Disposition');
        let filename = `policy_${policyId}.rego`;
        if (disposition && disposition.includes('filename=')) {
          filename = disposition.split('filename=')[1].replace(/"/g, '').trim();
        }
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
      })
      .catch((err) => {
        alert('Error downloading rego: ' + err.message);
      });
  };
  const handleDeleteRego = (policyId) => {
    if (!window.confirm(`Are you sure you want to delete the rego file for policy ${policyId}?`)) return;
    fetch(`${API_BASE_URL}/api/v1/policies/${policyId}/blob`, {
      method: 'DELETE',
      headers: {
        'Authorization': 'Bearer admin-token'
      }
    })
      .then(async (response) => {
        if (response.ok) {
          alert('Rego file deleted successfully!');
          fetchData(); // Refresh policies to update status
        } else {
          const errorText = await response.text();
          alert(`Failed to delete rego: ${errorText || response.statusText}`);
        }
      })
      .catch((err) => {
        alert('Error deleting rego: ' + err.message);
      });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-lg text-gray-600">Loading Policy Dashboard...</div>
      </div>
    );
  }

  // Handler for Bundle Rego button
  const handleBundleRego = () => {
    setLoading(true);
    fetch(`${API_BASE_URL}/api/v1/policies/bundle-rego`, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer admin-token'
      }
    })
      .then(async (response) => {
        setLoading(false);
        if (!response.ok) {
          const errorText = await response.text();
          alert(`Failed to bundle rego files: ${errorText || response.statusText}`);
          return;
        }
        // Get filename from Content-Disposition header
        const disposition = response.headers.get('Content-Disposition');
        let filename = 'rego_bundle.tar.gz';
        if (disposition && disposition.includes('filename=')) {
          filename = disposition.split('filename=')[1].replace(/"/g, '').trim();
        }
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
      })
      .catch((err) => {
        setLoading(false);
        alert('Error bundling rego files: ' + err.message);
      });
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b dark:border-gray-700">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none"
                onClick={() => setSidebarOpen(!sidebarOpen)}
              >
                <Menu className="h-6 w-6 text-gray-700 dark:text-gray-300" />
              </button>
              <Shield className="h-8 w-8 text-blue-600" />
              <div className="hidden sm:block">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Vitea Governance</h1>
                <p className="text-gray-600 dark:text-gray-300">Enterprise Platform</p>
              </div>
              {/* Breadcrumb */}
              <nav className="ml-6 flex items-center text-sm text-gray-500 dark:text-gray-400 space-x-1">
                <Link to="/" className="hover:text-blue-600">
                  <Home size={14} />
                </Link>
                <span className="mx-1">›</span>
                <span className="flex items-center space-x-1">
                  <Shield size={12} />
                  <span>Governance & Policies</span>
                </span>
                <span className="mx-1">›</span>
                <span className="font-medium text-gray-700 dark:text-gray-200">Policy Configuration</span>
              </nav>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-green-700 transition-colors"
              >
                <Plus className="h-4 w-4" />
                <span>New Policy</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <div className={`transition-transform duration-200 ${sidebarOpen ? 'translate-x-0 w-64' : '-translate-x-48 w-16'} bg-white dark:bg-gray-800 shadow-sm h-screen border-r dark:border-gray-700`}> 
          <nav className={`${sidebarOpen ? 'p-4' : 'p-2'} space-y-2`}>
            {/* Governance & Policies Section */}
            <button
              className={`w-full py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg focus:outline-none ${sidebarOpen ? 'px-3 justify-between' : 'justify-center'}`}
              onClick={() => setGovOpen(!govOpen)}
            >
              <span className={`flex items-center ${sidebarOpen ? 'space-x-2' : ''}`}>
                <Shield className="h-4 w-4" />
                {sidebarOpen && <span className="font-medium">Governance & Policies</span>}
              </span>
              {sidebarOpen && (
                <ChevronDown className={`h-4 w-4 transform transition-transform ${govOpen ? '' : '-rotate-90'}`} />
              )}
            </button>

            {govOpen && (
              <div className={`${sidebarOpen ? 'ml-6' : 'ml-0'} space-y-1`}>
                <Link
                  to="/"
                  className={`${sidebarOpen ? 'px-3 justify-start space-x-2' : 'p-2 justify-center'} py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg`}
                >
                  <Shield className="h-4 w-4" />
                  {sidebarOpen && <span>Policy Configuration</span>}
                </Link>
                {isPolicyGroupFeatureEnabled() && (
                  <Link
                    to="/policy-groups"
                    className={`${sidebarOpen ? 'px-3 justify-start space-x-2' : 'p-2 justify-center'} py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg`}
                  >
                    <Users className="h-4 w-4" />
                    {sidebarOpen && <span>Policy Groups</span>}
                  </Link>
                )}
              </div>
            )}
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {/* Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">Total Policies</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{metrics.current_stats?.total_policies || policies.length}</p>
                </div>
                <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                  <Shield className="h-5 w-5 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">Active Policies</p>
                  <p className="text-2xl font-bold text-green-600">{metrics.current_stats?.active_policies || policies.filter(p => p && p.is_active).length}</p>
                </div>
                <div className="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">Critical Policies</p>
                  <p className="text-2xl font-bold text-red-600">{metrics.current_stats?.critical_policies || policies.filter(p => p.severity === 'critical').length}</p>
                </div>
                <div className="h-10 w-10 bg-red-100 rounded-lg flex items-center justify-center">
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">Total Violations</p>
                  <p className="text-2xl font-bold text-orange-600">{metrics.current_stats?.total_violations || violations.length}</p>
                </div>
                <div className="h-10 w-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Activity className="h-5 w-5 text-orange-600" />
                </div>
              </div>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <input
                    type="text"
                    placeholder="Search policies..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Categories</option>
                  <option value="content_safety">Content Safety</option>
                  <option value="data_masking">Data Masking</option>
                  <option value="access_control">Access Control</option>
                  <option value="medical_privacy">Medical Privacy</option>
                  <option value="compliance">Compliance</option>
                </select>

                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>

                <MultiSelectDropdown
                  options={agents.map((a) => ({ id: a.agent_id || a.id, label: a.name }))}
                  selected={selectedAgents}
                  onChange={setSelectedAgents}
                  placeholder="Agents"
                  dataCy="agents-dd"
                />
                <MultiSelectDropdown
                  options={groups.map((g) => ({ id: g.group_id || g.policy_group_id || g.id, label: g.name }))}
                  selected={selectedGroups}
                  onChange={setSelectedGroups}
                  placeholder="Policy Groups"
                  dataCy="policy-groups-dd"
                />
              </div>

              <div className="text-sm text-gray-600">
                Showing {filteredPolicies.length} of {policies.length} policies
              </div>
            </div>
          </div>

          {/* Policies Table */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                <thead className="bg-gray-50 dark:bg-gray-900">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Policy Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Category</th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Severity</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Assignment</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Violations</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Last Modified</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredPolicies.map((policy) => {
                    // Skip rendering if policy is undefined or null
                    if (!policy) return null;
                    
                    const policyViolations = violations.filter(v => v.policy_id === policy.policy_id);
                    return (
                      <tr key={policy.policy_id} className="hover:bg-gray-50 dark:hover:bg-gray-900">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{policy.name || 'Unnamed Policy'}</div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">{policy.description || 'No description'}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(policy.category)}`}>
                            {policy.category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </span>
                        </td>
                        <td className="px-6 py-4 flex justify-center items-center">
                          <ToggleSwitch
                            isLoading={loadingStatusId === policy.policy_id}
                            isActive={policy.is_active}
                            onToggle={() => handleToggleStatus(policy)}
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(policy.severity)}`}>
                            {policy.severity}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <AssignmentTypeBadge type={policy.assignment_type || '—'} />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">
                            {policyViolations.length}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(policy.updated_at).toLocaleDateString()}
                        </td>
                        {/* Removed Rego/Blob status, last generated, and Rego action buttons */}
                        {/* Existing actions */}
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleEditPolicy(policy)}
                              className="text-green-600 hover:text-green-900 p-1 rounded"
                              title="Edit Policy"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDelete(policy.policy_id)}
                              className="text-red-600 hover:text-red-900 p-1 rounded"
                              title="Delete Policy"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {filteredPolicies.length === 0 && (
            <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
              <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No policies found</h3>
              <p className="text-gray-600 mb-4">Get started by creating your first policy.</p>
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Create Policy
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Policy Details Modal */}
      {selectedPolicy && (
        <PolicyModal
          policy={selectedPolicy}
          onClose={() => setSelectedPolicy(null)}
          onSave={handleUpdatePolicy}
        />
      )}

      {/* Create Policy Modal */}
      {showCreateModal && (
        <EnhancedPolicyModal
          onClose={() => {
            setShowCreateModal(false);
            setSelectedPolicyForEdit(null);
          }}
          onSave={handleSaveNewPolicy}
          existingPolicy={selectedPolicyForEdit}
        />
      )}
      
      {/* Enum Management Modal */}
      {showEnumManagement && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-full max-h-[90vh] overflow-hidden">
            <div className="flex justify-between items-center p-4 border-b">
              <h2 className="text-xl font-semibold">Enum Value Management</h2>
              <button
                onClick={() => setShowEnumManagement(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            <div className="overflow-y-auto h-full">
              <EnumManagement />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

function PolicyModal({ policy, onClose, onSave }) {
  const [editedPolicy, setEditedPolicy] = useState({ ...policy });

  const handleSave = async () => {
    onSave(editedPolicy);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-8 w-full max-w-2xl">
        <h2 className="text-2xl font-bold mb-6 text-gray-800">Edit Policy</h2>
        <div className="space-y-4">
          <input
            type="text"
            value={editedPolicy.name}
            onChange={(e) => setEditedPolicy({ ...editedPolicy, name: e.target.value })}
            placeholder="Policy Name"
            className="w-full p-2 border rounded"
          />
          <textarea
            value={editedPolicy.description}
            onChange={(e) => setEditedPolicy({ ...editedPolicy, description: e.target.value })}
            placeholder="Description"
            className="w-full p-2 border rounded"
            rows="3"
          ></textarea>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
              <select
                value={editedPolicy.category}
                onChange={(e) => setEditedPolicy({ ...editedPolicy, category: e.target.value })}
                className="w-full p-2 border rounded"
              >
                <option value="content_safety">Content Safety</option>
                <option value="data_masking">Data Masking</option>
                <option value="access_control">Access Control</option>
                <option value="medical_privacy">Medical Privacy</option>
                <option value="compliance">Compliance</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Severity</label>
              <select
                value={editedPolicy.severity}
                onChange={(e) => setEditedPolicy({ ...editedPolicy, severity: e.target.value })}
                className="w-full p-2 border rounded"
              >
                <option value="critical">Critical</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Policy Type</label>
              <input
                type="text"
                value={editedPolicy.policy_type || 'opa'}
                readOnly
                className="w-full p-2 border rounded bg-gray-100"
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Policy Definition (JSON)</label>
            <textarea
              value={JSON.stringify(editedPolicy.definition, null, 2)}
              onChange={(e) => setEditedPolicy({ ...editedPolicy, definition: JSON.parse(e.target.value) })}
              rows={8}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Save Changes
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

function CreatePolicyModal({ onClose, onSave }) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('content_safety');
  const [severity, setSeverity] = useState('medium');
  const [definition, setDefinition] = useState('');
  const [policyType, setPolicyType] = useState('opa'); // Add policy type state

  const handleSave = async () => {
    // Basic validation
    if (!name || !category || !severity || !definition) {
      alert('Please fill all required fields.');
      return;
    }

    // Validate JSON definition
    let parsedDefinition;
    try {
      parsedDefinition = JSON.parse(definition);
    } catch (error) {
      alert('Policy Definition must be valid JSON. Please check your syntax.');
      return;
    }

    const newPolicy = {
      name,
      description,
      category,
      severity,
      definition: parsedDefinition,
      policy_type: policyType, // Include policy type in the payload
      is_active: true // By default, new policies are active
    };

    onSave(newPolicy);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-8 w-full max-w-2xl">
        <h2 className="text-2xl font-bold mb-6 text-gray-800">Create New Policy</h2>

        <div className="space-y-4">
          <input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Policy Name (e.g., 'Block PII in responses')"
            className="w-full p-2 border rounded"
          />
          <textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="A brief description of the policy's purpose and effect."
            className="w-full p-2 border rounded"
            rows="3"
          ></textarea>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
              <select value={category} onChange={(e) => setCategory(e.target.value)} className="w-full p-2 border rounded">
                <option value="content_safety">Content Safety</option>
                <option value="data_masking">Data Masking</option>
                <option value="access_control">Access Control</option>
                <option value="medical_privacy">Medical Privacy</option>
                <option value="compliance">Compliance</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Severity</label>
              <select value={severity} onChange={(e) => setSeverity(e.target.value)} className="w-full p-2 border rounded">
                <option value="critical">Critical</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Policy Type</label>
              <select value={policyType} onChange={(e) => setPolicyType(e.target.value)} className="w-full p-2 border rounded bg-gray-100 cursor-not-allowed">
                <option value="opa">OPA</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Policy Definition (JSON)</label>
            <textarea
              value={definition}
              onChange={(e) => setDefinition(e.target.value)}
              placeholder='{\n  "rule": "allow",\n  "conditions": {\n    "user_role": "admin"\n  }\n}'
              rows={8}
              className="w-full p-2 border rounded font-mono text-sm"
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Create Policy
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PolicyAdminDashboard;
