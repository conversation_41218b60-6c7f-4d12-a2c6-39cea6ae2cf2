{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Policy Management Schemas", "description": "JSON schemas for validating policy definitions", "definitions": {"basePolicy": {"type": "object", "properties": {"type": {"type": "string", "description": "Policy type identifier"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"], "description": "Policy severity level", "default": "medium"}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "enabled": {"type": "boolean", "description": "Whether the policy is currently active", "default": true}}, "required": ["type", "severity"]}, "roleArray": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "Array of role identifiers"}, "fieldArray": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "Array of field names"}}, "schemas": {"medical_privacy": {"type": "object", "title": "Medical Privacy Policy", "description": "Template for HIPAA-compliant medical privacy policies", "properties": {"type": {"type": "string", "const": "medical_privacy", "description": "Policy type identifier"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"], "description": "Policy severity level", "default": "medium"}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "enabled": {"type": "boolean", "description": "Whether the policy is currently active", "default": true}, "allowed_roles": {"type": "array", "items": {"type": "string", "enum": ["doctor", "nurse", "admin", "pharmacist", "lab_tech", "specialist", "resident"]}, "minItems": 1, "description": "Roles with access to medical data"}, "hipaa_compliance": {"type": "boolean", "default": true, "description": "Enforce HIPAA compliance requirements"}, "protected_fields": {"type": "array", "items": {"type": "string", "enum": ["diagnosis", "medication", "lab_orders", "medical_record_number", "treatment_plan", "billing_info", "patient_notes", "prescriptions", "vital_signs", "allergies", "family_history", "immunizations"]}, "minItems": 1, "description": "Medical fields requiring special protection"}, "audit_requirements": {"type": "object", "properties": {"log_access": {"type": "boolean", "default": true, "description": "Log all access to medical data"}, "retention_period": {"type": "integer", "minimum": 1, "maximum": 10, "default": 7, "description": "Audit log retention period in years"}, "encryption_required": {"type": "boolean", "default": true, "description": "Require encryption for medical data"}, "access_timeout": {"type": "integer", "minimum": 5, "maximum": 60, "default": 30, "description": "Session timeout in minutes"}}, "required": ["log_access", "retention_period"]}, "data_handling": {"type": "object", "properties": {"anonymization": {"type": "boolean", "default": false, "description": "Anonymize data for research purposes"}, "pseudonymization": {"type": "boolean", "default": true, "description": "Use pseudonyms for patient identification"}, "data_minimization": {"type": "boolean", "default": true, "description": "Collect only necessary data"}}}}, "required": ["type", "severity", "enabled", "allowed_roles", "protected_fields"], "additionalProperties": false}, "data_privacy": {"type": "object", "title": "Data Privacy Policy", "description": "Template for general data privacy and protection policies", "properties": {"type": {"type": "string", "const": "data_privacy", "description": "Policy type identifier"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"], "description": "Policy severity level", "default": "medium"}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "enabled": {"type": "boolean", "description": "Whether the policy is currently active", "default": true}, "allowed_roles": {"type": "array", "items": {"type": "string", "enum": ["admin", "manager", "analyst", "user", "viewer", "editor"]}, "minItems": 1, "description": "Roles with access to sensitive data"}, "data_classification": {"type": "string", "enum": ["public", "internal", "confidential", "restricted", "secret"], "default": "confidential", "description": "Classification level of the data"}, "protected_fields": {"type": "array", "items": {"type": "string", "enum": ["personal_info", "financial_data", "contact_details", "identification", "preferences", "behavioral_data", "location_data", "biometric_data"]}, "minItems": 1, "description": "Data fields requiring protection"}, "consent_requirements": {"type": "object", "properties": {"explicit_consent": {"type": "boolean", "default": true, "description": "Require explicit user consent"}, "consent_expiry": {"type": "integer", "minimum": 1, "maximum": 60, "default": 12, "description": "Consent validity period in months"}, "withdrawal_allowed": {"type": "boolean", "default": true, "description": "Allow users to withdraw consent"}}, "required": ["explicit_consent"]}, "data_retention": {"type": "object", "properties": {"retention_period": {"type": "integer", "minimum": 1, "maximum": 120, "default": 24, "description": "Data retention period in months"}, "auto_deletion": {"type": "boolean", "default": true, "description": "Automatically delete expired data"}, "archive_after": {"type": "integer", "minimum": 1, "maximum": 60, "default": 12, "description": "Archive data after this many months"}}, "required": ["retention_period"]}}, "required": ["type", "severity", "allowed_roles", "data_classification", "protected_fields"], "additionalProperties": false}, "access_control": {"type": "object", "title": "Access Control Policy", "description": "Template for role-based access control policies", "properties": {"type": {"type": "string", "const": "access_control", "description": "Policy type identifier"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"], "description": "Policy severity level", "default": "medium"}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "enabled": {"type": "boolean", "description": "Whether the policy is currently active", "default": true}, "allowed_roles": {"type": "array", "items": {"type": "string", "enum": ["admin", "manager", "user", "viewer", "editor", "guest", "moderator"]}, "minItems": 1, "description": "Roles with access to the resource"}, "restricted_actions": {"type": "array", "items": {"type": "string", "enum": ["create", "read", "update", "delete", "export", "import", "share", "print"]}, "description": "Actions that are restricted for this resource"}, "ip_whitelist": {"type": "array", "items": {"type": "string", "pattern": "^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}(?:/[0-9]{1,2})?$"}, "description": "Allowed IP addresses or CIDR ranges"}, "time_restrictions": {"type": "object", "properties": {"start_time": {"type": "string", "pattern": "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", "default": "09:00", "description": "Start time for access (24-hour format)"}, "end_time": {"type": "string", "pattern": "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", "default": "17:00", "description": "End time for access (24-hour format)"}, "timezone": {"type": "string", "default": "UTC", "description": "Timezone for time restrictions"}, "allowed_days": {"type": "array", "items": {"type": "string", "enum": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]}, "default": ["monday", "tuesday", "wednesday", "thursday", "friday"], "description": "Days when access is allowed"}}, "required": ["start_time", "end_time"]}, "session_management": {"type": "object", "properties": {"max_session_duration": {"type": "integer", "minimum": 5, "maximum": 480, "default": 60, "description": "Maximum session duration in minutes"}, "inactivity_timeout": {"type": "integer", "minimum": 1, "maximum": 60, "default": 15, "description": "Inactivity timeout in minutes"}, "concurrent_sessions": {"type": "integer", "minimum": 1, "maximum": 10, "default": 3, "description": "Maximum concurrent sessions per user"}}, "required": ["max_session_duration", "inactivity_timeout"]}}, "required": ["type", "severity", "allowed_roles"], "additionalProperties": false}, "compliance": {"type": "object", "title": "Compliance Policy", "description": "Template for regulatory compliance policies", "properties": {"type": {"type": "string", "const": "compliance", "description": "Policy type identifier"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"], "description": "Policy severity level", "default": "medium"}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "enabled": {"type": "boolean", "description": "Whether the policy is currently active", "default": true}, "allowed_roles": {"type": "array", "items": {"type": "string", "enum": ["compliance_officer", "auditor", "admin", "manager", "supervisor"]}, "minItems": 1, "description": "Roles responsible for compliance"}, "regulatory_framework": {"type": "string", "enum": ["gdpr", "ccpa", "sox", "hipaa", "pci_dss", "iso27001", "ferpa"], "description": "Regulatory framework this policy enforces"}, "compliance_requirements": {"type": "array", "items": {"type": "string", "enum": ["data_encryption", "access_logging", "audit_trails", "consent_management", "data_minimization", "right_to_forget", "breach_notification", "vendor_management"]}, "minItems": 1, "description": "Specific compliance requirements to enforce"}, "audit_frequency": {"type": "string", "enum": ["monthly", "quarterly", "semi_annually", "annually"], "default": "quarterly", "description": "Frequency of compliance audits"}, "reporting_requirements": {"type": "object", "properties": {"incident_reporting": {"type": "boolean", "default": true, "description": "Require incident reporting"}, "reporting_timeframe": {"type": "integer", "minimum": 1, "maximum": 72, "default": 24, "description": "Hours to report incidents"}, "regulatory_notifications": {"type": "boolean", "default": true, "description": "Notify regulatory bodies of incidents"}}, "required": ["incident_reporting", "reporting_timeframe"]}, "penalties": {"type": "object", "properties": {"monetary_fines": {"type": "boolean", "default": true, "description": "Apply monetary fines for violations"}, "max_fine_amount": {"type": "number", "minimum": 0, "description": "Maximum fine amount in currency units"}, "suspension_period": {"type": "integer", "minimum": 1, "maximum": 365, "description": "Account suspension period in days"}}}}, "required": ["type", "severity", "allowed_roles", "regulatory_framework", "compliance_requirements"], "additionalProperties": false}}}