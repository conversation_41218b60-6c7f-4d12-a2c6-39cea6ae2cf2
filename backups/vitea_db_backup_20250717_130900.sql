--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5
-- Dumped by pg_dump version 17.5 (Ubuntu 17.5-1.pgdg24.04+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: azure_pg_admin
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO azure_pg_admin;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: generate_rego_for_policy(uuid); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.generate_rego_for_policy(policy_uuid uuid) RETURNS json
    LANGUAGE plpgsql
    AS $$
DECLARE
    policy_record policies%ROWTYPE;
    template_record rego_templates%ROWTYPE;
    generated_rego TEXT;
    blob_path VARCHAR(500);
    result JSON;
BEGIN
    -- Get policy details
    SELECT * INTO policy_record FROM policies WHERE policy_id = policy_uuid;
    IF NOT FOUND THEN
        RETURN json_build_object('success', false, 'error', 'Policy not found');
    END IF;
    -- Get template based on policy category
    SELECT * INTO template_record FROM rego_templates WHERE policy_category = policy_record.category AND is_active = true LIMIT 1;
    IF NOT FOUND THEN
        RETURN json_build_object('success', false, 'error', 'Template not found');
    END IF;
    -- Simulate template rendering (actual implementation should use server-side code)
    generated_rego := template_record.template_content;
    blob_path := CONCAT('active/policy_', policy_record.policy_id, '_', policy_record.category, '_v', policy_record.version, '.rego');
    -- Update policy with generated Rego
    UPDATE policies SET rego_code = generated_rego, blob_path = blob_path, rego_template_id = template_record.template_id, last_rego_generation = CURRENT_TIMESTAMP, opa_sync_status = 'generated', rego_version = policy_record.version WHERE policy_id = policy_uuid;
    -- Log operation
    PERFORM log_rego_operation('00000000-0000-0000-0000-000000000000', json_build_object('policy_id', policy_uuid, 'action', 'generate_rego', 'status', 'success'));
    RETURN json_build_object('success', true, 'rego_preview', generated_rego, 'blob_path', blob_path);
END;
$$;


ALTER FUNCTION public.generate_rego_for_policy(policy_uuid uuid) OWNER TO dbadmin;

--
-- Name: log_rego_operation(uuid, jsonb); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.log_rego_operation(user_uuid uuid, operation_details jsonb) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO audit_log (user_id, action, resource_type, resource_id, new_values, timestamp)
    VALUES (user_uuid, 'policy_rego', 'policy_rego', operation_details->>'policy_id', operation_details, CURRENT_TIMESTAMP);
END;
$$;


ALTER FUNCTION public.log_rego_operation(user_uuid uuid, operation_details jsonb) OWNER TO dbadmin;

--
-- Name: rollback_rego_generation(uuid); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.rollback_rego_generation(policy_uuid uuid) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
BEGIN
    UPDATE policies SET rego_code = NULL, blob_path = NULL, rego_template_id = NULL, last_rego_generation = NULL, opa_sync_status = 'pending', rego_generation_error = NULL WHERE policy_id = policy_uuid;
    PERFORM log_rego_operation('00000000-0000-0000-0000-000000000000', json_build_object('policy_id', policy_uuid, 'action', 'rollback_rego', 'status', 'success'));
    RETURN FOUND;
END;
$$;


ALTER FUNCTION public.rollback_rego_generation(policy_uuid uuid) OWNER TO dbadmin;

--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_updated_at_column() OWNER TO dbadmin;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: agents; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.agents (
    agent_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    agent_type character varying(100) DEFAULT 'policy_engine'::character varying,
    endpoint_url character varying(500),
    is_active boolean DEFAULT true,
    configuration jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid
);


ALTER TABLE public.agents OWNER TO dbadmin;

--
-- Name: audit_log; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.audit_log (
    log_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid,
    action character varying(100) NOT NULL,
    resource_type character varying(100),
    resource_id uuid,
    old_values jsonb,
    new_values jsonb,
    ip_address inet,
    user_agent text,
    "timestamp" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.audit_log OWNER TO dbadmin;

--
-- Name: chat_messages; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.chat_messages (
    message_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    session_id uuid,
    role character varying(20) NOT NULL,
    content text NOT NULL,
    original_content text,
    policies_applied uuid[] DEFAULT '{}'::uuid[],
    is_filtered boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.chat_messages OWNER TO dbadmin;

--
-- Name: documents; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.documents (
    document_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    title character varying(500) NOT NULL,
    content text,
    document_type character varying(100),
    metadata jsonb,
    file_path character varying(1000),
    is_sensitive boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    updated_by uuid
);


ALTER TABLE public.documents OWNER TO dbadmin;

--
-- Name: mcp_chat_sessions; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.mcp_chat_sessions (
    session_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid,
    status character varying(20) DEFAULT 'active'::character varying,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    metadata jsonb DEFAULT '{}'::jsonb
);


ALTER TABLE public.mcp_chat_sessions OWNER TO dbadmin;

--
-- Name: mcp_flow_steps; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.mcp_flow_steps (
    step_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    session_id uuid,
    step_number integer NOT NULL,
    step_name character varying(100) NOT NULL,
    status character varying(20) DEFAULT 'pending'::character varying,
    input_data jsonb,
    output_data jsonb,
    processing_time_ms integer,
    policies_applied uuid[] DEFAULT '{}'::uuid[],
    violations_detected uuid[] DEFAULT '{}'::uuid[],
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    completed_at timestamp with time zone
);


ALTER TABLE public.mcp_flow_steps OWNER TO dbadmin;

--
-- Name: openai_api_calls; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.openai_api_calls (
    call_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    session_id uuid,
    step_id uuid,
    model_name character varying(50),
    prompt_tokens integer,
    completion_tokens integer,
    total_tokens integer,
    cost_estimate numeric(10,6),
    response_time_ms integer,
    status_code integer,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.openai_api_calls OWNER TO dbadmin;

--
-- Name: policies; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policies (
    policy_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    category character varying(100) NOT NULL,
    definition jsonb NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    is_active boolean DEFAULT false,
    severity character varying(20) DEFAULT 'medium'::character varying,
    applies_to_roles text[],
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    updated_by uuid,
    rego_code text,
    blob_container character varying(255) DEFAULT 'rego-policies'::character varying,
    blob_path character varying(500),
    blob_url character varying(1000),
    rego_template_id character varying(100),
    opa_sync_status character varying(50) DEFAULT 'pending'::character varying,
    last_rego_generation timestamp with time zone,
    rego_generation_error text,
    rego_version integer DEFAULT 1
);


ALTER TABLE public.policies OWNER TO dbadmin;

--
-- Name: policy_executions; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policy_executions (
    execution_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    session_id uuid,
    step_id uuid,
    policy_id uuid,
    execution_status character varying(20),
    input_data jsonb,
    output_data jsonb,
    execution_time_ms integer,
    error_message text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.policy_executions OWNER TO dbadmin;

--
-- Name: policy_templates; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policy_templates (
    template_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    category character varying(100) NOT NULL,
    template_definition jsonb NOT NULL,
    is_system_template boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid
);


ALTER TABLE public.policy_templates OWNER TO dbadmin;

--
-- Name: policy_violations; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policy_violations (
    violation_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    policy_id uuid,
    user_id uuid,
    violation_type character varying(100) NOT NULL,
    details jsonb,
    severity character varying(20),
    resolved boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    resolved_at timestamp with time zone,
    resolved_by uuid
);


ALTER TABLE public.policy_violations OWNER TO dbadmin;

--
-- Name: rego_templates; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.rego_templates (
    template_id character varying(100) NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    policy_category character varying(100) NOT NULL,
    template_content text NOT NULL,
    variables jsonb DEFAULT '[]'::jsonb,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid
);


ALTER TABLE public.rego_templates OWNER TO dbadmin;

--
-- Name: system_metrics; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.system_metrics (
    metric_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    metric_name character varying(100) NOT NULL,
    metric_value numeric(15,6),
    metric_unit character varying(20),
    tags jsonb DEFAULT '{}'::jsonb,
    recorded_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.system_metrics OWNER TO dbadmin;

--
-- Name: users; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.users (
    user_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    azure_ad_id character varying(255) NOT NULL,
    email character varying(255) NOT NULL,
    first_name character varying(100),
    last_name character varying(100),
    role character varying(50) DEFAULT 'user'::character varying NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    updated_by uuid
);


ALTER TABLE public.users OWNER TO dbadmin;

--
-- Data for Name: agents; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.agents (agent_id, name, description, agent_type, endpoint_url, is_active, configuration, created_at, updated_at, created_by) FROM stdin;
3d066259-0f1a-4833-b6b5-811f450262da	default-agent	Default policy agent for initial implementation	policy_engine	\N	t	{}	2025-07-16 13:03:01.767462+00	2025-07-16 13:03:01.767462+00	00000000-0000-0000-0000-000000000000
\.


--
-- Data for Name: audit_log; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.audit_log (log_id, user_id, action, resource_type, resource_id, old_values, new_values, ip_address, user_agent, "timestamp") FROM stdin;
9cde1a25-5a47-44cc-b0f6-b4ebb1c09558	00000000-0000-0000-0000-000000000000	schema_update	database	\N	\N	{"update": "Added Rego and Blob Storage columns to policies table, created agents and rego_templates tables"}	\N	\N	2025-07-16 13:03:02.480176+00
\.


--
-- Data for Name: chat_messages; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.chat_messages (message_id, session_id, role, content, original_content, policies_applied, is_filtered, created_at) FROM stdin;
\.


--
-- Data for Name: documents; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.documents (document_id, title, content, document_type, metadata, file_path, is_sensitive, created_at, updated_at, created_by, updated_by) FROM stdin;
f38348b5-ae08-4a32-a4e2-c9e33e1e06fb	HIPAA Compliance Guidelines	This document outlines HIPAA compliance requirements for healthcare organizations...	guideline	{"category": "compliance", "department": "legal"}	\N	f	2025-07-03 19:06:38.574539+00	2025-07-03 19:06:38.574539+00	\N	\N
813f05d4-371f-4a35-8ae3-b40ea6603db5	Patient Care Protocol - Diabetes	Standard care protocol for diabetes patients including medication guidelines...	protocol	{"category": "medical", "condition": "diabetes", "department": "endocrinology"}	\N	t	2025-07-03 19:06:38.574539+00	2025-07-03 19:06:38.574539+00	\N	\N
5be58470-c224-434c-9f8b-6198dcdaae1d	HIPAA Compliance Guidelines	This document outlines HIPAA compliance requirements for healthcare organizations...	guideline	{"category": "compliance", "department": "legal"}	\N	f	2025-07-13 18:27:45.697296+00	2025-07-13 18:27:45.697296+00	00000000-0000-0000-0000-000000000000	00000000-0000-0000-0000-000000000000
6accfcc1-ca9c-4392-bca3-1f4301e86917	Patient Care Protocol - Diabetes	Standard care protocol for diabetes patients including medication guidelines...	protocol	{"category": "medical", "condition": "diabetes", "department": "endocrinology"}	\N	t	2025-07-13 18:27:45.697296+00	2025-07-13 18:27:45.697296+00	00000000-0000-0000-0000-000000000000	00000000-0000-0000-0000-000000000000
\.


--
-- Data for Name: mcp_chat_sessions; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.mcp_chat_sessions (session_id, user_id, status, created_at, updated_at, metadata) FROM stdin;
\.


--
-- Data for Name: mcp_flow_steps; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.mcp_flow_steps (step_id, session_id, step_number, step_name, status, input_data, output_data, processing_time_ms, policies_applied, violations_detected, created_at, completed_at) FROM stdin;
\.


--
-- Data for Name: openai_api_calls; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.openai_api_calls (call_id, session_id, step_id, model_name, prompt_tokens, completion_tokens, total_tokens, cost_estimate, response_time_ms, status_code, created_at) FROM stdin;
\.


--
-- Data for Name: policies; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policies (policy_id, name, description, category, definition, version, is_active, severity, applies_to_roles, created_at, updated_at, created_by, updated_by, rego_code, blob_container, blob_path, blob_url, rego_template_id, opa_sync_status, last_rego_generation, rego_generation_error, rego_version) FROM stdin;
0dcf4c78-a927-4c29-bb70-7fc3b5605f68	New Test 1	Mask 	compliance	{"type": "content_filtering", "rules": [], "actions": ["log", "block"]}	1	f	low	\N	2025-07-15 14:21:22.440438+00	2025-07-17 02:54:48.664792+00	00000000-0000-0000-0000-000000000000	00000000-0000-0000-0000-000000000000	\N	rego-policies	\N	\N	\N	pending	\N	\N	1
bb639948-1578-44af-b89c-935e4ed3d501	tes	tet	content_safety	{"type": "content_filtering", "rules": [], "actions": ["log", "block"]}	1	t	medium	\N	2025-07-17 02:59:31.466031+00	2025-07-17 02:59:31.466031+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1
cb4ec823-e9a3-43b4-b1bf-c7ba0229cfa7	Phone Number	Mask Phone	content_safety	{"type": "content_filtering", "rules": [], "actions": ["log", "block"]}	1	t	medium	{user,guest}	2025-07-15 10:43:18.001557+00	2025-07-17 11:25:51.014609+00	00000000-0000-0000-0000-000000000000	00000000-0000-0000-0000-000000000000	\N	rego-policies	\N	\N	\N	pending	\N	\N	1
4d42b86d-eb62-4c7c-bb44-065b8f527b44	test1	test1	content_safety	{"type": "data_masking", "regex_patterns": ["(SSN|ssn|social security number|(\\\\d{3}-\\\\d{2}-\\\\d{4}))"]}	1	f	critical	{all}	2025-07-13 18:34:46.948045+00	2025-07-14 23:00:45.503906+00	00000000-0000-0000-0000-000000000000	00000000-0000-0000-0000-000000000000	\N	rego-policies	\N	\N	\N	pending	\N	\N	1
8baea9f6-b30c-40f0-8806-c271e93a49ef	PII Access Control	Restricts access to sensitive PII fields	access_control	{"type": "access_control", "allowed_roles": ["admin", "manager"], "restricted_fields": ["medical_record_number", "insurance_number"]}	1	t	critical	{user,guest}	2025-07-03 19:06:38.498574+00	2025-07-14 23:00:49.581735+00	\N	00000000-0000-0000-0000-000000000000	\N	rego-policies	\N	\N	\N	pending	\N	\N	1
dd4f1e9f-f067-4c7d-82f7-5d12d73fed68	SSN Masking Policy	Masks SSN fields for non-admin users	data_masking	{"type": "data_masking", "mask_format": "XXX-XX-{last4}", "field_patterns": ["ssn", "social_security_number"], "regex_patterns": ["\\\\\\\\b\\\\\\\\d{3}-\\\\\\\\d{2}-\\\\\\\\d{4}\\\\\\\\b"]}	1	t	critical	\N	2025-07-03 19:06:38.498574+00	2025-07-15 10:43:59.557575+00	\N	00000000-0000-0000-0000-000000000000	\N	rego-policies	\N	\N	\N	pending	\N	\N	1
\.


--
-- Data for Name: policy_executions; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policy_executions (execution_id, session_id, step_id, policy_id, execution_status, input_data, output_data, execution_time_ms, error_message, created_at) FROM stdin;
\.


--
-- Data for Name: policy_templates; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policy_templates (template_id, name, description, category, template_definition, is_system_template, created_at, created_by) FROM stdin;
81ec93ea-45f1-45ff-bf2a-126b6efb4cac	PII Masking Template	Template for masking personally identifiable information	data_masking	{"type": "data_masking", "severity": "critical", "mask_format": "XXX-XX-{last4}", "field_patterns": ["ssn", "social_security_number", "phone", "email"], "regex_patterns": ["\\\\\\\\b\\\\\\\\d{3}-\\\\\\\\d{2}-\\\\\\\\d{4}\\\\\\\\b", "\\\\\\\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\\\\\\\.[A-Z|a-z]{2,}\\\\\\\\b"]}	t	2025-07-08 14:43:36.069534+00	\N
30460a4b-c9e8-42cf-8748-5df919482e7a	Content Safety Template	Template for detecting harmful or inappropriate content	content_safety	{"type": "content_filtering", "action": "block_and_log", "severity": "high", "blocked_categories": ["hate_speech", "violence", "self_harm", "sexual"], "confidence_threshold": 0.7}	t	2025-07-08 14:43:36.069534+00	\N
34a6a78e-779f-407e-8387-452e1275b555	Medical Info Protection Template	Template for protecting sensitive medical information	medical_privacy	{"type": "medical_privacy", "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}	t	2025-07-08 14:43:36.069534+00	\N
7c71da67-d94f-441e-97df-0680be4f50a5	PII Masking Template	Template for masking personally identifiable information	data_masking	{"type": "data_masking", "severity": "critical", "mask_format": "XXX-XX-{last4}", "field_patterns": ["ssn", "social_security_number", "phone", "email"], "regex_patterns": ["\\\\\\\\b\\\\\\\\d{3}-\\\\\\\\d{2}-\\\\\\\\d{4}\\\\\\\\b", "\\\\\\\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\\\\\\\.[A-Z|a-z]{2,}\\\\\\\\b"]}	t	2025-07-16 13:01:18.210929+00	\N
890ed4d9-8375-45dc-b447-e9dcada1ffee	Content Safety Template	Template for detecting harmful or inappropriate content	content_safety	{"type": "content_filtering", "action": "block_and_log", "severity": "high", "blocked_categories": ["hate_speech", "violence", "self_harm", "sexual"], "confidence_threshold": 0.7}	t	2025-07-16 13:01:18.210929+00	\N
965483f1-0073-4901-85e0-d9e112c95941	Medical Info Protection Template	Template for protecting sensitive medical information	medical_privacy	{"type": "medical_privacy", "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}	t	2025-07-16 13:01:18.210929+00	\N
db8d5fb0-eaad-4917-84d8-5dfc749462fa	PII Masking Template	Template for masking personally identifiable information	data_masking	{"type": "data_masking", "severity": "critical", "mask_format": "XXX-XX-{last4}", "field_patterns": ["ssn", "social_security_number", "phone", "email"], "regex_patterns": ["\\\\\\\\b\\\\\\\\d{3}-\\\\\\\\d{2}-\\\\\\\\d{4}\\\\\\\\b", "\\\\\\\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\\\\\\\.[A-Z|a-z]{2,}\\\\\\\\b"]}	t	2025-07-16 13:02:34.939009+00	\N
1f918f59-69a9-4092-963b-fb7f134f704f	Content Safety Template	Template for detecting harmful or inappropriate content	content_safety	{"type": "content_filtering", "action": "block_and_log", "severity": "high", "blocked_categories": ["hate_speech", "violence", "self_harm", "sexual"], "confidence_threshold": 0.7}	t	2025-07-16 13:02:34.939009+00	\N
99ae91ce-68bf-4cc8-98ec-97c5b84042c8	Medical Info Protection Template	Template for protecting sensitive medical information	medical_privacy	{"type": "medical_privacy", "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}	t	2025-07-16 13:02:34.939009+00	\N
14b4b1c5-ef7a-481e-831f-849493d51beb	PII Masking Template	Template for masking personally identifiable information	data_masking	{"type": "data_masking", "severity": "critical", "mask_format": "XXX-XX-{last4}", "field_patterns": ["ssn", "social_security_number", "phone", "email"], "regex_patterns": ["\\\\\\\\b\\\\\\\\d{3}-\\\\\\\\d{2}-\\\\\\\\d{4}\\\\\\\\b", "\\\\\\\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\\\\\\\.[A-Z|a-z]{2,}\\\\\\\\b"]}	t	2025-07-16 13:02:59.253047+00	\N
db42cef6-86b1-4e4f-82ed-de2fe59de4b5	Content Safety Template	Template for detecting harmful or inappropriate content	content_safety	{"type": "content_filtering", "action": "block_and_log", "severity": "high", "blocked_categories": ["hate_speech", "violence", "self_harm", "sexual"], "confidence_threshold": 0.7}	t	2025-07-16 13:02:59.253047+00	\N
621ad09a-b516-421c-a76e-1f675eba13e7	Medical Info Protection Template	Template for protecting sensitive medical information	medical_privacy	{"type": "medical_privacy", "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}	t	2025-07-16 13:02:59.253047+00	\N
\.


--
-- Data for Name: policy_violations; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policy_violations (violation_id, policy_id, user_id, violation_type, details, severity, resolved, created_at, resolved_at, resolved_by) FROM stdin;
\.


--
-- Data for Name: rego_templates; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.rego_templates (template_id, name, description, policy_category, template_content, variables, is_active, created_at, updated_at, created_by) FROM stdin;
data_masking_v1	Data Masking Template v1	Template for data masking policies	data_masking	-- Data Masking Rego Template --\npackage vitea.policies.{{policy_id}}.data_masking\n\nimport rego.v1\n\n# Policy: {{policy_name}}\n# Description: {{description}}\n# Generated: {{generated_at}}\n\nfield_patterns := {{field_patterns}}\nregex_patterns := {{regex_patterns}}\nmask_format := "{{mask_format}}"\napplies_to_roles := {{applies_to_roles}}\n\nmask_required if {\n    input.user.role in applies_to_roles\n    input.field_name in field_patterns\n}\n\nmasked_value := result if {\n    mask_required\n    some pattern in regex_patterns\n    regex.match(pattern, input.value)\n    result := apply_mask_format(input.value, mask_format)\n}\n\napply_mask_format(value, format) := result if {\n    format == "XXX-XX-{last4}"\n    result := format("XXX-XX-%s", substring(value, count(value) - 4, 4))\n}	["policy_id", "policy_name", "description", "generated_at", "field_patterns", "regex_patterns", "mask_format", "applies_to_roles"]	t	2025-07-16 20:09:30.686629+00	2025-07-16 20:09:30.686629+00	00000000-0000-0000-0000-000000000000
access_control_v1	Access Control Template v1	Template for access control policies	access_control	-- Access Control Rego Template --\npackage vitea.policies.{{policy_id}}.access_control\n\nimport rego.v1\n\n# Policy: {{policy_name}}\n# Description: {{description}}\n# Generated: {{generated_at}}\n\nrestricted_fields := {{restricted_fields}}\nallowed_roles := {{allowed_roles}}\nseverity := "{{severity}}"\n\ndeny contains violation if {\n    input.field_name in restricted_fields\n    not input.user.role in allowed_roles\n    violation := {\n        "message": format("Access denied to field '%s' for role '%s'", input.field_name, input.user.role),\n        "user_role": input.user.role\n    }\n}\n\nallow if {\n    input.field_name in restricted_fields\n    input.user.role in allowed_roles\n}	["policy_id", "policy_name", "description", "generated_at", "restricted_fields", "allowed_roles", "severity"]	t	2025-07-16 20:09:30.686629+00	2025-07-16 20:09:30.686629+00	00000000-0000-0000-0000-000000000000
\.


--
-- Data for Name: system_metrics; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.system_metrics (metric_id, metric_name, metric_value, metric_unit, tags, recorded_at) FROM stdin;
e9291b77-008d-4fd0-87c6-7549e78f1146	total_chat_sessions	0.000000	count	{"period": "daily"}	2025-07-08 14:43:36.159319+00
96369f34-c95e-4ad1-ad4b-24ff3c993aef	average_response_time	0.000000	milliseconds	{"component": "api"}	2025-07-08 14:43:36.159319+00
989029c5-ee90-458e-815f-72d378615b59	policy_violations_count	0.000000	count	{"severity": "all"}	2025-07-08 14:43:36.159319+00
49d012ff-3325-407a-8a9b-f257061ce47b	openai_api_cost	0.000000	dollars	{"period": "daily"}	2025-07-08 14:43:36.159319+00
5ababa84-25cf-45da-becd-50f072fa0d6c	active_policies_count	2.000000	count	{"status": "active"}	2025-07-08 14:43:36.159319+00
5cc0890d-49ed-44d6-9132-e503e8287b82	total_chat_sessions	0.000000	count	{"period": "daily"}	2025-07-16 13:01:18.286106+00
b261daf4-d1cb-4170-99c3-5173b76624e1	average_response_time	0.000000	milliseconds	{"component": "api"}	2025-07-16 13:01:18.286106+00
03cf97a7-f32a-4bf9-ac56-c87d3ef1eaa7	policy_violations_count	0.000000	count	{"severity": "all"}	2025-07-16 13:01:18.286106+00
99a39290-8432-444b-aa1c-cbf59cb8da70	openai_api_cost	0.000000	dollars	{"period": "daily"}	2025-07-16 13:01:18.286106+00
17c66cb9-df75-47bf-aea9-b2ada08d244f	active_policies_count	2.000000	count	{"status": "active"}	2025-07-16 13:01:18.286106+00
5c32b700-138a-43d7-a8b0-777cc4ef7084	total_chat_sessions	0.000000	count	{"period": "daily"}	2025-07-16 13:02:35.004325+00
17234e9d-04d6-406e-9265-a8eac32c0afd	average_response_time	0.000000	milliseconds	{"component": "api"}	2025-07-16 13:02:35.004325+00
1dfeb84f-420d-485b-9ba9-451223d6e67e	policy_violations_count	0.000000	count	{"severity": "all"}	2025-07-16 13:02:35.004325+00
edfc0b71-b75e-4d70-947d-c661862d6aa9	openai_api_cost	0.000000	dollars	{"period": "daily"}	2025-07-16 13:02:35.004325+00
0aa1fd9a-c069-411a-916c-78f9c65f5364	active_policies_count	2.000000	count	{"status": "active"}	2025-07-16 13:02:35.004325+00
cfd545a8-f121-4fcb-a25f-98fe26b407a1	total_chat_sessions	0.000000	count	{"period": "daily"}	2025-07-16 13:02:59.324851+00
1838c2a4-f5de-4407-895b-642ab8eeeeb7	average_response_time	0.000000	milliseconds	{"component": "api"}	2025-07-16 13:02:59.324851+00
87738af0-8da7-4053-91de-fb2703a98aee	policy_violations_count	0.000000	count	{"severity": "all"}	2025-07-16 13:02:59.324851+00
b5960412-cf59-4668-a37f-5ae0301b5d69	openai_api_cost	0.000000	dollars	{"period": "daily"}	2025-07-16 13:02:59.324851+00
d6daf0d7-506e-4550-b5ae-af0a58f29586	active_policies_count	2.000000	count	{"status": "active"}	2025-07-16 13:02:59.324851+00
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.users (user_id, azure_ad_id, email, first_name, last_name, role, is_active, created_at, updated_at, created_by, updated_by) FROM stdin;
00000000-0000-0000-0000-000000000000	system	<EMAIL>	System	User	system	t	2025-07-13 18:27:45.539403+00	2025-07-13 18:27:45.539403+00	\N	\N
\.


--
-- Name: agents agents_name_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agents
    ADD CONSTRAINT agents_name_key UNIQUE (name);


--
-- Name: agents agents_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agents
    ADD CONSTRAINT agents_pkey PRIMARY KEY (agent_id);


--
-- Name: audit_log audit_log_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.audit_log
    ADD CONSTRAINT audit_log_pkey PRIMARY KEY (log_id);


--
-- Name: chat_messages chat_messages_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.chat_messages
    ADD CONSTRAINT chat_messages_pkey PRIMARY KEY (message_id);


--
-- Name: documents documents_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_pkey PRIMARY KEY (document_id);


--
-- Name: mcp_chat_sessions mcp_chat_sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.mcp_chat_sessions
    ADD CONSTRAINT mcp_chat_sessions_pkey PRIMARY KEY (session_id);


--
-- Name: mcp_flow_steps mcp_flow_steps_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.mcp_flow_steps
    ADD CONSTRAINT mcp_flow_steps_pkey PRIMARY KEY (step_id);


--
-- Name: openai_api_calls openai_api_calls_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.openai_api_calls
    ADD CONSTRAINT openai_api_calls_pkey PRIMARY KEY (call_id);


--
-- Name: policies policies_name_version_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_name_version_key UNIQUE (name, version);


--
-- Name: policies policies_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_pkey PRIMARY KEY (policy_id);


--
-- Name: policy_executions policy_executions_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_executions
    ADD CONSTRAINT policy_executions_pkey PRIMARY KEY (execution_id);


--
-- Name: policy_templates policy_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_templates
    ADD CONSTRAINT policy_templates_pkey PRIMARY KEY (template_id);


--
-- Name: policy_violations policy_violations_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_violations
    ADD CONSTRAINT policy_violations_pkey PRIMARY KEY (violation_id);


--
-- Name: rego_templates rego_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.rego_templates
    ADD CONSTRAINT rego_templates_pkey PRIMARY KEY (template_id);


--
-- Name: system_metrics system_metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.system_metrics
    ADD CONSTRAINT system_metrics_pkey PRIMARY KEY (metric_id);


--
-- Name: users users_azure_ad_id_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_azure_ad_id_key UNIQUE (azure_ad_id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (user_id);


--
-- Name: idx_agents_active; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_agents_active ON public.agents USING btree (is_active);


--
-- Name: idx_agents_type; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_agents_type ON public.agents USING btree (agent_type);


--
-- Name: idx_audit_action; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_action ON public.audit_log USING btree (action);


--
-- Name: idx_audit_resource; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_resource ON public.audit_log USING btree (resource_type, resource_id);


--
-- Name: idx_audit_timestamp; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_timestamp ON public.audit_log USING btree ("timestamp");


--
-- Name: idx_audit_user_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_user_id ON public.audit_log USING btree (user_id);


--
-- Name: idx_chat_messages_created_at; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_chat_messages_created_at ON public.chat_messages USING btree (created_at);


--
-- Name: idx_chat_messages_role; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_chat_messages_role ON public.chat_messages USING btree (role);


--
-- Name: idx_chat_messages_session_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_chat_messages_session_id ON public.chat_messages USING btree (session_id);


--
-- Name: idx_documents_metadata; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_documents_metadata ON public.documents USING gin (metadata);


--
-- Name: idx_documents_sensitive; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_documents_sensitive ON public.documents USING btree (is_sensitive);


--
-- Name: idx_documents_type; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_documents_type ON public.documents USING btree (document_type);


--
-- Name: idx_mcp_flow_steps_session_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_mcp_flow_steps_session_id ON public.mcp_flow_steps USING btree (session_id);


--
-- Name: idx_mcp_flow_steps_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_mcp_flow_steps_status ON public.mcp_flow_steps USING btree (status);


--
-- Name: idx_mcp_flow_steps_step_number; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_mcp_flow_steps_step_number ON public.mcp_flow_steps USING btree (step_number);


--
-- Name: idx_mcp_sessions_created_at; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_mcp_sessions_created_at ON public.mcp_chat_sessions USING btree (created_at);


--
-- Name: idx_mcp_sessions_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_mcp_sessions_status ON public.mcp_chat_sessions USING btree (status);


--
-- Name: idx_mcp_sessions_user_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_mcp_sessions_user_id ON public.mcp_chat_sessions USING btree (user_id);


--
-- Name: idx_openai_calls_created_at; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_openai_calls_created_at ON public.openai_api_calls USING btree (created_at);


--
-- Name: idx_openai_calls_session_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_openai_calls_session_id ON public.openai_api_calls USING btree (session_id);


--
-- Name: idx_policies_active; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_active ON public.policies USING btree (is_active);


--
-- Name: idx_policies_blob_path; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_blob_path ON public.policies USING btree (blob_path);


--
-- Name: idx_policies_category; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_category ON public.policies USING btree (category);


--
-- Name: idx_policies_last_generation; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_last_generation ON public.policies USING btree (last_rego_generation);


--
-- Name: idx_policies_rego_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_rego_status ON public.policies USING btree (opa_sync_status);


--
-- Name: idx_policies_roles; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_roles ON public.policies USING gin (applies_to_roles);


--
-- Name: idx_policies_severity; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_severity ON public.policies USING btree (severity);


--
-- Name: idx_policy_executions_policy_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policy_executions_policy_id ON public.policy_executions USING btree (policy_id);


--
-- Name: idx_policy_executions_session_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policy_executions_session_id ON public.policy_executions USING btree (session_id);


--
-- Name: idx_policy_executions_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policy_executions_status ON public.policy_executions USING btree (execution_status);


--
-- Name: idx_policy_templates_category; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policy_templates_category ON public.policy_templates USING btree (category);


--
-- Name: idx_system_metrics_name; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_system_metrics_name ON public.system_metrics USING btree (metric_name);


--
-- Name: idx_system_metrics_recorded_at; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_system_metrics_recorded_at ON public.system_metrics USING btree (recorded_at);


--
-- Name: idx_users_azure_ad_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_users_azure_ad_id ON public.users USING btree (azure_ad_id);


--
-- Name: idx_users_email; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_users_email ON public.users USING btree (email);


--
-- Name: idx_users_role; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_users_role ON public.users USING btree (role);


--
-- Name: idx_violations_created_at; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_violations_created_at ON public.policy_violations USING btree (created_at);


--
-- Name: idx_violations_policy_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_violations_policy_id ON public.policy_violations USING btree (policy_id);


--
-- Name: idx_violations_resolved; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_violations_resolved ON public.policy_violations USING btree (resolved);


--
-- Name: idx_violations_user_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_violations_user_id ON public.policy_violations USING btree (user_id);


--
-- Name: documents update_documents_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON public.documents FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: mcp_chat_sessions update_mcp_sessions_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_mcp_sessions_updated_at BEFORE UPDATE ON public.mcp_chat_sessions FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: policies update_policies_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_policies_updated_at BEFORE UPDATE ON public.policies FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: rego_templates update_rego_templates_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_rego_templates_updated_at BEFORE UPDATE ON public.rego_templates FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: users update_users_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: agents agents_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agents
    ADD CONSTRAINT agents_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: audit_log audit_log_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.audit_log
    ADD CONSTRAINT audit_log_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id);


--
-- Name: chat_messages chat_messages_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.chat_messages
    ADD CONSTRAINT chat_messages_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.mcp_chat_sessions(session_id);


--
-- Name: documents documents_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: documents documents_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: mcp_chat_sessions mcp_chat_sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.mcp_chat_sessions
    ADD CONSTRAINT mcp_chat_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id);


--
-- Name: mcp_flow_steps mcp_flow_steps_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.mcp_flow_steps
    ADD CONSTRAINT mcp_flow_steps_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.mcp_chat_sessions(session_id);


--
-- Name: openai_api_calls openai_api_calls_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.openai_api_calls
    ADD CONSTRAINT openai_api_calls_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.mcp_chat_sessions(session_id);


--
-- Name: openai_api_calls openai_api_calls_step_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.openai_api_calls
    ADD CONSTRAINT openai_api_calls_step_id_fkey FOREIGN KEY (step_id) REFERENCES public.mcp_flow_steps(step_id);


--
-- Name: policies policies_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: policies policies_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: policy_executions policy_executions_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_executions
    ADD CONSTRAINT policy_executions_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id);


--
-- Name: policy_executions policy_executions_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_executions
    ADD CONSTRAINT policy_executions_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.mcp_chat_sessions(session_id);


--
-- Name: policy_executions policy_executions_step_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_executions
    ADD CONSTRAINT policy_executions_step_id_fkey FOREIGN KEY (step_id) REFERENCES public.mcp_flow_steps(step_id);


--
-- Name: policy_templates policy_templates_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_templates
    ADD CONSTRAINT policy_templates_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: policy_violations policy_violations_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_violations
    ADD CONSTRAINT policy_violations_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id);


--
-- Name: policy_violations policy_violations_resolved_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_violations
    ADD CONSTRAINT policy_violations_resolved_by_fkey FOREIGN KEY (resolved_by) REFERENCES public.users(user_id);


--
-- Name: policy_violations policy_violations_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_violations
    ADD CONSTRAINT policy_violations_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id);


--
-- Name: rego_templates rego_templates_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.rego_templates
    ADD CONSTRAINT rego_templates_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: users users_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: users users_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: FUNCTION pg_replication_origin_advance(text, pg_lsn); Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT ALL ON FUNCTION pg_catalog.pg_replication_origin_advance(text, pg_lsn) TO azure_pg_admin;


--
-- Name: FUNCTION pg_replication_origin_create(text); Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT ALL ON FUNCTION pg_catalog.pg_replication_origin_create(text) TO azure_pg_admin;


--
-- Name: FUNCTION pg_replication_origin_drop(text); Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT ALL ON FUNCTION pg_catalog.pg_replication_origin_drop(text) TO azure_pg_admin;


--
-- Name: FUNCTION pg_replication_origin_oid(text); Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT ALL ON FUNCTION pg_catalog.pg_replication_origin_oid(text) TO azure_pg_admin;


--
-- Name: FUNCTION pg_replication_origin_progress(text, boolean); Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT ALL ON FUNCTION pg_catalog.pg_replication_origin_progress(text, boolean) TO azure_pg_admin;


--
-- Name: FUNCTION pg_replication_origin_session_is_setup(); Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT ALL ON FUNCTION pg_catalog.pg_replication_origin_session_is_setup() TO azure_pg_admin;


--
-- Name: FUNCTION pg_replication_origin_session_progress(boolean); Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT ALL ON FUNCTION pg_catalog.pg_replication_origin_session_progress(boolean) TO azure_pg_admin;


--
-- Name: FUNCTION pg_replication_origin_session_reset(); Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT ALL ON FUNCTION pg_catalog.pg_replication_origin_session_reset() TO azure_pg_admin;


--
-- Name: FUNCTION pg_replication_origin_session_setup(text); Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT ALL ON FUNCTION pg_catalog.pg_replication_origin_session_setup(text) TO azure_pg_admin;


--
-- Name: FUNCTION pg_replication_origin_xact_reset(); Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT ALL ON FUNCTION pg_catalog.pg_replication_origin_xact_reset() TO azure_pg_admin;


--
-- Name: FUNCTION pg_replication_origin_xact_setup(pg_lsn, timestamp with time zone); Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT ALL ON FUNCTION pg_catalog.pg_replication_origin_xact_setup(pg_lsn, timestamp with time zone) TO azure_pg_admin;


--
-- Name: FUNCTION pg_show_replication_origin_status(OUT local_id oid, OUT external_id text, OUT remote_lsn pg_lsn, OUT local_lsn pg_lsn); Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT ALL ON FUNCTION pg_catalog.pg_show_replication_origin_status(OUT local_id oid, OUT external_id text, OUT remote_lsn pg_lsn, OUT local_lsn pg_lsn) TO azure_pg_admin;


--
-- Name: FUNCTION pg_stat_reset(); Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT ALL ON FUNCTION pg_catalog.pg_stat_reset() TO azure_pg_admin;


--
-- Name: FUNCTION pg_stat_reset_shared(target text); Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT ALL ON FUNCTION pg_catalog.pg_stat_reset_shared(target text) TO azure_pg_admin;


--
-- Name: FUNCTION pg_stat_reset_single_function_counters(oid); Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT ALL ON FUNCTION pg_catalog.pg_stat_reset_single_function_counters(oid) TO azure_pg_admin;


--
-- Name: FUNCTION pg_stat_reset_single_table_counters(oid); Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT ALL ON FUNCTION pg_catalog.pg_stat_reset_single_table_counters(oid) TO azure_pg_admin;


--
-- Name: COLUMN pg_config.name; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(name) ON TABLE pg_catalog.pg_config TO azure_pg_admin;


--
-- Name: COLUMN pg_config.setting; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(setting) ON TABLE pg_catalog.pg_config TO azure_pg_admin;


--
-- Name: COLUMN pg_hba_file_rules.line_number; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(line_number) ON TABLE pg_catalog.pg_hba_file_rules TO azure_pg_admin;


--
-- Name: COLUMN pg_hba_file_rules.type; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(type) ON TABLE pg_catalog.pg_hba_file_rules TO azure_pg_admin;


--
-- Name: COLUMN pg_hba_file_rules.database; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(database) ON TABLE pg_catalog.pg_hba_file_rules TO azure_pg_admin;


--
-- Name: COLUMN pg_hba_file_rules.user_name; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(user_name) ON TABLE pg_catalog.pg_hba_file_rules TO azure_pg_admin;


--
-- Name: COLUMN pg_hba_file_rules.address; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(address) ON TABLE pg_catalog.pg_hba_file_rules TO azure_pg_admin;


--
-- Name: COLUMN pg_hba_file_rules.netmask; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(netmask) ON TABLE pg_catalog.pg_hba_file_rules TO azure_pg_admin;


--
-- Name: COLUMN pg_hba_file_rules.auth_method; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(auth_method) ON TABLE pg_catalog.pg_hba_file_rules TO azure_pg_admin;


--
-- Name: COLUMN pg_hba_file_rules.options; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(options) ON TABLE pg_catalog.pg_hba_file_rules TO azure_pg_admin;


--
-- Name: COLUMN pg_hba_file_rules.error; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(error) ON TABLE pg_catalog.pg_hba_file_rules TO azure_pg_admin;


--
-- Name: COLUMN pg_replication_origin_status.local_id; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(local_id) ON TABLE pg_catalog.pg_replication_origin_status TO azure_pg_admin;


--
-- Name: COLUMN pg_replication_origin_status.external_id; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(external_id) ON TABLE pg_catalog.pg_replication_origin_status TO azure_pg_admin;


--
-- Name: COLUMN pg_replication_origin_status.remote_lsn; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(remote_lsn) ON TABLE pg_catalog.pg_replication_origin_status TO azure_pg_admin;


--
-- Name: COLUMN pg_replication_origin_status.local_lsn; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(local_lsn) ON TABLE pg_catalog.pg_replication_origin_status TO azure_pg_admin;


--
-- Name: COLUMN pg_shmem_allocations.name; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(name) ON TABLE pg_catalog.pg_shmem_allocations TO azure_pg_admin;


--
-- Name: COLUMN pg_shmem_allocations.off; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(off) ON TABLE pg_catalog.pg_shmem_allocations TO azure_pg_admin;


--
-- Name: COLUMN pg_shmem_allocations.size; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(size) ON TABLE pg_catalog.pg_shmem_allocations TO azure_pg_admin;


--
-- Name: COLUMN pg_shmem_allocations.allocated_size; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(allocated_size) ON TABLE pg_catalog.pg_shmem_allocations TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.starelid; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(starelid) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.staattnum; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(staattnum) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stainherit; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stainherit) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stanullfrac; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stanullfrac) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stawidth; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stawidth) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stadistinct; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stadistinct) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stakind1; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stakind1) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stakind2; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stakind2) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stakind3; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stakind3) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stakind4; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stakind4) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stakind5; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stakind5) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.staop1; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(staop1) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.staop2; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(staop2) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.staop3; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(staop3) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.staop4; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(staop4) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.staop5; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(staop5) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stacoll1; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stacoll1) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stacoll2; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stacoll2) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stacoll3; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stacoll3) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stacoll4; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stacoll4) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stacoll5; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stacoll5) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stanumbers1; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stanumbers1) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stanumbers2; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stanumbers2) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stanumbers3; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stanumbers3) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stanumbers4; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stanumbers4) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stanumbers5; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stanumbers5) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stavalues1; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stavalues1) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stavalues2; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stavalues2) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stavalues3; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stavalues3) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stavalues4; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stavalues4) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_statistic.stavalues5; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(stavalues5) ON TABLE pg_catalog.pg_statistic TO azure_pg_admin;


--
-- Name: COLUMN pg_subscription.oid; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(oid) ON TABLE pg_catalog.pg_subscription TO azure_pg_admin;


--
-- Name: COLUMN pg_subscription.subdbid; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(subdbid) ON TABLE pg_catalog.pg_subscription TO azure_pg_admin;


--
-- Name: COLUMN pg_subscription.subname; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(subname) ON TABLE pg_catalog.pg_subscription TO azure_pg_admin;


--
-- Name: COLUMN pg_subscription.subowner; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(subowner) ON TABLE pg_catalog.pg_subscription TO azure_pg_admin;


--
-- Name: COLUMN pg_subscription.subenabled; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(subenabled) ON TABLE pg_catalog.pg_subscription TO azure_pg_admin;


--
-- Name: COLUMN pg_subscription.subconninfo; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(subconninfo) ON TABLE pg_catalog.pg_subscription TO azure_pg_admin;


--
-- Name: COLUMN pg_subscription.subslotname; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(subslotname) ON TABLE pg_catalog.pg_subscription TO azure_pg_admin;


--
-- Name: COLUMN pg_subscription.subsynccommit; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(subsynccommit) ON TABLE pg_catalog.pg_subscription TO azure_pg_admin;


--
-- Name: COLUMN pg_subscription.subpublications; Type: ACL; Schema: pg_catalog; Owner: azuresu
--

GRANT SELECT(subpublications) ON TABLE pg_catalog.pg_subscription TO azure_pg_admin;


--
-- PostgreSQL database dump complete
--

