--
-- PostgreSQL database dump
--

\restrict ThIkomKOgptyb5DuadqdZbXCuctDeOhtTakvDEaQfBt1WPOTN6FovlbLpUWVZxm

-- Dumped from database version 15.14
-- Dumped by pg_dump version 15.14

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: access_level_enum; Type: TYPE; Schema: public; Owner: dbadmin
--

CREATE TYPE public.access_level_enum AS ENUM (
    'view',
    'manage'
);


ALTER TYPE public.access_level_enum OWNER TO dbadmin;

--
-- Name: agent_status_enum; Type: TYPE; Schema: public; Owner: dbadmin
--

CREATE TYPE public.agent_status_enum AS ENUM (
    'active',
    'pending',
    'maintenance',
    'deprecated'
);


ALTER TYPE public.agent_status_enum OWNER TO dbadmin;

--
-- Name: lifecycle_status_enum; Type: TYPE; Schema: public; Owner: dbadmin
--

CREATE TYPE public.lifecycle_status_enum AS ENUM (
    'draft',
    'active',
    'deprecated'
);


ALTER TYPE public.lifecycle_status_enum OWNER TO dbadmin;

--
-- Name: link_type_enum; Type: TYPE; Schema: public; Owner: dbadmin
--

CREATE TYPE public.link_type_enum AS ENUM (
    'direct',
    'via_group'
);


ALTER TYPE public.link_type_enum OWNER TO dbadmin;

--
-- Name: severity_level_enum; Type: TYPE; Schema: public; Owner: dbadmin
--

CREATE TYPE public.severity_level_enum AS ENUM (
    'low',
    'medium',
    'high',
    'critical'
);


ALTER TYPE public.severity_level_enum OWNER TO dbadmin;

--
-- Name: user_status_enum; Type: TYPE; Schema: public; Owner: dbadmin
--

CREATE TYPE public.user_status_enum AS ENUM (
    'active',
    'suspended',
    'pending'
);


ALTER TYPE public.user_status_enum OWNER TO dbadmin;

--
-- Name: clone_policy(uuid, character varying, text, uuid); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.clone_policy(p_original_policy_id uuid, p_new_name character varying, p_new_description text, p_cloned_by_user_id uuid) RETURNS uuid
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_new_policy_id UUID;
    v_original_policy RECORD;
BEGIN
    -- Get original policy data
    SELECT * INTO v_original_policy
    FROM policies
    WHERE policy_id = p_original_policy_id
    AND deleted_at IS NULL;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Original policy not found or deleted';
    END IF;
    
    -- Create new policy as clone
    INSERT INTO policies (
        name, description, category, policy_type, definition, version,
        is_active, severity, applies_to_roles, created_by, updated_by,
        original_policy_id, cloned_from_policy_name
    ) VALUES (
        p_new_name, p_new_description, v_original_policy.category,
        v_original_policy.policy_type, v_original_policy.definition, 1,
        false, v_original_policy.severity, v_original_policy.applies_to_roles,
        p_cloned_by_user_id, p_cloned_by_user_id,
        p_original_policy_id, v_original_policy.name
    ) RETURNING policy_id INTO v_new_policy_id;
    
    -- Log the cloning event
    PERFORM log_hipaa_audit_event(
        p_cloned_by_user_id,
        'POLICY_CLONED',
        'policy',
        v_new_policy_id,
        jsonb_build_object('original_policy_id', p_original_policy_id, 'original_policy_name', v_original_policy.name),
        jsonb_build_object('new_policy_name', p_new_name, 'new_policy_id', v_new_policy_id),
        NULL, NULL, 'admin', 'policy_cloning', 'write', 'sensitive'
    );
    
    RETURN v_new_policy_id;
END;
$$;


ALTER FUNCTION public.clone_policy(p_original_policy_id uuid, p_new_name character varying, p_new_description text, p_cloned_by_user_id uuid) OWNER TO dbadmin;

--
-- Name: generate_rego_for_policy(uuid); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.generate_rego_for_policy(policy_uuid uuid) RETURNS json
    LANGUAGE plpgsql
    AS $$
DECLARE
    policy_record policies%ROWTYPE;
    template_record rego_templates%ROWTYPE;
    generated_rego TEXT;
    blob_path_var VARCHAR(500);
    result JSON;
BEGIN
    -- Get policy details
    SELECT * INTO policy_record FROM policies WHERE policy_id = policy_uuid;
    IF NOT FOUND THEN
        RETURN json_build_object('success', false, 'error', 'Policy not found');
    END IF;
    -- Get template based on policy category
    SELECT * INTO template_record FROM rego_templates WHERE policy_category = policy_record.category AND is_active = true LIMIT 1;
    IF NOT FOUND THEN
        RETURN json_build_object('success', false, 'error', 'Template not found');
    END IF;
    -- Increment version before generating Rego
    UPDATE policies SET version = version + 1 WHERE policy_id = policy_uuid RETURNING * INTO policy_record;
    -- Simulate template rendering (actual implementation should use server-side code)
    generated_rego := template_record.template_content;
    blob_path_var := CONCAT('active/policy_', policy_record.policy_id, '_', policy_record.category, '_v', policy_record.version, '.rego');
    -- Update policy with generated Rego
    UPDATE policies SET rego_code = generated_rego, blob_path = blob_path_var, rego_template_id = template_record.template_id, last_rego_generation = CURRENT_TIMESTAMP, opa_sync_status = 'generated', rego_version = policy_record.version WHERE policy_id = policy_uuid;
    -- Log operation
    --PERFORM log_rego_operation('00000000-0000-0000-0000-000000000000', json_build_object('policy_id', policy_uuid, 'action', 'generate_rego', 'status', 'success'));
    RETURN json_build_object('success', true, 'rego_preview', generated_rego, 'blob_path', blob_path_var);
END;
$$;


ALTER FUNCTION public.generate_rego_for_policy(policy_uuid uuid) OWNER TO dbadmin;

--
-- Name: get_enum_fields_for_policy_type(character varying); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.get_enum_fields_for_policy_type(p_policy_type character varying) RETURNS TABLE(field_path character varying, category_name character varying, description text)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT ec.field_path, ec.name, ec.description
    FROM enum_categories ec
    WHERE ec.policy_type = p_policy_type
    AND ec.is_active = true
    ORDER BY ec.name;
END;
$$;


ALTER FUNCTION public.get_enum_fields_for_policy_type(p_policy_type character varying) OWNER TO dbadmin;

--
-- Name: get_enum_values(character varying, character varying); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.get_enum_values(p_policy_type character varying, p_field_path character varying) RETURNS TABLE(value character varying, display_name character varying, description text)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT ev.value, ev.display_name, ev.description
    FROM enum_values ev
    JOIN enum_categories ec ON ev.category_id = ec.category_id
    WHERE ec.policy_type = p_policy_type 
    AND ec.field_path = p_field_path
    AND ec.is_active = true 
    AND ev.is_active = true
    ORDER BY ev.sort_order, ev.value;
END;
$$;


ALTER FUNCTION public.get_enum_values(p_policy_type character varying, p_field_path character varying) OWNER TO dbadmin;

--
-- Name: get_policy_template_by_category(character varying); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.get_policy_template_by_category(p_category character varying) RETURNS TABLE(template_id uuid, name character varying, description text, template_definition jsonb)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT pt.template_id, pt.name, pt.description, pt.template_definition
    FROM policy_templates pt
    WHERE pt.category = p_category
    AND pt.is_system_template = true
    ORDER BY pt.created_at DESC
    LIMIT 1;
END;
$$;


ALTER FUNCTION public.get_policy_template_by_category(p_category character varying) OWNER TO dbadmin;

--
-- Name: integration_enqueue_event(text, jsonb); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.integration_enqueue_event(p_event_type text, p_payload jsonb) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  INSERT INTO integration_outbox (event_type, payload_json, status, attempts, next_attempt_at)
  VALUES (p_event_type, p_payload, 'pending', 0, CURRENT_TIMESTAMP);
END;
$$;


ALTER FUNCTION public.integration_enqueue_event(p_event_type text, p_payload jsonb) OWNER TO dbadmin;

--
-- Name: log_hipaa_audit_event(uuid, character varying, character varying, uuid, jsonb, jsonb, character varying, character varying, character varying, character varying, character varying, character varying); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.log_hipaa_audit_event(p_user_id uuid, p_action character varying, p_resource_type character varying, p_resource_id uuid, p_old_values jsonb, p_new_values jsonb, p_session_id character varying, p_request_id character varying, p_user_role character varying, p_resource_name character varying, p_access_level character varying, p_data_classification character varying) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO audit_log (
        user_id, action, resource_type, resource_id, old_values, new_values,
        ip_address, user_agent, timestamp, session_id, request_id, user_role,
        resource_name, access_level, data_classification
    ) VALUES (
        p_user_id, p_action, p_resource_type, p_resource_id, p_old_values, p_new_values,
        inet_client_addr(), current_setting('application_name', true), CURRENT_TIMESTAMP,
        p_session_id, p_request_id, p_user_role, p_resource_name, p_access_level, p_data_classification
    );
END;
$$;


ALTER FUNCTION public.log_hipaa_audit_event(p_user_id uuid, p_action character varying, p_resource_type character varying, p_resource_id uuid, p_old_values jsonb, p_new_values jsonb, p_session_id character varying, p_request_id character varying, p_user_role character varying, p_resource_name character varying, p_access_level character varying, p_data_classification character varying) OWNER TO dbadmin;

--
-- Name: log_rego_operation(uuid, jsonb); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.log_rego_operation(user_uuid uuid, operation_details jsonb) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO audit_log (user_id, action, resource_type, resource_id, new_values, timestamp)
    VALUES (user_uuid, 'policy_rego', 'policy_rego', operation_details->>'policy_id', operation_details, CURRENT_TIMESTAMP);
END;
$$;


ALTER FUNCTION public.log_rego_operation(user_uuid uuid, operation_details jsonb) OWNER TO dbadmin;

--
-- Name: rollback_rego_generation(uuid); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.rollback_rego_generation(policy_uuid uuid) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
BEGIN
    UPDATE policies SET rego_code = NULL, blob_path = NULL, rego_template_id = NULL, last_rego_generation = NULL, opa_sync_status = 'pending', rego_generation_error = NULL WHERE policy_id = policy_uuid;
    PERFORM log_rego_operation('00000000-0000-0000-0000-000000000000', json_build_object('policy_id', policy_uuid, 'action', 'rollback_rego', 'status', 'success'));
    RETURN FOUND;
END;
$$;


ALTER FUNCTION public.rollback_rego_generation(policy_uuid uuid) OWNER TO dbadmin;

--
-- Name: search_policies(character varying, character varying, character varying, boolean, integer, integer); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.search_policies(p_search_term character varying, p_category character varying, p_severity character varying, p_is_active boolean, p_limit integer DEFAULT 20, p_offset integer DEFAULT 0) RETURNS TABLE(policy_id uuid, name character varying, description text, category character varying, severity character varying, is_active boolean, policy_type character varying, definition jsonb, applies_to_roles text[], created_at timestamp with time zone, updated_at timestamp with time zone, total_count bigint)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    WITH filtered_policies AS (
        SELECT p.*,
               COUNT(*) OVER() as total_count
        FROM policies p
        WHERE p.deleted_at IS NULL
        AND (p_search_term IS NULL OR 
             p.name ILIKE '%' || p_search_term || '%' OR 
             p.description ILIKE '%' || p_search_term || '%')
        AND (p_category IS NULL OR p.category = p_category)
        AND (p_severity IS NULL OR p.severity = p_severity)
        AND (p_is_active IS NULL OR p.is_active = p_is_active)
    )
    SELECT fp.policy_id, fp.name, fp.description, fp.category, fp.severity,
           fp.is_active, fp.policy_type, fp.definition, fp.applies_to_roles,
           fp.created_at, fp.updated_at, fp.total_count
    FROM filtered_policies fp
    ORDER BY fp.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$;


ALTER FUNCTION public.search_policies(p_search_term character varying, p_category character varying, p_severity character varying, p_is_active boolean, p_limit integer, p_offset integer) OWNER TO dbadmin;

--
-- Name: trg_agents_enqueue(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.trg_agents_enqueue() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'agent.created';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','agent','resource_id', NEW.agent_id),
      'data', jsonb_build_object('change_type','created')
    );
  ELSIF TG_OP = 'UPDATE' THEN
    v_event := 'agent.updated';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','agent','resource_id', NEW.agent_id),
      'data', jsonb_build_object('change_type','updated')
    );
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'agent.deleted';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','agent','resource_id', OLD.agent_id),
      'data', jsonb_build_object('change_type','deleted')
    );
  END IF;
  PERFORM integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$;


ALTER FUNCTION public.trg_agents_enqueue() OWNER TO dbadmin;

--
-- Name: trg_arp_enqueue(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.trg_arp_enqueue() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'assignment.linked';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','agent_role_policy','resource_id', jsonb_build_object('agent_id', NEW.agent_id, 'role_id', NEW.role_id, 'group_id', NEW.group_id, 'policy_id', NEW.policy_id)), 'data', jsonb_build_object('change_type','created'));
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'assignment.unlinked';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','agent_role_policy','resource_id', jsonb_build_object('agent_id', OLD.agent_id, 'role_id', OLD.role_id, 'group_id', OLD.group_id, 'policy_id', OLD.policy_id)), 'data', jsonb_build_object('change_type','deleted'));
  ELSE
    RETURN COALESCE(NEW, OLD);
  END IF;
  PERFORM integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$;


ALTER FUNCTION public.trg_arp_enqueue() OWNER TO dbadmin;

--
-- Name: trg_policies_enqueue(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.trg_policies_enqueue() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'policy.created';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','policy','resource_id', NEW.policy_id), 'data', jsonb_build_object('change_type','created'));
  ELSIF TG_OP = 'UPDATE' THEN
    v_event := 'policy.updated';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','policy','resource_id', NEW.policy_id), 'data', jsonb_build_object('change_type','updated'));
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'policy.deleted';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','policy','resource_id', OLD.policy_id), 'data', jsonb_build_object('change_type','deleted'));
  END IF;
  PERFORM integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$;


ALTER FUNCTION public.trg_policies_enqueue() OWNER TO dbadmin;

--
-- Name: trg_roles_enqueue(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.trg_roles_enqueue() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'role.created';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','role','resource_id', NEW.role_id), 'data', jsonb_build_object('change_type','created'));
  ELSIF TG_OP = 'UPDATE' THEN
    v_event := 'role.updated';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','role','resource_id', NEW.role_id), 'data', jsonb_build_object('change_type','updated'));
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'role.deleted';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','role','resource_id', OLD.role_id), 'data', jsonb_build_object('change_type','deleted'));
  END IF;
  PERFORM integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$;


ALTER FUNCTION public.trg_roles_enqueue() OWNER TO dbadmin;

--
-- Name: update_enum_categories_updated_at(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.update_enum_categories_updated_at() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_enum_categories_updated_at() OWNER TO dbadmin;

--
-- Name: update_enum_values_updated_at(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.update_enum_values_updated_at() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_enum_values_updated_at() OWNER TO dbadmin;

--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_updated_at_column() OWNER TO dbadmin;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: agent_access; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.agent_access (
    agent_id uuid NOT NULL,
    role_id uuid NOT NULL,
    access_level public.access_level_enum DEFAULT 'view'::public.access_level_enum,
    granted_by uuid
);


ALTER TABLE public.agent_access OWNER TO dbadmin;

--
-- Name: agent_policies; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.agent_policies (
    agent_id uuid NOT NULL,
    policy_id uuid NOT NULL,
    link_type public.link_type_enum NOT NULL
);


ALTER TABLE public.agent_policies OWNER TO dbadmin;

--
-- Name: agent_role_policies; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.agent_role_policies (
    agent_id uuid NOT NULL,
    role_id uuid NOT NULL,
    group_id uuid NOT NULL,
    policy_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.agent_role_policies OWNER TO dbadmin;

--
-- Name: agents; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.agents (
    agent_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    agent_type character varying(100) DEFAULT 'policy_engine'::character varying,
    endpoint_url character varying(500),
    is_active boolean DEFAULT true,
    configuration jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    vendor character varying(255),
    department character varying(255),
    risk_score numeric(4,2) DEFAULT 0.0,
    status public.agent_status_enum DEFAULT 'active'::public.agent_status_enum,
    deleted_at timestamp with time zone
);


ALTER TABLE public.agents OWNER TO dbadmin;

--
-- Name: alembic_version; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.alembic_version (
    version_num character varying(32) NOT NULL
);


ALTER TABLE public.alembic_version OWNER TO dbadmin;

--
-- Name: audit_log; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.audit_log (
    log_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid,
    action character varying(100) NOT NULL,
    resource_type character varying(100),
    resource_id uuid,
    old_values jsonb,
    new_values jsonb,
    ip_address inet,
    user_agent text,
    "timestamp" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    session_id character varying(255),
    request_id character varying(255),
    user_role character varying(50),
    resource_name character varying(255),
    access_level character varying(50),
    data_classification character varying(50)
);


ALTER TABLE public.audit_log OWNER TO dbadmin;

--
-- Name: chat_messages; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.chat_messages (
    message_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    session_id uuid,
    role character varying(20) NOT NULL,
    content text NOT NULL,
    original_content text,
    policies_applied uuid[] DEFAULT '{}'::uuid[],
    is_filtered boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.chat_messages OWNER TO dbadmin;

--
-- Name: dataset_entries; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.dataset_entries (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    dataset_id uuid,
    test_case_type character varying(20) NOT NULL,
    input jsonb NOT NULL,
    expected_output text,
    context text,
    retrieval_context text[],
    tools_called text[],
    expected_outcome character varying(50),
    scenario text,
    initial_context text,
    entry_order integer DEFAULT 0 NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.dataset_entries OWNER TO dbadmin;

--
-- Name: datasets; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.datasets (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    type character varying(50) DEFAULT 'custom'::character varying NOT NULL,
    status character varying(20) DEFAULT 'active'::character varying NOT NULL,
    data jsonb DEFAULT '[]'::jsonb NOT NULL,
    record_count integer DEFAULT 0 NOT NULL,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.datasets OWNER TO dbadmin;

--
-- Name: documents; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.documents (
    document_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    title character varying(500) NOT NULL,
    content text,
    document_type character varying(100),
    metadata jsonb,
    file_path character varying(1000),
    is_sensitive boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    updated_by uuid
);


ALTER TABLE public.documents OWNER TO dbadmin;

--
-- Name: enum_categories; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.enum_categories (
    category_id integer NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    policy_type character varying(50) NOT NULL,
    field_path character varying(200) NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.enum_categories OWNER TO dbadmin;

--
-- Name: enum_categories_category_id_seq; Type: SEQUENCE; Schema: public; Owner: dbadmin
--

CREATE SEQUENCE public.enum_categories_category_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.enum_categories_category_id_seq OWNER TO dbadmin;

--
-- Name: enum_categories_category_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dbadmin
--

ALTER SEQUENCE public.enum_categories_category_id_seq OWNED BY public.enum_categories.category_id;


--
-- Name: enum_values; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.enum_values (
    value_id integer NOT NULL,
    category_id integer,
    value character varying(200) NOT NULL,
    display_name character varying(200),
    description text,
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.enum_values OWNER TO dbadmin;

--
-- Name: enum_values_value_id_seq; Type: SEQUENCE; Schema: public; Owner: dbadmin
--

CREATE SEQUENCE public.enum_values_value_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.enum_values_value_id_seq OWNER TO dbadmin;

--
-- Name: enum_values_value_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dbadmin
--

ALTER SEQUENCE public.enum_values_value_id_seq OWNED BY public.enum_values.value_id;


--
-- Name: evaluation_metrics; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.evaluation_metrics (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    category character varying(50),
    implementation_type character varying(50),
    config jsonb,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.evaluation_metrics OWNER TO dbadmin;

--
-- Name: evaluations; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.evaluations (
    id integer NOT NULL,
    evaluation_id character varying(255) NOT NULL,
    experiment_id uuid NOT NULL,
    experiment_name character varying(255),
    agent_name character varying(255),
    dataset_name character varying(255),
    evaluation_type character varying(50),
    status character varying(50) DEFAULT 'pending'::character varying,
    evaluations jsonb,
    summary jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    completed_at timestamp without time zone
);


ALTER TABLE public.evaluations OWNER TO dbadmin;

--
-- Name: evaluations_id_seq; Type: SEQUENCE; Schema: public; Owner: dbadmin
--

CREATE SEQUENCE public.evaluations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.evaluations_id_seq OWNER TO dbadmin;

--
-- Name: evaluations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dbadmin
--

ALTER SEQUENCE public.evaluations_id_seq OWNED BY public.evaluations.id;


--
-- Name: experiment_evaluations; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.experiment_evaluations (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    experiment_id uuid NOT NULL,
    metric_id uuid NOT NULL,
    status character varying(20) DEFAULT 'pending'::character varying,
    score numeric(5,2),
    details jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.experiment_evaluations OWNER TO dbadmin;

--
-- Name: experiments; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.experiments (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    dataset_id uuid,
    agent_config jsonb,
    execution_mode character varying(20) DEFAULT 'automated'::character varying NOT NULL,
    status character varying(20) DEFAULT 'pending'::character varying NOT NULL,
    progress integer DEFAULT 0 NOT NULL,
    started_at timestamp with time zone,
    completed_at timestamp with time zone,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.experiments OWNER TO dbadmin;

--
-- Name: guardrail_services; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.guardrail_services (
    id uuid NOT NULL,
    service_id character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    version character varying(50) DEFAULT '1.0.0'::character varying NOT NULL,
    type character varying(50) DEFAULT 'modifier'::character varying NOT NULL,
    endpoint character varying(500) NOT NULL,
    health_check_path character varying(255) DEFAULT '/health'::character varying,
    timeout_ms integer DEFAULT 100,
    capabilities character varying[] DEFAULT '{}'::character varying[],
    supported_content_types character varying[] DEFAULT '{text/plain,application/json}'::character varying[],
    status character varying(50) DEFAULT 'unknown'::character varying,
    last_health_check timestamp with time zone,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.guardrail_services OWNER TO dbadmin;

--
-- Name: integration_dlq; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.integration_dlq (
    id uuid NOT NULL,
    tenant_id uuid,
    destination text NOT NULL,
    event_type text NOT NULL,
    event_version integer NOT NULL,
    payload_json jsonb NOT NULL,
    attempts integer NOT NULL,
    last_error text,
    dead_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone
);


ALTER TABLE public.integration_dlq OWNER TO dbadmin;

--
-- Name: integration_outbox; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.integration_outbox (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    tenant_id uuid,
    destination text DEFAULT 'webhook'::text NOT NULL,
    event_type text NOT NULL,
    event_version integer DEFAULT 1 NOT NULL,
    payload_json jsonb NOT NULL,
    status text DEFAULT 'pending'::text NOT NULL,
    attempts integer DEFAULT 0 NOT NULL,
    next_attempt_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    last_error text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.integration_outbox OWNER TO dbadmin;

--
-- Name: mcp_chat_sessions; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.mcp_chat_sessions (
    session_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid,
    status character varying(20) DEFAULT 'active'::character varying,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    metadata jsonb DEFAULT '{}'::jsonb
);


ALTER TABLE public.mcp_chat_sessions OWNER TO dbadmin;

--
-- Name: mcp_flow_steps; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.mcp_flow_steps (
    step_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    session_id uuid,
    step_number integer NOT NULL,
    step_name character varying(100) NOT NULL,
    status character varying(20) DEFAULT 'pending'::character varying,
    input_data jsonb,
    output_data jsonb,
    processing_time_ms integer,
    policies_applied uuid[] DEFAULT '{}'::uuid[],
    violations_detected uuid[] DEFAULT '{}'::uuid[],
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    completed_at timestamp with time zone
);


ALTER TABLE public.mcp_flow_steps OWNER TO dbadmin;

--
-- Name: openai_api_calls; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.openai_api_calls (
    call_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    session_id uuid,
    step_id uuid,
    model_name character varying(50),
    prompt_tokens integer,
    completion_tokens integer,
    total_tokens integer,
    cost_estimate numeric(10,6),
    response_time_ms integer,
    status_code integer,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.openai_api_calls OWNER TO dbadmin;

--
-- Name: policies; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policies (
    policy_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    category character varying(100) NOT NULL,
    policy_type character varying(50) DEFAULT 'opa'::character varying,
    definition jsonb NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    is_active boolean DEFAULT false,
    severity character varying(20) DEFAULT 'medium'::character varying,
    applies_to_roles text[],
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    updated_by uuid,
    rego_code text,
    blob_container character varying(255) DEFAULT 'rego-policies'::character varying,
    blob_path character varying(500),
    blob_url character varying(1000),
    rego_template_id character varying(100),
    opa_sync_status character varying(50) DEFAULT 'pending'::character varying,
    last_rego_generation timestamp with time zone,
    rego_generation_error text,
    rego_version integer DEFAULT 1,
    original_policy_id uuid,
    cloned_from_policy_name character varying(255),
    deleted_at timestamp with time zone,
    guardrail_id uuid
);


ALTER TABLE public.policies OWNER TO dbadmin;

--
-- Name: policy_executions; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policy_executions (
    execution_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    session_id uuid,
    step_id uuid,
    policy_id uuid,
    execution_status character varying(20),
    input_data jsonb,
    output_data jsonb,
    execution_time_ms integer,
    error_message text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.policy_executions OWNER TO dbadmin;

--
-- Name: policy_group_policies; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policy_group_policies (
    group_id uuid NOT NULL,
    policy_id uuid NOT NULL
);


ALTER TABLE public.policy_group_policies OWNER TO dbadmin;

--
-- Name: policy_groups; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policy_groups (
    group_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    is_template boolean DEFAULT false,
    severity public.severity_level_enum DEFAULT 'medium'::public.severity_level_enum,
    status public.lifecycle_status_enum DEFAULT 'active'::public.lifecycle_status_enum,
    version character varying(20) DEFAULT 'v1.0.0'::character varying,
    tags text[] DEFAULT '{}'::text[],
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    deleted_at timestamp with time zone
);


ALTER TABLE public.policy_groups OWNER TO dbadmin;

--
-- Name: policy_schemas; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policy_schemas (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    schema_name character varying(255) NOT NULL,
    schema_content jsonb NOT NULL,
    description text,
    guardrail_id uuid,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.policy_schemas OWNER TO dbadmin;

--
-- Name: policy_templates; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policy_templates (
    template_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    category character varying(100) NOT NULL,
    template_definition jsonb NOT NULL,
    is_system_template boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    guardrail_id character varying(255)
);


ALTER TABLE public.policy_templates OWNER TO dbadmin;

--
-- Name: policy_violations; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policy_violations (
    violation_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    policy_id uuid,
    user_id uuid,
    violation_type character varying(100) NOT NULL,
    details jsonb,
    severity character varying(20),
    resolved boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    resolved_at timestamp with time zone,
    resolved_by uuid
);


ALTER TABLE public.policy_violations OWNER TO dbadmin;

--
-- Name: rego_templates; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.rego_templates (
    template_id character varying(100) NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    policy_category character varying(100) NOT NULL,
    template_content text NOT NULL,
    variables jsonb DEFAULT '[]'::jsonb,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid
);


ALTER TABLE public.rego_templates OWNER TO dbadmin;

--
-- Name: roles; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.roles (
    role_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    code character varying(100) NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    is_system_role boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    permissions text[] DEFAULT '{}'::text[]
);


ALTER TABLE public.roles OWNER TO dbadmin;

--
-- Name: system_metrics; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.system_metrics (
    metric_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    metric_type character varying(100) NOT NULL,
    metric_name character varying(255) NOT NULL,
    metric_value numeric(15,4),
    dimensions jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.system_metrics OWNER TO dbadmin;

--
-- Name: test_results; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.test_results (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    experiment_id uuid,
    dataset_entry_id uuid,
    test_case_type character varying(20) NOT NULL,
    input jsonb NOT NULL,
    expected_output text,
    actual_output text NOT NULL,
    context text,
    retrieval_context text[],
    tools_called text[],
    expected_outcome character varying(50),
    scenario text,
    status character varying(20) DEFAULT 'pending'::character varying NOT NULL,
    score numeric(5,2),
    latency_ms integer,
    token_count integer,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.test_results OWNER TO dbadmin;

--
-- Name: user_roles; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_roles (
    user_id uuid NOT NULL,
    role_id uuid NOT NULL
);


ALTER TABLE public.user_roles OWNER TO dbadmin;

--
-- Name: users; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.users (
    user_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    azure_ad_id character varying(255) NOT NULL,
    email character varying(255) NOT NULL,
    first_name character varying(100),
    last_name character varying(100),
    role character varying(50) DEFAULT 'user'::character varying NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    updated_by uuid,
    department character varying(255),
    status public.user_status_enum DEFAULT 'active'::public.user_status_enum,
    risk_score numeric(4,2) DEFAULT 0.0,
    last_login timestamp with time zone,
    two_factor_enabled boolean DEFAULT false
);


ALTER TABLE public.users OWNER TO dbadmin;

--
-- Name: enum_categories category_id; Type: DEFAULT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.enum_categories ALTER COLUMN category_id SET DEFAULT nextval('public.enum_categories_category_id_seq'::regclass);


--
-- Name: enum_values value_id; Type: DEFAULT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.enum_values ALTER COLUMN value_id SET DEFAULT nextval('public.enum_values_value_id_seq'::regclass);


--
-- Name: evaluations id; Type: DEFAULT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.evaluations ALTER COLUMN id SET DEFAULT nextval('public.evaluations_id_seq'::regclass);


--
-- Data for Name: agent_access; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.agent_access (agent_id, role_id, access_level, granted_by) FROM stdin;
c1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-111111111111	manage	a1b2c3d4-e5f6-7890-abcd-111111111111
c1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-222222222222	view	a1b2c3d4-e5f6-7890-abcd-111111111111
c1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-333333333333	manage	a1b2c3d4-e5f6-7890-abcd-111111111111
c1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-444444444444	view	a1b2c3d4-e5f6-7890-abcd-111111111111
c1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-555555555555	manage	a1b2c3d4-e5f6-7890-abcd-111111111111
\.


--
-- Data for Name: agent_policies; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.agent_policies (agent_id, policy_id, link_type) FROM stdin;
c1b2c3d4-e5f6-7890-abcd-111111111111	f1b2c3d4-e5f6-7890-abcd-111111111111	via_group
c1b2c3d4-e5f6-7890-abcd-111111111111	f1b2c3d4-e5f6-7890-abcd-222222222222	via_group
c1b2c3d4-e5f6-7890-abcd-111111111111	f1b2c3d4-e5f6-7890-abcd-333333333333	via_group
\.


--
-- Data for Name: agent_role_policies; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.agent_role_policies (agent_id, role_id, group_id, policy_id, created_at) FROM stdin;
00033e45-2454-42cc-b247-0c6e3d4e7e06	b1b2c3d4-e5f6-7890-abcd-111111111111	d1b2c3d4-e5f6-7890-abcd-111111111111	bd7363f8-6104-449e-873a-db0539f0fa17	2025-08-27 18:11:42.061408+00
00033e45-2454-42cc-b247-0c6e3d4e7e06	b1b2c3d4-e5f6-7890-abcd-222222222222	d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d	2025-08-27 22:05:45.835249+00
00033e45-2454-42cc-b247-0c6e3d4e7e06	b1b2c3d4-e5f6-7890-abcd-444444444444	d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	62124064-1825-4bfa-99be-580b1a1c625d	2025-08-28 04:40:13.324233+00
c1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-444444444444	d1b2c3d4-e5f6-7890-abcd-111111111111	bd7363f8-6104-449e-873a-db0539f0fa17	2025-08-28 05:05:26.389862+00
c1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-444444444444	d1b2c3d4-e5f6-7890-abcd-111111111111	759c48a8-4e86-4430-8da0-73973abba594	2025-08-28 05:14:04.919087+00
c1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-111111111111	d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	5c36b6fd-c0ce-40e1-9681-75c2d500d588	2025-08-28 05:14:25.257781+00
00033e45-2454-42cc-b247-0c6e3d4e7e06	b1b2c3d4-e5f6-7890-abcd-444444444444	d1b2c3d4-e5f6-7890-abcd-111111111111	9b3d812f-bde0-43e9-a211-5d48379f1320	2025-08-28 05:22:45.41843+00
\.


--
-- Data for Name: agents; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.agents (agent_id, name, description, agent_type, endpoint_url, is_active, configuration, created_at, updated_at, created_by, vendor, department, risk_score, status, deleted_at) FROM stdin;
00033e45-2454-42cc-b247-0c6e3d4e7e06	a1	a1	data_pipeline	\N	t	{}	2025-08-27 18:11:28.981122+00	2025-08-27 18:11:28.981122+00	00000000-0000-0000-0000-000000000000	v1	d1	0.00	active	\N
1b126746-792f-4a37-b052-537962196a48	Test Medical Agent	Test agent for medical privacy policy	policy_engine	\N	t	{}	2025-08-27 22:01:47.268285+00	2025-08-27 22:01:47.268285+00	00000000-0000-0000-0000-000000000000	\N	\N	0.00	active	\N
18c5cf42-b62f-4242-9435-738cc9157755	Demo Test Agent **********	Test agent for demo flow verification	policy_engine	\N	t	{}	2025-08-28 04:30:10.192057+00	2025-08-28 04:30:10.192057+00	00000000-0000-0000-0000-000000000000	\N	\N	0.00	active	\N
c1b2c3d4-e5f6-7890-abcd-111111111111	BCBS HIPAA Compliance Agent test	AI agent specialized in HIPAA compliance monitoring and PHI protection test	healthcare_hipaa_compliance	\N	t	{"auto_log_access": true, "audit_all_access": true, "redaction_enabled": true, "compliance_version": "HIPAA_2023", "emergency_override": true, "require_two_factor": false, "alert_on_violations": true, "max_session_duration": 3600, "supported_redaction_types": ["ssn", "phone", "address", "email", "dob", "insurance_id"]}	2025-08-27 17:57:54.32967+00	2025-08-28 05:08:06.220713+00	a1b2c3d4-e5f6-7890-abcd-111111111111	BCBS Blue Cross	Privacy & Compliance	0.20	active	\N
\.


--
-- Data for Name: alembic_version; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.alembic_version (version_num) FROM stdin;
\.


--
-- Data for Name: audit_log; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.audit_log (log_id, user_id, action, resource_type, resource_id, old_values, new_values, ip_address, user_agent, "timestamp", session_id, request_id, user_role, resource_name, access_level, data_classification) FROM stdin;
45633e22-7876-4d26-a4c2-678ae42d558a	00000000-0000-0000-0000-000000000000	schema_creation	database	\N	\N	{"update": "Complete database schema created from consolidated migration files"}	\N	\N	2025-08-27 17:57:54.285266+00	\N	\N	\N	\N	\N	\N
3735f69f-327f-4a82-abd4-fd32b2f252c9	************************************	HIPAA_PHI_ACCESS	patient_record	7f18997e-873f-4a81-a866-e41d2830b227	\N	{"access_type": "read", "fields_accessed": ["demographics", "medical_history"], "redaction_applied": true}	*************	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36	2025-08-27 15:57:54.32967+00	\N	\N	\N	\N	\N	\N
4f64540f-8b2c-4347-b4a2-c432b36fca49	a1b2c3d4-e5f6-7890-abcd-333333333333	HIPAA_POLICY_EXECUTION	policy	f1b2c3d4-e5f6-7890-abcd-111111111111	\N	{"redaction_count": 3, "compliance_score": 95, "execution_result": "allow"}	*************	Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36	2025-08-27 16:57:54.32967+00	\N	\N	\N	\N	\N	\N
7216d0c4-fbea-42a6-bd3b-3538de460378	a1b2c3d4-e5f6-7890-abcd-222222222222	HIPAA_RECORD_SHARING	medical_record	4fcd6ab2-b295-47ca-bde8-6be4d7802052	\N	{"recipient": "external_specialist", "sharing_method": "secure_portal", "redaction_applied": true}	*************	Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0	2025-08-27 14:57:54.32967+00	\N	\N	\N	\N	\N	\N
0de24c4a-b5c9-4707-ab14-8d368e065122	a1b2c3d4-e5f6-7890-abcd-555555555555	HIPAA_CONSENT_UPDATE	patient_consent	2f32307c-edfb-4772-a124-66c59ba5c397	\N	{"consent_types": ["treatment", "payment", "operations"], "consent_status": "granted"}	*************	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36	2025-08-27 13:57:54.32967+00	\N	\N	\N	\N	\N	\N
f94160a8-d6b9-4333-bd7d-d72f6feace66	************************************	HIPAA_FAILED_LOGIN	user_session	\N	\N	{"source_ip": "*************", "attempt_count": 2, "failure_reason": "invalid_password"}	*************	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36	2025-08-27 12:57:54.32967+00	\N	\N	\N	\N	\N	\N
b367ecb0-c5cc-46ed-a4ba-3ec34aa66de8	a1b2c3d4-e5f6-7890-abcd-333333333333	HIPAA_AUDIT_REPORT	system_report	0888dc4c-ee8c-4433-b6a4-d428b58c1a44	\N	{"report_type": "monthly_access", "records_count": 1247, "violations_found": 0}	*************	Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36	2025-08-27 11:57:54.32967+00	\N	\N	\N	\N	\N	\N
e2e2e897-20b6-4361-b8da-e673991d8457	a1b2c3d4-e5f6-7890-abcd-222222222222	HIPAA_EMERGENCY_OVERRIDE	policy_execution	f1b2c3d4-e5f6-7890-abcd-222222222222	\N	{"patient_id": "12345", "override_reason": "medical_emergency", "redaction_enabled": false}	*************	Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0	2025-08-27 09:57:54.32967+00	\N	\N	\N	\N	\N	\N
408e783d-3d54-4fe8-866c-6a31bad6fe59	a1b2c3d4-e5f6-7890-abcd-333333333333	HIPAA_THIRD_PARTY_REQUEST	data_sharing	7ccab097-6a1f-4154-8b65-7f2e9fee1c99	\N	{"baa_status": "verified", "data_types": ["lab_results", "imaging"], "requesting_entity": "Mayo Clinic"}	*************	Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36	2025-08-27 05:57:54.32967+00	\N	\N	\N	\N	\N	\N
aa7077bf-7295-4533-9ceb-8acd4f766bb2	a1b2c3d4-e5f6-7890-abcd-555555555555	HIPAA_PATIENT_ACCESS_REQUEST	patient_request	1f14246c-3936-4e67-b2b4-7e3d655c02c9	\N	{"request_type": "medical_records", "delivery_method": "electronic", "patient_verified": true}	*************	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36	2025-08-26 17:57:54.32967+00	\N	\N	\N	\N	\N	\N
3c1462b0-de84-4fa3-8783-14001819dc8f	a1b2c3d4-e5f6-7890-abcd-111111111111	HIPAA_CONFIG_CHANGE	system_config	9af31731-2d43-4370-ab5b-b16dbd8fa74d	\N	{"redaction_timeout": 3600}	************	Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36	2025-08-25 17:57:54.32967+00	\N	\N	\N	\N	\N	\N
5180199d-4609-4945-ad6d-eb9e7f1c8f31	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	bd7363f8-6104-449e-873a-db0539f0fa17	\N	{"name": "p1", "category": "data_privacy", "severity": "medium", "is_active": true}	**********		2025-08-27 18:11:04.650915+00	\N	\N	admin	policy_creation	write	sensitive
c5611c26-1296-4a31-b7b4-4a30af001009	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	759c48a8-4e86-4430-8da0-73973abba594	\N	{"name": "p2", "category": "access_control", "severity": "low", "is_active": true}	**********		2025-08-27 20:07:59.80883+00	\N	\N	admin	policy_creation	write	sensitive
eff3a986-775a-4f88-a3aa-513a60a6152f	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	4855b086-ab50-4488-9094-58f0be3f2cf3	\N	{"name": "Test Medical Privacy Policy", "category": "medical_privacy", "severity": "high"}	**********		2025-08-27 21:38:06.078962+00	\N	\N	admin	policy_creation	write	sensitive
1426512f-2c79-4f10-a185-ccf675374438	00000000-0000-0000-0000-000000000000	CREATE	policy_group	d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	\N	{"name": "Test Medical Group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf", "severity": "medium", "created_at": "2025-08-27T22:01:09.725Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-27T22:01:09.725Z", "description": "Test policy group for medical privacy policies", "is_template": false}	**********		2025-08-27 22:01:09.729771+00	\N	\N	system	create	write	sensitive
085a5c4d-e992-48eb-9613-a87cb5d54b66	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	\N	{"addedPolicyIds": ["4855b086-ab50-4488-9094-58f0be3f2cf3"]}	**********		2025-08-27 22:01:28.858032+00	\N	\N	system	add_policy	write	sensitive
6c817782-92eb-493f-829f-07c8802d4375	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	9b3d812f-bde0-43e9-a211-5d48379f1320	\N	{"name": "p3", "category": "medical_privacy", "severity": "medium", "is_active": true}	**********		2025-08-27 22:01:37.884014+00	\N	\N	admin	policy_creation	write	sensitive
720b4a8a-0172-4162-8a11-4323d7b5f751	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d	\N	{"name": "p4", "category": "access_control", "severity": "low", "is_active": true}	**********		2025-08-27 22:02:54.832003+00	\N	\N	admin	policy_creation	write	sensitive
5952df59-3fc2-4612-be6a-2cd76780e883	00000000-0000-0000-0000-000000000000	CREATE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	{"name": "PI Bundle", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "11a01996-cb91-40ef-88c1-8e71328ca7f4", "severity": "medium", "created_at": "2025-08-27T22:04:32.761Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-27T22:04:32.761Z", "description": "PI Bun", "is_template": true}	**********		2025-08-27 22:04:32.766584+00	\N	\N	system	create	write	sensitive
3aff4aef-a9cb-4336-9198-f2a00a9fbf00	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	00accc7f-7fd6-42cd-acbd-e9016b48eaec	\N	{"name": "Test Database Schema Policy", "category": "Access Control", "severity": "medium"}	**********		2025-08-27 22:17:01.527401+00	\N	\N	admin	policy_creation	write	sensitive
a9315c67-b720-4489-9d65-9f300f8743d7	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	3c7b0cb1-0c84-4c64-8ebb-bedc7c262e53	\N	{"name": "Test DB Schema Policy 1756333061", "category": "Access Control", "severity": "medium"}	**********		2025-08-27 22:17:41.282415+00	\N	\N	admin	policy_creation	write	sensitive
9cbf9a2f-5d60-464f-80a5-9739679044f3	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	f4c1a348-7f94-4ed5-908c-4aee04e82f37	\N	{"name": "Test DB Schema Policy 1756333083", "category": "Access Control", "severity": "medium"}	**********		2025-08-27 22:18:03.521453+00	\N	\N	admin	policy_creation	write	sensitive
ff5050b3-e43e-4fda-b7ab-9187bf579bde	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	5c36b6fd-c0ce-40e1-9681-75c2d500d588	\N	{"name": "Test Fix Policy **********", "category": "Data Privacy", "severity": "high"}	**********		2025-08-27 22:30:11.912441+00	\N	\N	admin	policy_creation	write	sensitive
3951d83b-db58-4cf8-a6fc-18fdefb1db83	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	62124064-1825-4bfa-99be-580b1a1c625d	\N	{"name": "p5", "category": "access_control", "severity": "medium", "is_active": true}	**********		2025-08-27 22:30:58.628141+00	\N	\N	admin	policy_creation	write	sensitive
75d724d0-e014-4e7e-aba8-e77ff377b087	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	\N	{"name": "p6", "category": "compliance", "severity": "medium", "is_active": true}	**********		2025-08-28 04:17:49.447202+00	\N	\N	admin	policy_creation	write	sensitive
e79664c7-15da-4094-a252-a5afedddaaad	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	6123f679-d34c-4b99-abea-46249bd69db1	\N	\N	**********		2025-08-28 20:35:22.678795+00	\N	\N	system	role_deleted	write	sensitive
1b8fa7d0-b5aa-4564-85ac-6f44ecdd6054	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	b82ce312-7cda-43a6-b3ef-a97b58e19e17	\N	\N	**********		2025-08-28 20:35:22.682002+00	\N	\N	system	deprecate	write	sensitive
a760edde-59f6-4f71-be4e-a1d516e6217c	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	e1414965-9a7d-4fdb-aa06-666be8dc37bf	\N	{"name": "e2e_test_1756413348147_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:35:48.219765+00	\N	\N	admin	policy_creation	write	sensitive
cb37fc59-6ea6-4423-af48-5e1c3cfc43e3	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	5c36b6fd-c0ce-40e1-9681-75c2d500d588	{"name": "Test Fix Policy **********", "version": 1, "blob_url": null, "category": "Data Privacy", "severity": "high", "blob_path": null, "is_active": null, "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "rego_code": null, "created_at": "2025-08-27T22:30:11.908Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-27T22:30:11.908Z", "updated_by": null, "description": "Testing after fixing JsonStructureGuide", "policy_type": "data_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Test Fix Policy **********", "version": 1, "blob_url": null, "category": "Data Privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "rego_code": null, "created_at": "2025-08-27T22:30:11.908Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-28T03:45:18.454Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing after fixing JsonStructureGuide", "policy_type": "data_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 03:45:18.465335+00	\N	\N	admin	policy_update	write	sensitive
53856f06-01f6-4ec9-a816-feba5a6191c7	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	5c36b6fd-c0ce-40e1-9681-75c2d500d588	{"name": "Test Fix Policy **********", "version": 1, "blob_url": null, "category": "Data Privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "rego_code": null, "created_at": "2025-08-27T22:30:11.908Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-28T03:45:18.454Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing after fixing JsonStructureGuide", "policy_type": "data_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Test Fix Policy **********", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "rego_code": null, "created_at": "2025-08-27T22:30:11.908Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-28T03:45:51.355Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing after fixing JsonStructureGuide", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin", "manager"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 03:45:51.362784+00	\N	\N	admin	policy_update	write	sensitive
92078949-82fa-47b6-aa00-96f9ece40e55	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	5c36b6fd-c0ce-40e1-9681-75c2d500d588	{"name": "Test Fix Policy **********", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "rego_code": null, "created_at": "2025-08-27T22:30:11.908Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-28T03:45:51.355Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing after fixing JsonStructureGuide", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin", "manager"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Test Fix Policy **********", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "high", "blob_path": null, "is_active": false, "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "rego_code": null, "created_at": "2025-08-27T22:30:11.908Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": false, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-28T04:04:37.642Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing after fixing JsonStructureGuide", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin", "manager"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 04:04:37.65283+00	\N	\N	admin	policy_update	write	sensitive
dded515e-2d70-4346-bf25-f5be9921f44c	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	5c36b6fd-c0ce-40e1-9681-75c2d500d588	{"name": "Test Fix Policy **********", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "high", "blob_path": null, "is_active": false, "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "rego_code": null, "created_at": "2025-08-27T22:30:11.908Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": false, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-28T04:04:37.642Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing after fixing JsonStructureGuide", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin", "manager"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Test Fix Policy **********", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "rego_code": null, "created_at": "2025-08-27T22:30:11.908Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-28T04:04:53.021Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing after fixing JsonStructureGuide", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin", "manager"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 04:04:53.027872+00	\N	\N	admin	policy_update	write	sensitive
25b27674-dda4-4dc4-8fef-2f513c5a384d	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "medium", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "severity": "medium", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-28T04:17:49.436Z", "updated_by": null, "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-28T04:18:06.626Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 04:18:06.634313+00	\N	\N	admin	policy_update	write	sensitive
d8676a88-5883-41b6-9b85-c6a118b293d3	00000000-0000-0000-0000-000000000000	CREATE	policy_group	bf6bde98-2093-4da8-b6d8-3929ef674aff	\N	{"name": "Test Policy Group Demo", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "bf6bde98-2093-4da8-b6d8-3929ef674aff", "severity": "medium", "created_at": "2025-08-28T04:25:26.763Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T04:25:26.763Z", "description": "Test group for demo flow verification", "is_template": false}	**********		2025-08-28 04:25:26.769067+00	\N	\N	system	create	write	sensitive
53ef02e7-1abd-4cd9-8920-6f48389a57e7	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2	\N	{"name": "Demo Medical Privacy Policy", "category": "Medical Privacy", "severity": "medium"}	**********		2025-08-28 04:27:16.870204+00	\N	\N	admin	policy_creation	write	sensitive
4091d8be-e933-49f2-b346-e6953a2ab9ff	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	bf6bde98-2093-4da8-b6d8-3929ef674aff	\N	{"addedPolicyIds": ["3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2"]}	**********		2025-08-28 04:28:19.91075+00	\N	\N	system	add_policy	write	sensitive
21b9d447-03b4-4070-9e0b-1c56c6d3ee77	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2	{"name": "Demo Medical Privacy Policy", "version": 1, "blob_url": null, "category": "Medical Privacy", "severity": "medium", "blob_path": null, "is_active": null, "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "rego_code": null, "created_at": "2025-08-28T04:27:16.865Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "medium", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-08-28T04:27:16.865Z", "updated_by": null, "description": "Test medical privacy policy for demo flow", "policy_type": "medical_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Demo Medical Privacy Policy", "version": 1, "blob_url": null, "category": "Medical Privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "rego_code": null, "created_at": "2025-08-28T04:27:16.865Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-08-28T04:29:13.577Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Test medical privacy policy for demo flow - UPDATED", "policy_type": "medical_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 04:29:13.590399+00	\N	\N	admin	policy_update	write	sensitive
078ea76c-1447-4468-9cc7-7fc627a6a7fb	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	bf6bde98-2093-4da8-b6d8-3929ef674aff	\N	{"addedPolicyIds": ["3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2"]}	**********		2025-08-28 04:33:04.248445+00	\N	\N	system	add_policy	write	sensitive
dcaad9fd-aa97-4b54-acb5-433246806d95	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-28T04:18:06.626Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-28T06:10:34.652Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 06:10:34.658972+00	\N	\N	admin	policy_update	write	sensitive
19d3b28d-4261-430c-ab9b-31babf77e121	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	7dcc8340-2f3a-4a2d-9e85-b821940a6560	\N	{"addedPolicyIds": ["8d723cde-418b-4c56-94de-f6ff3edb7703"]}	**********		2025-08-28 20:34:58.742654+00	\N	\N	system	add_policy	write	sensitive
5ede877f-e856-44fb-8ed1-7353c11ebe47	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	c281f81a-761e-4502-8d6a-9fc0e4e24872	\N	{"code": "e2e_test_1756413298660_HCO", "name": "e2e_test_1756413298660_healthcare_officer"}	**********		2025-08-28 20:34:58.748897+00	\N	\N	system	role_created	write	sensitive
13d6cf8d-6bd0-418b-9fc9-c32bf8cc78fa	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	c281f81a-761e-4502-8d6a-9fc0e4e24872	\N	\N	**********		2025-08-28 20:34:58.762773+00	\N	\N	system	role_deleted	write	sensitive
153f5e35-6fcd-4df3-bb83-18755da308bc	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	7dcc8340-2f3a-4a2d-9e85-b821940a6560	\N	\N	**********		2025-08-28 20:34:58.765995+00	\N	\N	system	deprecate	write	sensitive
6368a226-927d-4ca0-a788-abf279b88a6b	00000000-0000-0000-0000-000000000000	CREATE	policy_group	3884f39f-c810-46e8-ad63-9353c491574a	\N	{"name": "e2e_test_1756413348147_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "3884f39f-c810-46e8-ad63-9353c491574a", "severity": "medium", "created_at": "2025-08-28T20:35:48.211Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:35:48.211Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:35:48.148Z)", "is_template": false}	**********		2025-08-28 20:35:48.213254+00	\N	\N	system	create	write	sensitive
909cdf71-2ea9-49af-9977-e9f8936d7d18	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-28T06:10:34.652Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": true, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-28T06:10:46.049Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 06:10:46.055616+00	\N	\N	admin	policy_update	write	sensitive
4e97ba66-8892-4a58-bf51-5353dcb09d1f	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2	{"name": "Demo Medical Privacy Policy", "version": 1, "blob_url": null, "category": "Medical Privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "rego_code": null, "created_at": "2025-08-28T04:27:16.865Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-08-28T04:29:13.577Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Test medical privacy policy for demo flow - UPDATED", "policy_type": "medical_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Demo Medical Privacy Policy", "version": 1, "blob_url": null, "category": "Medical Privacy", "severity": "high", "blob_path": null, "is_active": false, "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "rego_code": null, "created_at": "2025-08-28T04:27:16.865Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-08-28T06:23:03.140Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Test medical privacy policy for demo flow - UPDATED", "policy_type": "medical_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 06:23:03.144815+00	\N	\N	admin	policy_update	write	sensitive
807f99b7-412e-44c6-a5d8-2854f9d14cbd	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2	{"name": "Demo Medical Privacy Policy", "version": 1, "blob_url": null, "category": "Medical Privacy", "severity": "high", "blob_path": null, "is_active": false, "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "rego_code": null, "created_at": "2025-08-28T04:27:16.865Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-08-28T06:23:03.140Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Test medical privacy policy for demo flow - UPDATED", "policy_type": "medical_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Demo Medical Privacy Policy", "version": 1, "blob_url": null, "category": "Medical Privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "rego_code": null, "created_at": "2025-08-28T04:27:16.865Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-08-28T06:29:34.940Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Test medical privacy policy for demo flow - UPDATED", "policy_type": "medical_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 06:29:34.947579+00	\N	\N	admin	policy_update	write	sensitive
da36e481-8350-4171-8a26-82b7b2ef2973	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	9340a076-0ac3-4c43-8845-6143b2bc032a	\N	{"name": "p7", "category": "data_privacy", "severity": "low", "is_active": true}	**********		2025-08-28 19:37:57.474747+00	\N	\N	admin	policy_creation	write	sensitive
58f99252-30ac-4f0d-993e-3581e9f9b0ea	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	c3bd8586-ce3f-4d21-bdb0-f1afb644fc4a	\N	{"name": "p8", "category": "test_schema_1756409008", "severity": "low", "is_active": true}	**********		2025-08-28 19:59:32.200848+00	\N	\N	admin	policy_creation	write	sensitive
3ff0afda-b759-44af-9717-d6549e7a31fd	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	2ada8019-e800-4aed-a43d-69f7c461b7f0	\N	{"name": "p9", "category": "test_schema_1756409008", "severity": "low", "is_active": true}	**********		2025-08-28 20:06:24.594666+00	\N	\N	admin	policy_creation	write	sensitive
092f4810-710e-4474-9499-2ae716a5ce0f	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	177115f8-1121-4fa0-8e81-f6db326fabf6	\N	{"name": "p10", "category": "test_schema_1756409008", "severity": "low", "is_active": false}	**********		2025-08-28 20:15:26.905399+00	\N	\N	admin	policy_creation	write	sensitive
d0e6c12a-c1d6-4599-9f87-813ddd6380f8	00000000-0000-0000-0000-000000000000	CREATE	policy_group	b82ce312-7cda-43a6-b3ef-a97b58e19e17	\N	{"name": "e2e_test_1756413322576_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "b82ce312-7cda-43a6-b3ef-a97b58e19e17", "severity": "medium", "created_at": "2025-08-28T20:35:22.641Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:35:22.641Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:35:22.577Z)", "is_template": false}	**********		2025-08-28 20:35:22.643172+00	\N	\N	system	create	write	sensitive
67b490c0-cd4c-44d3-9c17-c2cb6891ca3b	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	19fe1b8d-89f4-4019-8f59-2d08382d356d	\N	{"name": "e2e_test_1756413322576_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:35:22.653422+00	\N	\N	admin	policy_creation	write	sensitive
1d3b1d99-0ea9-4e8e-ba75-cc168e1e78f2	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	b82ce312-7cda-43a6-b3ef-a97b58e19e17	\N	{"addedPolicyIds": ["19fe1b8d-89f4-4019-8f59-2d08382d356d"]}	**********		2025-08-28 20:35:22.656634+00	\N	\N	system	add_policy	write	sensitive
2c1da6e5-3ded-48cf-9a16-a732fcc8ff4f	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	6123f679-d34c-4b99-abea-46249bd69db1	\N	{"code": "e2e_test_1756413322576_HCO", "name": "e2e_test_1756413322576_healthcare_officer"}	**********		2025-08-28 20:35:22.662204+00	\N	\N	system	role_created	write	sensitive
ea6a0511-47d4-42cc-80fc-1f2f0d542eba	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	177115f8-1121-4fa0-8e81-f6db326fabf6	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "", "type": "test_schema_1756409008", "active": false, "enabled": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-28T20:15:26.894Z", "updated_by": null, "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-28T20:15:40.781Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 20:15:40.791133+00	\N	\N	admin	policy_update	write	sensitive
6d8c7cf5-b7b2-49f9-a764-faac2663d8bf	00000000-0000-0000-0000-000000000000	CREATE	policy_group	309aef0b-8e87-4d69-b6e7-21b7c89417cb	\N	{"name": "e2e_test_1756413012146_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "309aef0b-8e87-4d69-b6e7-21b7c89417cb", "severity": "medium", "created_at": "2025-08-28T20:30:12.211Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:30:12.211Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:30:12.146Z)", "is_template": false}	**********		2025-08-28 20:30:12.213483+00	\N	\N	system	create	write	sensitive
0cb86f69-321d-4dde-9bd9-daaef71f473c	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	f2d309ee-d92c-4f88-aadc-d714e08d286c	\N	{"name": "e2e_test_1756413012146_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:30:12.222024+00	\N	\N	admin	policy_creation	write	sensitive
416dc813-89fb-4be1-93e6-7d3de3812e9e	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	309aef0b-8e87-4d69-b6e7-21b7c89417cb	\N	\N	**********		2025-08-28 20:30:12.236159+00	\N	\N	system	deprecate	write	sensitive
48b57aa5-1898-4fd5-9fb3-e02497a1b637	00000000-0000-0000-0000-000000000000	CREATE	policy_group	1bd513d1-1cf3-473d-828a-d0420fe00265	\N	{"name": "e2e_test_1756413202760_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "1bd513d1-1cf3-473d-828a-d0420fe00265", "severity": "medium", "created_at": "2025-08-28T20:33:22.823Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:33:22.823Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:33:22.760Z)", "is_template": false}	**********		2025-08-28 20:33:22.827385+00	\N	\N	system	create	write	sensitive
8c59b0af-d497-4169-8669-a7511cbf34b0	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	ea7ec861-35f9-4897-9c04-b5b05823ca32	\N	{"name": "e2e_test_1756413202760_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:33:22.834432+00	\N	\N	admin	policy_creation	write	sensitive
c8dba458-5337-463a-9526-9cdd80838979	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	1bd513d1-1cf3-473d-828a-d0420fe00265	\N	{"addedPolicyIds": ["ea7ec861-35f9-4897-9c04-b5b05823ca32"]}	**********		2025-08-28 20:33:22.837291+00	\N	\N	system	add_policy	write	sensitive
865e1397-7537-4bae-97e6-1d6d59995c9c	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	1bd513d1-1cf3-473d-828a-d0420fe00265	\N	\N	**********		2025-08-28 20:33:22.843475+00	\N	\N	system	deprecate	write	sensitive
cc687639-3939-4b59-a850-dd42a38c717b	00000000-0000-0000-0000-000000000000	CREATE	policy_group	0f3d98da-c217-4ed7-9adf-432da4f42cba	\N	{"name": "e2e_test_1756413223928_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "0f3d98da-c217-4ed7-9adf-432da4f42cba", "severity": "medium", "created_at": "2025-08-28T20:33:44.002Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:33:44.002Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:33:43.929Z)", "is_template": false}	**********		2025-08-28 20:33:44.004463+00	\N	\N	system	create	write	sensitive
0ee47967-24e3-4918-8ffa-09cbd1a99883	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	d58edf2e-641b-496b-b92e-d0c297f9684a	\N	{"name": "e2e_test_1756413223928_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:33:44.011969+00	\N	\N	admin	policy_creation	write	sensitive
c0e23eff-129c-471f-b8a1-c23a6f2fd9c2	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	0f3d98da-c217-4ed7-9adf-432da4f42cba	\N	{"addedPolicyIds": ["d58edf2e-641b-496b-b92e-d0c297f9684a"]}	**********		2025-08-28 20:33:44.014993+00	\N	\N	system	add_policy	write	sensitive
dbcc801b-ecc4-431d-8cd0-647293a9688e	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	0f3d98da-c217-4ed7-9adf-432da4f42cba	\N	\N	**********		2025-08-28 20:33:44.027057+00	\N	\N	system	deprecate	write	sensitive
ae77929f-716f-4a7d-8e85-b79071216be1	00000000-0000-0000-0000-000000000000	CREATE	policy_group	8b1c2a2c-8741-4d76-b92f-a5b806ed431a	\N	{"name": "e2e_test_1756413259601_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "8b1c2a2c-8741-4d76-b92f-a5b806ed431a", "severity": "medium", "created_at": "2025-08-28T20:34:19.665Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:34:19.665Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:34:19.601Z)", "is_template": false}	**********		2025-08-28 20:34:19.66804+00	\N	\N	system	create	write	sensitive
30a81fd8-2bf9-477f-8b18-71b3f99bb8ea	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	afc5a3cf-82e8-450b-a846-2c4529ac170b	\N	{"name": "e2e_test_1756413259601_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:34:19.680678+00	\N	\N	admin	policy_creation	write	sensitive
f7112022-c8b1-4857-a9c7-5fe30329320a	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	8b1c2a2c-8741-4d76-b92f-a5b806ed431a	\N	{"addedPolicyIds": ["afc5a3cf-82e8-450b-a846-2c4529ac170b"]}	**********		2025-08-28 20:34:19.683605+00	\N	\N	system	add_policy	write	sensitive
bef1fd79-c02f-4e5f-b734-1bbc27eff6d6	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	0dcc858a-5610-4abc-952b-67a3ede00efc	\N	{"code": "e2e_test_1756413259601_HCO", "name": "e2e_test_1756413259601_healthcare_officer"}	**********		2025-08-28 20:34:19.690153+00	\N	\N	system	role_created	write	sensitive
61a64171-933e-4e54-a2df-a462498239e7	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	0dcc858a-5610-4abc-952b-67a3ede00efc	\N	\N	**********		2025-08-28 20:34:19.703413+00	\N	\N	system	role_deleted	write	sensitive
e873cf3b-6119-474c-b6c4-9618c80b3372	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	8b1c2a2c-8741-4d76-b92f-a5b806ed431a	\N	\N	**********		2025-08-28 20:34:19.70752+00	\N	\N	system	deprecate	write	sensitive
c43836f8-ab68-45e0-bae1-9178c4e0627f	00000000-0000-0000-0000-000000000000	CREATE	policy_group	7dcc8340-2f3a-4a2d-9e85-b821940a6560	\N	{"name": "e2e_test_1756413298660_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "7dcc8340-2f3a-4a2d-9e85-b821940a6560", "severity": "medium", "created_at": "2025-08-28T20:34:58.729Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:34:58.729Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:34:58.661Z)", "is_template": false}	**********		2025-08-28 20:34:58.732158+00	\N	\N	system	create	write	sensitive
8e9f1a9d-00f8-4026-a341-4537552d4c0c	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	8d723cde-418b-4c56-94de-f6ff3edb7703	\N	{"name": "e2e_test_1756413298660_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:34:58.73963+00	\N	\N	admin	policy_creation	write	sensitive
4cccd91e-5e4d-4b9b-aa06-94c83a7eddb9	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	3884f39f-c810-46e8-ad63-9353c491574a	\N	{"addedPolicyIds": ["e1414965-9a7d-4fdb-aa06-666be8dc37bf"]}	**********		2025-08-28 20:35:48.223468+00	\N	\N	system	add_policy	write	sensitive
50adf91c-3052-482e-8a1b-2973683f6c19	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	28db8e6c-4f60-4d7d-b706-ad3e181da2bb	\N	{"code": "e2e_test_1756413348147_HCO", "name": "e2e_test_1756413348147_healthcare_officer"}	**********		2025-08-28 20:35:48.23635+00	\N	\N	system	role_created	write	sensitive
a64d442c-2f2f-4f9c-b476-b98a1e8e32ab	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	28db8e6c-4f60-4d7d-b706-ad3e181da2bb	\N	\N	**********		2025-08-28 20:35:49.27525+00	\N	\N	system	role_deleted	write	sensitive
64219805-7b97-4e2d-9ab0-7e97a41deebd	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	3884f39f-c810-46e8-ad63-9353c491574a	\N	\N	**********		2025-08-28 20:35:49.286235+00	\N	\N	system	deprecate	write	sensitive
3f32234f-6228-4177-8bff-26ff30a688de	00000000-0000-0000-0000-000000000000	CREATE	policy_group	b504593d-3a44-455d-8fe1-59656ea1753a	\N	{"name": "e2e_test_1756413382997_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "b504593d-3a44-455d-8fe1-59656ea1753a", "severity": "medium", "created_at": "2025-08-28T20:36:23.057Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:36:23.057Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:36:22.997Z)", "is_template": false}	**********		2025-08-28 20:36:23.059467+00	\N	\N	system	create	write	sensitive
e7a9455a-2cd2-4f38-a759-785bb143a063	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	3ffa6959-db5b-492a-a963-944763a7c7b1	\N	{"name": "e2e_test_1756413382997_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:36:23.067045+00	\N	\N	admin	policy_creation	write	sensitive
6af18f71-6e02-46c9-9c44-3b5eaaf3d73d	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	b504593d-3a44-455d-8fe1-59656ea1753a	\N	{"addedPolicyIds": ["3ffa6959-db5b-492a-a963-944763a7c7b1"]}	**********		2025-08-28 20:36:23.069724+00	\N	\N	system	add_policy	write	sensitive
c60dd60e-3b4c-4573-b60e-8d90629902a0	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	4519f7b7-580b-401c-9477-d8910e4c4073	\N	{"code": "e2e_test_1756413382997_HCO", "name": "e2e_test_1756413382997_healthcare_officer"}	**********		2025-08-28 20:36:23.075478+00	\N	\N	system	role_created	write	sensitive
a4243976-dd23-400a-be63-5a558cba6be5	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	4519f7b7-580b-401c-9477-d8910e4c4073	\N	\N	**********		2025-08-28 20:36:24.117057+00	\N	\N	system	role_deleted	write	sensitive
29b98db6-7be8-4721-98e0-28d6581bb507	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	b504593d-3a44-455d-8fe1-59656ea1753a	\N	\N	**********		2025-08-28 20:36:24.125238+00	\N	\N	system	deprecate	write	sensitive
ae98e751-d0a7-44ec-bf99-b2aaabf1c06c	00000000-0000-0000-0000-000000000000	CREATE	policy_group	3ec7f725-252e-43a7-bcd7-82e8d7b82d9f	\N	{"name": "e2e_test_1756413399924_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "3ec7f725-252e-43a7-bcd7-82e8d7b82d9f", "severity": "medium", "created_at": "2025-08-28T20:36:39.970Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:36:39.970Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:36:39.928Z)", "is_template": false}	**********		2025-08-28 20:36:39.972204+00	\N	\N	system	create	write	sensitive
6d3284ed-8579-4ea4-980e-e479241f21e3	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	1604904e-f5d0-4842-b59c-a11390052789	\N	{"name": "e2e_test_1756413399924_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:36:39.979728+00	\N	\N	admin	policy_creation	write	sensitive
8dbe88f7-2876-46a5-b168-4668b8ab2586	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	3ec7f725-252e-43a7-bcd7-82e8d7b82d9f	\N	{"addedPolicyIds": ["1604904e-f5d0-4842-b59c-a11390052789"]}	**********		2025-08-28 20:36:39.983452+00	\N	\N	system	add_policy	write	sensitive
c816bd1a-31c8-4718-af6f-39f20deac818	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	6e5b3ebc-7db5-4b8a-9ae2-411b5a93ac62	\N	{"code": "e2e_test_1756413399924_HCO", "name": "e2e_test_1756413399924_healthcare_officer"}	**********		2025-08-28 20:36:39.989539+00	\N	\N	system	role_created	write	sensitive
df2b08ca-3fd2-4153-bbc4-138e14ae8795	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	4eccd6ca-bc28-428d-9e0d-0cdc4257fa33	\N	{"name": "e2e_test_1756413399924_group_test_policy", "category": "healthcare_compliance", "severity": "medium"}	**********		2025-08-28 20:36:41.035014+00	\N	\N	admin	policy_creation	write	sensitive
b7a1a46c-1e2a-4348-b6ab-86ed39733c73	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	3ec7f725-252e-43a7-bcd7-82e8d7b82d9f	\N	{"addedPolicyIds": ["4eccd6ca-bc28-428d-9e0d-0cdc4257fa33"]}	**********		2025-08-28 20:36:41.041087+00	\N	\N	system	add_policy	write	sensitive
3d7b28df-1d0c-47fe-a05f-945a6210b773	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	3ec7f725-252e-43a7-bcd7-82e8d7b82d9f	\N	\N	**********		2025-08-28 20:36:41.047015+00	\N	\N	system	deprecate	write	sensitive
7a6f1509-2865-4761-a89c-14076033c375	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	6e5b3ebc-7db5-4b8a-9ae2-411b5a93ac62	\N	\N	**********		2025-08-28 20:36:41.055298+00	\N	\N	system	role_deleted	write	sensitive
6a54a316-65d0-4664-b695-dba6f736ec8b	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	3ec7f725-252e-43a7-bcd7-82e8d7b82d9f	\N	\N	**********		2025-08-28 20:36:41.062906+00	\N	\N	system	deprecate	write	sensitive
ec5918a0-d363-4f3a-9417-c13b7ce191b3	00000000-0000-0000-0000-000000000000	CREATE	policy_group	5c51e454-aa47-4bfe-b81a-815be0cce9cb	\N	{"name": "e2e_test_1756413483366_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "5c51e454-aa47-4bfe-b81a-815be0cce9cb", "severity": "medium", "created_at": "2025-08-28T20:38:03.457Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:38:03.457Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:38:03.366Z)", "is_template": false}	**********		2025-08-28 20:38:03.459788+00	\N	\N	system	create	write	sensitive
6e89ea9a-4c68-4ba0-b551-9e25103956f8	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	80073610-ee06-49db-bd2a-531c5c39a76c	\N	{"name": "e2e_test_1756413483366_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:38:03.466995+00	\N	\N	admin	policy_creation	write	sensitive
8421efb7-6160-485b-a5da-78fdea53e6d9	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	5c51e454-aa47-4bfe-b81a-815be0cce9cb	\N	{"addedPolicyIds": ["80073610-ee06-49db-bd2a-531c5c39a76c"]}	**********		2025-08-28 20:38:03.470164+00	\N	\N	system	add_policy	write	sensitive
1dc5b7a3-cdc2-4c6a-9ef7-754d60e31e5c	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	8f482811-77fa-4661-b79e-597049b7aaef	\N	{"code": "e2e_test_1756413483366_HCO", "name": "e2e_test_1756413483366_healthcare_officer"}	**********		2025-08-28 20:38:03.479515+00	\N	\N	system	role_created	write	sensitive
38e3007d-2bd4-429c-8ddc-3866293463ea	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	0c1e7f1c-18a6-4df9-973d-55fe90202b0f	\N	{"name": "e2e_test_1756413483366_group_test_policy", "category": "healthcare_compliance", "severity": "medium"}	**********		2025-08-28 20:38:04.517469+00	\N	\N	admin	policy_creation	write	sensitive
5362ee31-7dc8-4ebb-b337-b3534bf51be0	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	5c51e454-aa47-4bfe-b81a-815be0cce9cb	\N	{"addedPolicyIds": ["0c1e7f1c-18a6-4df9-973d-55fe90202b0f"]}	**********		2025-08-28 20:38:04.524184+00	\N	\N	system	add_policy	write	sensitive
6903c426-8853-4028-9caf-00b24bea38c6	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	5c51e454-aa47-4bfe-b81a-815be0cce9cb	\N	\N	**********		2025-08-28 20:38:04.530244+00	\N	\N	system	deprecate	write	sensitive
c81c0a5c-3241-41d5-8165-a5cb426c5bfc	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	8f482811-77fa-4661-b79e-597049b7aaef	\N	\N	**********		2025-08-28 20:38:05.564631+00	\N	\N	system	role_deleted	write	sensitive
834eaf47-46b2-4d2a-9ed6-990db7bd4b13	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	5c51e454-aa47-4bfe-b81a-815be0cce9cb	\N	\N	**********		2025-08-28 20:38:05.575208+00	\N	\N	system	deprecate	write	sensitive
bae17043-9dc9-4451-913e-9aec89c16e16	00000000-0000-0000-0000-000000000000	CREATE	policy_group	05c16aef-9eb0-4452-9b46-57aeb4f6e3e5	\N	{"name": "e2e_test_1756413525743_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "05c16aef-9eb0-4452-9b46-57aeb4f6e3e5", "severity": "medium", "created_at": "2025-08-28T20:38:45.802Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:38:45.802Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:38:45.744Z)", "is_template": false}	**********		2025-08-28 20:38:45.805103+00	\N	\N	system	create	write	sensitive
fc327169-cc66-4913-ae23-8f575b22384a	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	3c35d5ca-164b-4b75-bf0b-596fa2ee2627	\N	{"name": "e2e_test_1756413525743_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:38:45.813774+00	\N	\N	admin	policy_creation	write	sensitive
cb9c67dd-dac6-4edf-99e0-242060490d5e	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	05c16aef-9eb0-4452-9b46-57aeb4f6e3e5	\N	{"addedPolicyIds": ["3c35d5ca-164b-4b75-bf0b-596fa2ee2627"]}	**********		2025-08-28 20:38:45.816887+00	\N	\N	system	add_policy	write	sensitive
c141d6c7-74bc-4fe2-b92a-a92cec27124a	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	30086819-845b-42fc-8f09-9a042841eab5	\N	{"code": "e2e_test_1756413525743_HCO", "name": "e2e_test_1756413525743_healthcare_officer"}	**********		2025-08-28 20:38:45.82332+00	\N	\N	system	role_created	write	sensitive
abc67b31-cdd5-4fe9-9990-8de63718fa68	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	7ee9ae41-de86-4244-90f8-25c2da0623b2	\N	{"name": "e2e_test_1756413525743_group_test_policy", "category": "healthcare_compliance", "severity": "medium"}	**********		2025-08-28 20:38:46.856834+00	\N	\N	admin	policy_creation	write	sensitive
72992cd5-4ad4-497a-8832-0aac23ceb070	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	05c16aef-9eb0-4452-9b46-57aeb4f6e3e5	\N	{"addedPolicyIds": ["7ee9ae41-de86-4244-90f8-25c2da0623b2"]}	**********		2025-08-28 20:38:46.863397+00	\N	\N	system	add_policy	write	sensitive
b4a5f3a4-e257-4633-a00c-4df6875fb5eb	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	05c16aef-9eb0-4452-9b46-57aeb4f6e3e5	\N	\N	**********		2025-08-28 20:38:46.869205+00	\N	\N	system	deprecate	write	sensitive
c99241b8-302d-4ef2-8a79-f5a8d95a1d3e	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	30086819-845b-42fc-8f09-9a042841eab5	\N	\N	**********		2025-08-28 20:38:47.902969+00	\N	\N	system	role_deleted	write	sensitive
109314e3-ad71-4c4d-bf7c-62a2cfd04002	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	05c16aef-9eb0-4452-9b46-57aeb4f6e3e5	\N	\N	**********		2025-08-28 20:38:47.908609+00	\N	\N	system	deprecate	write	sensitive
3dca5209-9097-4b12-b618-3156fffba835	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	30086819-845b-42fc-8f09-9a042841eab5	\N	\N	**********		2025-08-28 20:38:47.920641+00	\N	\N	system	role_deleted	write	sensitive
89b6bbe9-ca2b-445c-9f31-0bf44f288a7a	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	05c16aef-9eb0-4452-9b46-57aeb4f6e3e5	\N	\N	**********		2025-08-28 20:38:47.925414+00	\N	\N	system	deprecate	write	sensitive
d855acf1-90e9-408e-b393-7c84bce8b47b	00000000-0000-0000-0000-000000000000	CREATE	policy_group	33b0b43b-f336-49e0-ad22-a8ab1ca8fc70	\N	{"name": "e2e_test_1756413938766_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "33b0b43b-f336-49e0-ad22-a8ab1ca8fc70", "severity": "medium", "created_at": "2025-08-28T20:45:38.819Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:45:38.819Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:45:38.767Z)", "is_template": false}	**********		2025-08-28 20:45:38.822159+00	\N	\N	system	create	write	sensitive
e65f1ffc-b8e4-4c50-9471-262b7ac1144a	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	6f2c6077-2364-478f-96e6-c2196d623b4e	\N	{"name": "e2e_test_1756413938766_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:45:38.829147+00	\N	\N	admin	policy_creation	write	sensitive
725c562c-470f-4944-83d6-759f00aa92e5	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	33b0b43b-f336-49e0-ad22-a8ab1ca8fc70	\N	{"addedPolicyIds": ["6f2c6077-2364-478f-96e6-c2196d623b4e"]}	**********		2025-08-28 20:45:38.831579+00	\N	\N	system	add_policy	write	sensitive
e059d13d-2983-457e-a25d-8966f4ea1a9a	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	63db9c9d-b64d-452c-9d54-194005f0c329	\N	{"code": "e2e_test_1756413938766_HCO", "name": "e2e_test_1756413938766_healthcare_officer"}	**********		2025-08-28 20:45:38.836556+00	\N	\N	system	role_created	write	sensitive
1822f11c-09fb-49fa-b6fd-adee822bc826	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	63db9c9d-b64d-452c-9d54-194005f0c329	\N	\N	**********		2025-08-28 20:45:38.843587+00	\N	\N	system	role_deleted	write	sensitive
7519e7fb-5341-499d-8b96-d8ae857b40d8	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	33b0b43b-f336-49e0-ad22-a8ab1ca8fc70	\N	\N	**********		2025-08-28 20:45:38.846727+00	\N	\N	system	deprecate	write	sensitive
90a80c5c-86a5-4535-8117-345021f53249	00000000-0000-0000-0000-000000000000	CREATE	policy_group	dda421b1-14c6-4ede-b3fe-84b4fc2e4805	\N	{"name": "e2e_test_1756414222865_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "dda421b1-14c6-4ede-b3fe-84b4fc2e4805", "severity": "medium", "created_at": "2025-08-28T20:50:22.918Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:50:22.918Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:50:22.866Z)", "is_template": false}	**********		2025-08-28 20:50:22.919887+00	\N	\N	system	create	write	sensitive
1e799b0f-ec28-4d67-b140-ff49fca36691	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	96c01e28-12b6-4aac-a83f-15bb4527f26a	\N	{"name": "e2e_test_1756414222865_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:50:22.931378+00	\N	\N	admin	policy_creation	write	sensitive
2ea2f0cf-5d37-4723-9e58-41438a137775	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	dda421b1-14c6-4ede-b3fe-84b4fc2e4805	\N	{"addedPolicyIds": ["96c01e28-12b6-4aac-a83f-15bb4527f26a"]}	**********		2025-08-28 20:50:22.936093+00	\N	\N	system	add_policy	write	sensitive
01db11ab-2848-4197-bc5f-b8aa72b56f37	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	b19d20da-80cc-4626-ac74-4d57160829c8	\N	{"code": "e2e_test_1756414222865_HCO", "name": "e2e_test_1756414222865_healthcare_officer"}	**********		2025-08-28 20:50:22.941625+00	\N	\N	system	role_created	write	sensitive
6ec6ccd9-aa74-48e0-90a2-f3794648355a	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	b19d20da-80cc-4626-ac74-4d57160829c8	\N	\N	**********		2025-08-28 20:50:22.948338+00	\N	\N	system	role_deleted	write	sensitive
0a44764c-d311-4422-8eab-460f3ebf1fbc	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	dda421b1-14c6-4ede-b3fe-84b4fc2e4805	\N	\N	**********		2025-08-28 20:50:22.951528+00	\N	\N	system	deprecate	write	sensitive
e9e9e885-be80-42be-ba4b-39bba3e142dc	00000000-0000-0000-0000-000000000000	CREATE	policy_group	22106b70-899a-4851-8f03-d858b0589f01	\N	{"name": "e2e_test_1756414247713_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "22106b70-899a-4851-8f03-d858b0589f01", "severity": "medium", "created_at": "2025-08-28T20:50:47.771Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:50:47.771Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:50:47.713Z)", "is_template": false}	**********		2025-08-28 20:50:47.775648+00	\N	\N	system	create	write	sensitive
6ad9c89c-bf2c-493c-b6df-4c79e759336d	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	3e428b15-e58c-4ef9-ab58-bfe6303ecf4f	\N	{"name": "e2e_test_1756414247713_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:50:47.79002+00	\N	\N	admin	policy_creation	write	sensitive
5d65b6ed-5bb5-4ae9-b78d-c6e6f80931ff	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	22106b70-899a-4851-8f03-d858b0589f01	\N	{"addedPolicyIds": ["3e428b15-e58c-4ef9-ab58-bfe6303ecf4f"]}	**********		2025-08-28 20:50:47.792813+00	\N	\N	system	add_policy	write	sensitive
29d24ad5-603c-4f88-a4c9-cd53b2cad3f3	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	942e55df-944d-413a-9270-b00a25156c7d	\N	{"code": "e2e_test_1756414247713_HCO", "name": "e2e_test_1756414247713_healthcare_officer"}	**********		2025-08-28 20:50:47.798721+00	\N	\N	system	role_created	write	sensitive
ab30fa0b-0bca-4df9-9981-6c525052759d	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	2a052eef-c31d-44e3-92f8-5a9aec35d792	\N	{"name": "e2e_test_1756414247713_group_test_policy", "category": "healthcare_compliance", "severity": "medium"}	**********		2025-08-28 20:50:48.833264+00	\N	\N	admin	policy_creation	write	sensitive
dc46d193-3d5e-458e-a6eb-25d206239389	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	22106b70-899a-4851-8f03-d858b0589f01	\N	{"addedPolicyIds": ["2a052eef-c31d-44e3-92f8-5a9aec35d792"]}	**********		2025-08-28 20:50:48.840223+00	\N	\N	system	add_policy	write	sensitive
f5dabeed-e606-4b7b-bc2c-3e716c0cd5e7	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	22106b70-899a-4851-8f03-d858b0589f01	\N	\N	**********		2025-08-28 20:50:48.845856+00	\N	\N	system	deprecate	write	sensitive
eb5b6f5d-2493-4f2f-b31a-ca5c14a7dc00	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	942e55df-944d-413a-9270-b00a25156c7d	\N	\N	**********		2025-08-28 20:50:49.874237+00	\N	\N	system	role_deleted	write	sensitive
ad908a64-695a-4da4-805b-8c2ebff13b74	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	22106b70-899a-4851-8f03-d858b0589f01	\N	\N	**********		2025-08-28 20:50:49.882448+00	\N	\N	system	deprecate	write	sensitive
80a99b4b-613a-451d-b7f2-bc7a10036af2	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	942e55df-944d-413a-9270-b00a25156c7d	\N	\N	**********		2025-08-28 20:50:49.898974+00	\N	\N	system	role_deleted	write	sensitive
8c0d38d6-974b-409b-bd1e-25ad11a0eafe	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	22106b70-899a-4851-8f03-d858b0589f01	\N	\N	**********		2025-08-28 20:50:49.903264+00	\N	\N	system	deprecate	write	sensitive
a1e7cd3b-e694-4ea7-bf71-e74366d775b8	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	40a63e03-d616-4877-9f4b-cbf5fcad8cdc	\N	{"name": "p11", "category": "test_schema_1756409008", "severity": "low", "is_active": true}	**********		2025-08-29 08:30:16.841696+00	\N	\N	admin	policy_creation	write	sensitive
4ca766bf-6a59-4d48-b971-2f0badc4e144	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	86d75236-d62b-4f41-8eb9-70047dc986bc	\N	{"name": "p12", "category": "test_schema_1756409008", "severity": "low", "is_active": true}	**********		2025-08-29 08:34:22.84542+00	\N	\N	admin	policy_creation	write	sensitive
\.


--
-- Data for Name: chat_messages; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.chat_messages (message_id, session_id, role, content, original_content, policies_applied, is_filtered, created_at) FROM stdin;
\.


--
-- Data for Name: dataset_entries; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.dataset_entries (id, dataset_id, test_case_type, input, expected_output, context, retrieval_context, tools_called, expected_outcome, scenario, initial_context, entry_order, created_at) FROM stdin;
\.


--
-- Data for Name: datasets; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.datasets (id, name, description, type, status, data, record_count, created_by, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: documents; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.documents (document_id, title, content, document_type, metadata, file_path, is_sensitive, created_at, updated_at, created_by, updated_by) FROM stdin;
\.


--
-- Data for Name: enum_categories; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.enum_categories (category_id, name, description, policy_type, field_path, is_active, created_at, updated_at) FROM stdin;
1	Medical Roles	Roles that can access medical data	medical_privacy	allowed_roles	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
2	Medical Fields	Medical fields requiring special protection	medical_privacy	protected_fields	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
3	Data Privacy Roles	Roles that can access sensitive data	data_privacy	allowed_roles	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
4	Data Privacy Fields	Data fields requiring privacy protection	data_privacy	protected_fields	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
5	Access Control Roles	Roles for access control policies	access_control	allowed_roles	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
6	Compliance Roles	Roles for compliance policies	compliance	allowed_roles	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
7	Severity Levels	Policy severity levels	all	severity	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
\.


--
-- Data for Name: enum_values; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.enum_values (value_id, category_id, value, display_name, description, sort_order, is_active, created_at, updated_at) FROM stdin;
1	1	doctor	Doctor	Medical doctors with full access	1	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
2	1	nurse	Nurse	Nursing staff with patient care access	2	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
3	1	admin	Administrator	System administrators	3	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
4	1	pharmacist	Pharmacist	Pharmacy staff with medication access	4	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
5	1	lab_tech	Lab Technician	Laboratory technicians	5	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
6	1	specialist	Specialist	Medical specialists	6	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
7	1	resident	Resident	Medical residents	7	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
8	2	diagnosis	Diagnosis	Patient diagnosis information	1	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
9	2	medication	Medication	Prescribed medications	2	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
10	2	lab_orders	Lab Orders	Laboratory test orders	3	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
11	2	medical_record_number	Medical Record Number	Patient record identifier	4	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
12	2	treatment_plan	Treatment Plan	Patient treatment plans	5	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
13	2	billing_info	Billing Information	Medical billing data	6	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
14	2	patient_notes	Patient Notes	Clinical notes and observations	7	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
15	2	prescriptions	Prescriptions	Medication prescriptions	8	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
16	2	vital_signs	Vital Signs	Patient vital signs	9	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
17	2	allergies	Allergies	Patient allergy information	10	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
18	2	family_history	Family History	Patient family medical history	11	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
19	2	immunizations	Immunizations	Patient immunization records	12	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
20	3	admin	Administrator	System administrators	1	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
21	3	manager	Manager	Department managers	2	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
22	3	analyst	Analyst	Data analysts	3	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
23	3	user	User	General users	4	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
24	3	viewer	Viewer	Read-only users	5	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
25	3	editor	Editor	Content editors	6	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
26	4	personal_info	Personal Information	Personal identification data	1	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
27	4	contact_info	Contact Information	Contact details	2	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
28	4	financial_data	Financial Data	Financial information	3	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
29	4	employment_data	Employment Data	Employment information	4	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
30	4	health_data	Health Data	Health-related information	5	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
31	5	admin	Administrator	System administrators	1	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
32	5	manager	Manager	Department managers	2	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
33	5	user	User	General users	3	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
34	5	guest	Guest	Temporary users	4	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
35	6	compliance_officer	Compliance Officer	Compliance monitoring staff	1	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
36	6	auditor	Auditor	Internal auditors	2	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
37	6	legal	Legal	Legal department staff	3	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
38	6	admin	Administrator	System administrators	4	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
39	7	low	Low	Low priority issues	1	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
40	7	medium	Medium	Medium priority issues	2	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
41	7	high	High	High priority issues	3	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
42	7	critical	Critical	Critical priority issues	4	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
\.


--
-- Data for Name: evaluation_metrics; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.evaluation_metrics (id, name, description, category, implementation_type, config, is_active, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: evaluations; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.evaluations (id, evaluation_id, experiment_id, experiment_name, agent_name, dataset_name, evaluation_type, status, evaluations, summary, created_at, completed_at) FROM stdin;
\.


--
-- Data for Name: experiment_evaluations; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.experiment_evaluations (id, experiment_id, metric_id, status, score, details, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: experiments; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.experiments (id, name, description, dataset_id, agent_config, execution_mode, status, progress, started_at, completed_at, created_by, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: guardrail_services; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.guardrail_services (id, service_id, name, version, type, endpoint, health_check_path, timeout_ms, capabilities, supported_content_types, status, last_health_check, metadata, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: integration_dlq; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.integration_dlq (id, tenant_id, destination, event_type, event_version, payload_json, attempts, last_error, dead_at, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: integration_outbox; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.integration_outbox (id, tenant_id, destination, event_type, event_version, payload_json, status, attempts, next_attempt_at, last_error, created_at, updated_at) FROM stdin;
8f57e63d-1fe8-4d54-a1ca-0b405941fb8f	\N	webhook	role.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "b1b2c3d4-e5f6-7890-abcd-111111111111", "resource_type": "role"}}	pending	0	2025-08-27 17:57:54.32967+00	\N	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00
01041ecf-4bb0-4cc2-8bd4-343e45c6b9a7	\N	webhook	role.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "b1b2c3d4-e5f6-7890-abcd-222222222222", "resource_type": "role"}}	pending	0	2025-08-27 17:57:54.32967+00	\N	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00
66869758-2306-48c4-8f7a-ffbd4f640f87	\N	webhook	role.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "b1b2c3d4-e5f6-7890-abcd-333333333333", "resource_type": "role"}}	pending	0	2025-08-27 17:57:54.32967+00	\N	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00
c7d7d63b-88b0-4487-acc2-3f6286825b04	\N	webhook	role.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "b1b2c3d4-e5f6-7890-abcd-444444444444", "resource_type": "role"}}	pending	0	2025-08-27 17:57:54.32967+00	\N	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00
c31cfbee-d4ef-4cac-b4cd-11e5e2ad4445	\N	webhook	role.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "b1b2c3d4-e5f6-7890-abcd-555555555555", "resource_type": "role"}}	pending	0	2025-08-27 17:57:54.32967+00	\N	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00
b56cc67f-0db9-4d5a-8167-064a56658ef5	\N	webhook	agent.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "c1b2c3d4-e5f6-7890-abcd-111111111111", "resource_type": "agent"}}	pending	0	2025-08-27 17:57:54.32967+00	\N	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00
505a214e-f396-457d-a474-6e2d4ac97e86	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "f1b2c3d4-e5f6-7890-abcd-111111111111", "resource_type": "policy"}}	pending	0	2025-08-27 17:57:54.32967+00	\N	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00
981f40f9-a8f1-4fcb-b735-91e46e6e665b	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "f1b2c3d4-e5f6-7890-abcd-222222222222", "resource_type": "policy"}}	pending	0	2025-08-27 17:57:54.32967+00	\N	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00
9f80cecb-7656-4ba1-bfe4-11c152458824	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "f1b2c3d4-e5f6-7890-abcd-333333333333", "resource_type": "policy"}}	pending	0	2025-08-27 17:57:54.32967+00	\N	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00
f884cab8-40f7-49aa-a3df-db14efe7b89c	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "bd7363f8-6104-449e-873a-db0539f0fa17", "resource_type": "policy"}}	pending	0	2025-08-27 18:11:04.643479+00	\N	2025-08-27 18:11:04.643479+00	2025-08-27 18:11:04.643479+00
3bcfe5f9-946e-418e-ad71-138ab726e91c	\N	webhook	agent.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "00033e45-2454-42cc-b247-0c6e3d4e7e06", "resource_type": "agent"}}	pending	0	2025-08-27 18:11:28.981122+00	\N	2025-08-27 18:11:28.981122+00	2025-08-27 18:11:28.981122+00
53887779-e448-4da0-94bd-d9c473410403	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "b1b2c3d4-e5f6-7890-abcd-111111111111", "agent_id": "00033e45-2454-42cc-b247-0c6e3d4e7e06", "group_id": "d1b2c3d4-e5f6-7890-abcd-111111111111", "policy_id": "bd7363f8-6104-449e-873a-db0539f0fa17"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-27 18:11:42.061408+00	\N	2025-08-27 18:11:42.061408+00	2025-08-27 18:11:42.061408+00
58721fb4-d68e-4328-8858-6d0c5b6d6cf5	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "759c48a8-4e86-4430-8da0-73973abba594", "resource_type": "policy"}}	pending	0	2025-08-27 20:07:59.801197+00	\N	2025-08-27 20:07:59.801197+00	2025-08-27 20:07:59.801197+00
1f95d02f-5e28-44b4-9e27-962629aa7f92	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "4855b086-ab50-4488-9094-58f0be3f2cf3", "resource_type": "policy"}}	pending	0	2025-08-27 21:38:06.072074+00	\N	2025-08-27 21:38:06.072074+00	2025-08-27 21:38:06.072074+00
b8d767f7-f240-4f3b-97f6-9ebd86acf979	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "9b3d812f-bde0-43e9-a211-5d48379f1320", "resource_type": "policy"}}	pending	0	2025-08-27 22:01:37.878715+00	\N	2025-08-27 22:01:37.878715+00	2025-08-27 22:01:37.878715+00
70b176bf-8369-4b6b-ba00-14bb4defe3f6	\N	webhook	agent.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "1b126746-792f-4a37-b052-537962196a48", "resource_type": "agent"}}	pending	0	2025-08-27 22:01:47.268285+00	\N	2025-08-27 22:01:47.268285+00	2025-08-27 22:01:47.268285+00
a48e4cba-1418-458a-96c9-794a4cb8eb08	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "resource_type": "policy"}}	pending	0	2025-08-27 22:02:54.811707+00	\N	2025-08-27 22:02:54.811707+00	2025-08-27 22:02:54.811707+00
263348af-8611-43db-879a-9e4ee0588efc	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "b1b2c3d4-e5f6-7890-abcd-222222222222", "agent_id": "00033e45-2454-42cc-b247-0c6e3d4e7e06", "group_id": "d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf", "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-27 22:05:45.835249+00	\N	2025-08-27 22:05:45.835249+00	2025-08-27 22:05:45.835249+00
df8363de-d894-466a-9d44-686251d958a1	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "00accc7f-7fd6-42cd-acbd-e9016b48eaec", "resource_type": "policy"}}	pending	0	2025-08-27 22:17:01.52259+00	\N	2025-08-27 22:17:01.52259+00	2025-08-27 22:17:01.52259+00
cbc2d6c8-635e-4fac-b154-2f84b89abf61	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "3c7b0cb1-0c84-4c64-8ebb-bedc7c262e53", "resource_type": "policy"}}	pending	0	2025-08-27 22:17:41.276739+00	\N	2025-08-27 22:17:41.276739+00	2025-08-27 22:17:41.276739+00
53a2204f-da34-4481-9b00-77422261b370	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "f4c1a348-7f94-4ed5-908c-4aee04e82f37", "resource_type": "policy"}}	pending	0	2025-08-27 22:18:03.518299+00	\N	2025-08-27 22:18:03.518299+00	2025-08-27 22:18:03.518299+00
de8746c0-ed7c-44a0-9427-c0decdd7e8f1	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "resource_type": "policy"}}	pending	0	2025-08-27 22:30:11.908103+00	\N	2025-08-27 22:30:11.908103+00	2025-08-27 22:30:11.908103+00
5f1c626c-6308-4230-9c89-c97c5c19ae82	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "62124064-1825-4bfa-99be-580b1a1c625d", "resource_type": "policy"}}	pending	0	2025-08-27 22:30:58.610437+00	\N	2025-08-27 22:30:58.610437+00	2025-08-27 22:30:58.610437+00
020916c1-74ef-4fc2-9ecc-b20f4af19024	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "resource_type": "policy"}}	pending	0	2025-08-28 03:45:18.454539+00	\N	2025-08-28 03:45:18.454539+00	2025-08-28 03:45:18.454539+00
679744a6-26f7-44cc-b3e4-4d9880acf077	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "resource_type": "policy"}}	pending	0	2025-08-28 03:45:51.355972+00	\N	2025-08-28 03:45:51.355972+00	2025-08-28 03:45:51.355972+00
14a529b2-7f7d-4506-b4a1-c6ff8263d5e8	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "resource_type": "policy"}}	pending	0	2025-08-28 04:04:37.642964+00	\N	2025-08-28 04:04:37.642964+00	2025-08-28 04:04:37.642964+00
c4956b63-8b01-4892-81db-91219f888b10	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "resource_type": "policy"}}	pending	0	2025-08-28 04:04:53.021326+00	\N	2025-08-28 04:04:53.021326+00	2025-08-28 04:04:53.021326+00
c8e8fad3-5c22-4b65-92d2-ad964e9e32f6	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "resource_type": "policy"}}	pending	0	2025-08-28 04:17:49.436762+00	\N	2025-08-28 04:17:49.436762+00	2025-08-28 04:17:49.436762+00
da67fb6b-0597-4e13-a4ca-45a8b4d1d158	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "resource_type": "policy"}}	pending	0	2025-08-28 04:18:06.626674+00	\N	2025-08-28 04:18:06.626674+00	2025-08-28 04:18:06.626674+00
78db24e3-b54e-4f4b-8e0d-43671dd0ca04	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "resource_type": "policy"}}	pending	0	2025-08-28 04:27:16.865765+00	\N	2025-08-28 04:27:16.865765+00	2025-08-28 04:27:16.865765+00
813350b6-17cd-441d-a7e6-bea0fd37d193	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "resource_type": "policy"}}	pending	0	2025-08-28 04:29:13.577347+00	\N	2025-08-28 04:29:13.577347+00	2025-08-28 04:29:13.577347+00
c021cdf6-5052-417a-95cf-77282d5bb083	\N	webhook	agent.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "18c5cf42-b62f-4242-9435-738cc9157755", "resource_type": "agent"}}	pending	0	2025-08-28 04:30:10.192057+00	\N	2025-08-28 04:30:10.192057+00	2025-08-28 04:30:10.192057+00
d1616399-e72e-487e-99f8-ac01d3b37c38	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "b1b2c3d4-e5f6-7890-abcd-111111111111", "agent_id": "18c5cf42-b62f-4242-9435-738cc9157755", "group_id": "bf6bde98-2093-4da8-b6d8-3929ef674aff", "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 04:33:27.109254+00	\N	2025-08-28 04:33:27.109254+00	2025-08-28 04:33:27.109254+00
6b1e18b2-ead1-4012-8c17-df7dfd3f8751	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "b1b2c3d4-e5f6-7890-abcd-444444444444", "agent_id": "00033e45-2454-42cc-b247-0c6e3d4e7e06", "group_id": "d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf", "policy_id": "62124064-1825-4bfa-99be-580b1a1c625d"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 04:40:13.324233+00	\N	2025-08-28 04:40:13.324233+00	2025-08-28 04:40:13.324233+00
11dc3d4c-f00a-4b3c-b7e1-0d1edc6d3579	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "b1b2c3d4-e5f6-7890-abcd-333333333333", "agent_id": "1b126746-792f-4a37-b052-537962196a48", "group_id": "11a01996-cb91-40ef-88c1-8e71328ca7f4", "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 04:54:59.449627+00	\N	2025-08-28 04:54:59.449627+00	2025-08-28 04:54:59.449627+00
fbf6c3f8-8de5-4db2-9615-7948ab7e2657	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "b1b2c3d4-e5f6-7890-abcd-444444444444", "agent_id": "c1b2c3d4-e5f6-7890-abcd-111111111111", "group_id": "d1b2c3d4-e5f6-7890-abcd-111111111111", "policy_id": "bd7363f8-6104-449e-873a-db0539f0fa17"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 05:05:26.389862+00	\N	2025-08-28 05:05:26.389862+00	2025-08-28 05:05:26.389862+00
edef86f7-6c98-4843-96b3-c01005785d36	\N	webhook	agent.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "c1b2c3d4-e5f6-7890-abcd-111111111111", "resource_type": "agent"}}	pending	0	2025-08-28 05:07:55.619267+00	\N	2025-08-28 05:07:55.619267+00	2025-08-28 05:07:55.619267+00
40c005a1-6185-4f9e-82ea-4d8a7341fa1b	\N	webhook	agent.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "c1b2c3d4-e5f6-7890-abcd-111111111111", "resource_type": "agent"}}	pending	0	2025-08-28 05:08:06.220713+00	\N	2025-08-28 05:08:06.220713+00	2025-08-28 05:08:06.220713+00
c414c15e-725c-41ab-84f8-6fac63777bbd	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "b1b2c3d4-e5f6-7890-abcd-444444444444", "agent_id": "c1b2c3d4-e5f6-7890-abcd-111111111111", "group_id": "d1b2c3d4-e5f6-7890-abcd-111111111111", "policy_id": "759c48a8-4e86-4430-8da0-73973abba594"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 05:14:04.919087+00	\N	2025-08-28 05:14:04.919087+00	2025-08-28 05:14:04.919087+00
162e234f-9abf-407f-aee5-4d28bda16bbd	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "b1b2c3d4-e5f6-7890-abcd-111111111111", "agent_id": "c1b2c3d4-e5f6-7890-abcd-111111111111", "group_id": "d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf", "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 05:14:25.257781+00	\N	2025-08-28 05:14:25.257781+00	2025-08-28 05:14:25.257781+00
b96a8612-db5c-4f23-b44d-4e80030bb774	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "b1b2c3d4-e5f6-7890-abcd-444444444444", "agent_id": "00033e45-2454-42cc-b247-0c6e3d4e7e06", "group_id": "d1b2c3d4-e5f6-7890-abcd-111111111111", "policy_id": "9b3d812f-bde0-43e9-a211-5d48379f1320"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 05:22:45.41843+00	\N	2025-08-28 05:22:45.41843+00	2025-08-28 05:22:45.41843+00
ce6898a8-cc1f-43bf-8b51-04f9259a6841	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "resource_type": "policy"}}	pending	0	2025-08-28 06:10:34.652454+00	\N	2025-08-28 06:10:34.652454+00	2025-08-28 06:10:34.652454+00
e6daec0e-5fee-40e8-8ef8-3ef530ddb1fc	\N	webhook	assignment.unlinked	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": {"role_id": "b1b2c3d4-e5f6-7890-abcd-333333333333", "agent_id": "1b126746-792f-4a37-b052-537962196a48", "group_id": "11a01996-cb91-40ef-88c1-8e71328ca7f4", "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 06:10:34.657212+00	\N	2025-08-28 06:10:34.657212+00	2025-08-28 06:10:34.657212+00
6efba6dc-db79-4132-bd8b-a2e0778028d1	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "resource_type": "policy"}}	pending	0	2025-08-28 06:10:46.049987+00	\N	2025-08-28 06:10:46.049987+00	2025-08-28 06:10:46.049987+00
d07517b5-6808-4752-b87f-5e6754b92538	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "resource_type": "policy"}}	pending	0	2025-08-28 06:23:03.140227+00	\N	2025-08-28 06:23:03.140227+00	2025-08-28 06:23:03.140227+00
cc9933ff-a027-4ca7-bad7-70b3461ebfac	\N	webhook	assignment.unlinked	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": {"role_id": "b1b2c3d4-e5f6-7890-abcd-111111111111", "agent_id": "18c5cf42-b62f-4242-9435-738cc9157755", "group_id": "bf6bde98-2093-4da8-b6d8-3929ef674aff", "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 06:23:03.143659+00	\N	2025-08-28 06:23:03.143659+00	2025-08-28 06:23:03.143659+00
8e6fb16d-5b0d-414b-a721-088a5605b577	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "resource_type": "policy"}}	pending	0	2025-08-28 06:29:34.94031+00	\N	2025-08-28 06:29:34.94031+00	2025-08-28 06:29:34.94031+00
19a66b8c-b9cf-4976-abbb-279aab04631e	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "resource_type": "policy"}}	pending	0	2025-08-28 19:37:57.463989+00	\N	2025-08-28 19:37:57.463989+00	2025-08-28 19:37:57.463989+00
80ba941b-454a-4d46-989e-d65d17e9a7df	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "c3bd8586-ce3f-4d21-bdb0-f1afb644fc4a", "resource_type": "policy"}}	pending	0	2025-08-28 19:59:32.192178+00	\N	2025-08-28 19:59:32.192178+00	2025-08-28 19:59:32.192178+00
cf8a8c24-37df-4304-b62d-b4663ae1e0da	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "2ada8019-e800-4aed-a43d-69f7c461b7f0", "resource_type": "policy"}}	pending	0	2025-08-28 20:06:24.582913+00	\N	2025-08-28 20:06:24.582913+00	2025-08-28 20:06:24.582913+00
cd2684c1-e1b6-4e5d-ad4e-90f437ee3ba4	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "resource_type": "policy"}}	pending	0	2025-08-28 20:15:26.894453+00	\N	2025-08-28 20:15:26.894453+00	2025-08-28 20:15:26.894453+00
34fd9032-f86c-45e8-a71f-2d9fbcfe0ff8	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "resource_type": "policy"}}	pending	0	2025-08-28 20:15:40.781468+00	\N	2025-08-28 20:15:40.781468+00	2025-08-28 20:15:40.781468+00
11abd3d0-822a-4d87-8daf-c80c804fa298	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "f2d309ee-d92c-4f88-aadc-d714e08d286c", "resource_type": "policy"}}	pending	0	2025-08-28 20:30:12.219829+00	\N	2025-08-28 20:30:12.219829+00	2025-08-28 20:30:12.219829+00
08c60d9c-8aec-415a-93e6-5d5ef7f0beee	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "f2d309ee-d92c-4f88-aadc-d714e08d286c", "resource_type": "policy"}}	pending	0	2025-08-28 20:30:12.232005+00	\N	2025-08-28 20:30:12.232005+00	2025-08-28 20:30:12.232005+00
99ab4b84-4504-40fe-b255-215d195423d1	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "ea7ec861-35f9-4897-9c04-b5b05823ca32", "resource_type": "policy"}}	pending	0	2025-08-28 20:33:22.832913+00	\N	2025-08-28 20:33:22.832913+00	2025-08-28 20:33:22.832913+00
3770e087-5550-4c45-8562-ab5ada28280c	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "ea7ec861-35f9-4897-9c04-b5b05823ca32", "resource_type": "policy"}}	pending	0	2025-08-28 20:33:22.839088+00	\N	2025-08-28 20:33:22.839088+00	2025-08-28 20:33:22.839088+00
d2a6d3d2-7db8-4cc2-ac2e-3cb941b54f15	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "d58edf2e-641b-496b-b92e-d0c297f9684a", "resource_type": "policy"}}	pending	0	2025-08-28 20:33:44.010399+00	\N	2025-08-28 20:33:44.010399+00	2025-08-28 20:33:44.010399+00
ac8020e0-fa95-4127-ae0d-73b99f3fb619	\N	webhook	agent.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "e642cccc-6fca-4fcb-9735-738cb0acb708", "resource_type": "agent"}}	pending	0	2025-08-28 20:33:44.01701+00	\N	2025-08-28 20:33:44.01701+00	2025-08-28 20:33:44.01701+00
b3947a8c-4f21-4057-8a7a-ca8c7bf6f6d9	\N	webhook	agent.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "e642cccc-6fca-4fcb-9735-738cb0acb708", "resource_type": "agent"}}	pending	0	2025-08-28 20:33:44.02236+00	\N	2025-08-28 20:33:44.02236+00	2025-08-28 20:33:44.02236+00
708b2fe0-8305-4eba-b2d3-d34d76854a21	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "d58edf2e-641b-496b-b92e-d0c297f9684a", "resource_type": "policy"}}	pending	0	2025-08-28 20:33:44.024953+00	\N	2025-08-28 20:33:44.024953+00	2025-08-28 20:33:44.024953+00
37f53fdc-f1e4-4318-95bb-f70dcdcebe80	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "afc5a3cf-82e8-450b-a846-2c4529ac170b", "resource_type": "policy"}}	pending	0	2025-08-28 20:34:19.678302+00	\N	2025-08-28 20:34:19.678302+00	2025-08-28 20:34:19.678302+00
f40fed8a-29c4-4b67-a2a0-e6cfd0ea0b92	\N	webhook	agent.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "26c84573-d313-4fd6-92db-c1c85bd0fbd6", "resource_type": "agent"}}	pending	0	2025-08-28 20:34:19.685529+00	\N	2025-08-28 20:34:19.685529+00	2025-08-28 20:34:19.685529+00
814b5448-81ca-4907-ab1e-60008f5c9448	\N	webhook	role.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "0dcc858a-5610-4abc-952b-67a3ede00efc", "resource_type": "role"}}	pending	0	2025-08-28 20:34:19.688929+00	\N	2025-08-28 20:34:19.688929+00	2025-08-28 20:34:19.688929+00
8a1380fa-22a6-4de2-bdf6-1c613c05573c	\N	webhook	agent.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "26c84573-d313-4fd6-92db-c1c85bd0fbd6", "resource_type": "agent"}}	pending	0	2025-08-28 20:34:19.694526+00	\N	2025-08-28 20:34:19.694526+00	2025-08-28 20:34:19.694526+00
430c9416-e3db-49f5-8b46-6f6ba2c2a148	\N	webhook	role.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "0dcc858a-5610-4abc-952b-67a3ede00efc", "resource_type": "role"}}	pending	0	2025-08-28 20:34:19.702472+00	\N	2025-08-28 20:34:19.702472+00	2025-08-28 20:34:19.702472+00
4d6e6c86-f5d2-4f04-b9f3-87ec16a67776	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "afc5a3cf-82e8-450b-a846-2c4529ac170b", "resource_type": "policy"}}	pending	0	2025-08-28 20:34:19.705098+00	\N	2025-08-28 20:34:19.705098+00	2025-08-28 20:34:19.705098+00
b515ab99-8952-41bb-bd57-1d00f5c7041f	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "8d723cde-418b-4c56-94de-f6ff3edb7703", "resource_type": "policy"}}	pending	0	2025-08-28 20:34:58.737495+00	\N	2025-08-28 20:34:58.737495+00	2025-08-28 20:34:58.737495+00
da526104-9b43-450c-916f-09f407560bee	\N	webhook	agent.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "04142d0a-1150-4baa-9cef-fb2cc1a39470", "resource_type": "agent"}}	pending	0	2025-08-28 20:34:58.744676+00	\N	2025-08-28 20:34:58.744676+00	2025-08-28 20:34:58.744676+00
f15036df-ba72-46f6-8137-68cd1fcde0df	\N	webhook	role.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "c281f81a-761e-4502-8d6a-9fc0e4e24872", "resource_type": "role"}}	pending	0	2025-08-28 20:34:58.747982+00	\N	2025-08-28 20:34:58.747982+00	2025-08-28 20:34:58.747982+00
fd36376d-a799-4de4-b450-7da883002215	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "c281f81a-761e-4502-8d6a-9fc0e4e24872", "agent_id": "04142d0a-1150-4baa-9cef-fb2cc1a39470", "group_id": "7dcc8340-2f3a-4a2d-9e85-b821940a6560", "policy_id": "8d723cde-418b-4c56-94de-f6ff3edb7703"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 20:34:58.751999+00	\N	2025-08-28 20:34:58.751999+00	2025-08-28 20:34:58.751999+00
50f13072-0271-4db4-b57e-c20bb93a2f54	\N	webhook	agent.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "04142d0a-1150-4baa-9cef-fb2cc1a39470", "resource_type": "agent"}}	pending	0	2025-08-28 20:34:58.75941+00	\N	2025-08-28 20:34:58.75941+00	2025-08-28 20:34:58.75941+00
48e81a82-bd0e-4010-8c96-78e67ad2ae0f	\N	webhook	assignment.unlinked	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": {"role_id": "c281f81a-761e-4502-8d6a-9fc0e4e24872", "agent_id": "04142d0a-1150-4baa-9cef-fb2cc1a39470", "group_id": "7dcc8340-2f3a-4a2d-9e85-b821940a6560", "policy_id": "8d723cde-418b-4c56-94de-f6ff3edb7703"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 20:34:58.75941+00	\N	2025-08-28 20:34:58.75941+00	2025-08-28 20:34:58.75941+00
fe404625-d53b-4caf-be71-420715a20bea	\N	webhook	role.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "c281f81a-761e-4502-8d6a-9fc0e4e24872", "resource_type": "role"}}	pending	0	2025-08-28 20:34:58.762319+00	\N	2025-08-28 20:34:58.762319+00	2025-08-28 20:34:58.762319+00
9d0deb34-d831-4ee6-b6d5-b7bf16a5dc6b	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "8d723cde-418b-4c56-94de-f6ff3edb7703", "resource_type": "policy"}}	pending	0	2025-08-28 20:34:58.764096+00	\N	2025-08-28 20:34:58.764096+00	2025-08-28 20:34:58.764096+00
16337306-cffe-4faf-9a39-6b63fcd711cc	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "19fe1b8d-89f4-4019-8f59-2d08382d356d", "resource_type": "policy"}}	pending	0	2025-08-28 20:35:22.651548+00	\N	2025-08-28 20:35:22.651548+00	2025-08-28 20:35:22.651548+00
d267e967-7312-4821-bc76-78d2e1bc92a4	\N	webhook	agent.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "4830f887-f172-4476-8045-dcaacbd20142", "resource_type": "agent"}}	pending	0	2025-08-28 20:35:22.658573+00	\N	2025-08-28 20:35:22.658573+00	2025-08-28 20:35:22.658573+00
e495aff0-9c07-4b21-a9ed-64c3f03e64a4	\N	webhook	role.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "6123f679-d34c-4b99-abea-46249bd69db1", "resource_type": "role"}}	pending	0	2025-08-28 20:35:22.661412+00	\N	2025-08-28 20:35:22.661412+00	2025-08-28 20:35:22.661412+00
36a0a807-476a-4303-b906-44376e3ca9a4	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "6123f679-d34c-4b99-abea-46249bd69db1", "agent_id": "4830f887-f172-4476-8045-dcaacbd20142", "group_id": "b82ce312-7cda-43a6-b3ef-a97b58e19e17", "policy_id": "19fe1b8d-89f4-4019-8f59-2d08382d356d"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 20:35:22.664697+00	\N	2025-08-28 20:35:22.664697+00	2025-08-28 20:35:22.664697+00
a209ff5c-3e14-4e13-acb2-872f3c8b9f18	\N	webhook	agent.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "4830f887-f172-4476-8045-dcaacbd20142", "resource_type": "agent"}}	pending	0	2025-08-28 20:35:22.675619+00	\N	2025-08-28 20:35:22.675619+00	2025-08-28 20:35:22.675619+00
f9af634b-7dee-49c5-bbc8-ea4768072640	\N	webhook	assignment.unlinked	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": {"role_id": "6123f679-d34c-4b99-abea-46249bd69db1", "agent_id": "4830f887-f172-4476-8045-dcaacbd20142", "group_id": "b82ce312-7cda-43a6-b3ef-a97b58e19e17", "policy_id": "19fe1b8d-89f4-4019-8f59-2d08382d356d"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 20:35:22.675619+00	\N	2025-08-28 20:35:22.675619+00	2025-08-28 20:35:22.675619+00
5010b51b-02a8-4ceb-8d06-16e837a29bac	\N	webhook	role.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "6123f679-d34c-4b99-abea-46249bd69db1", "resource_type": "role"}}	pending	0	2025-08-28 20:35:22.678292+00	\N	2025-08-28 20:35:22.678292+00	2025-08-28 20:35:22.678292+00
70f4111e-bcf8-441d-be40-3154cf30362a	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "19fe1b8d-89f4-4019-8f59-2d08382d356d", "resource_type": "policy"}}	pending	0	2025-08-28 20:35:22.680147+00	\N	2025-08-28 20:35:22.680147+00	2025-08-28 20:35:22.680147+00
07fbe2ec-a8bb-4acb-ad6d-1474bb97ad95	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "e1414965-9a7d-4fdb-aa06-666be8dc37bf", "resource_type": "policy"}}	pending	0	2025-08-28 20:35:48.218259+00	\N	2025-08-28 20:35:48.218259+00	2025-08-28 20:35:48.218259+00
028897dd-7283-4c2e-b641-4ff18af9842f	\N	webhook	agent.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "7b96617b-aa43-4bab-82ea-7a8bce9160e4", "resource_type": "agent"}}	pending	0	2025-08-28 20:35:48.230882+00	\N	2025-08-28 20:35:48.230882+00	2025-08-28 20:35:48.230882+00
241b3091-ae89-4252-b003-99d486b29bbb	\N	webhook	role.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "28db8e6c-4f60-4d7d-b706-ad3e181da2bb", "resource_type": "role"}}	pending	0	2025-08-28 20:35:48.235633+00	\N	2025-08-28 20:35:48.235633+00	2025-08-28 20:35:48.235633+00
9fe7c5bf-a49b-43b3-a55e-b4a6aea71e06	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "28db8e6c-4f60-4d7d-b706-ad3e181da2bb", "agent_id": "7b96617b-aa43-4bab-82ea-7a8bce9160e4", "group_id": "3884f39f-c810-46e8-ad63-9353c491574a", "policy_id": "e1414965-9a7d-4fdb-aa06-666be8dc37bf"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 20:35:48.238924+00	\N	2025-08-28 20:35:48.238924+00	2025-08-28 20:35:48.238924+00
25df8361-a75e-4f6d-9859-ed132ed444db	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "e1414965-9a7d-4fdb-aa06-666be8dc37bf", "resource_type": "policy"}}	pending	0	2025-08-28 20:35:48.247241+00	\N	2025-08-28 20:35:48.247241+00	2025-08-28 20:35:48.247241+00
90779e15-5e48-4889-88a4-1d73ff00880e	\N	webhook	agent.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "7b96617b-aa43-4bab-82ea-7a8bce9160e4", "resource_type": "agent"}}	pending	0	2025-08-28 20:35:49.265849+00	\N	2025-08-28 20:35:49.265849+00	2025-08-28 20:35:49.265849+00
296aa624-e52d-4e0f-a91a-524d65a5d9de	\N	webhook	assignment.unlinked	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": {"role_id": "28db8e6c-4f60-4d7d-b706-ad3e181da2bb", "agent_id": "7b96617b-aa43-4bab-82ea-7a8bce9160e4", "group_id": "3884f39f-c810-46e8-ad63-9353c491574a", "policy_id": "e1414965-9a7d-4fdb-aa06-666be8dc37bf"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 20:35:49.265849+00	\N	2025-08-28 20:35:49.265849+00	2025-08-28 20:35:49.265849+00
a4b0532c-2fa2-4611-8f44-f4687e9f5ae5	\N	webhook	role.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "28db8e6c-4f60-4d7d-b706-ad3e181da2bb", "resource_type": "role"}}	pending	0	2025-08-28 20:35:49.273201+00	\N	2025-08-28 20:35:49.273201+00	2025-08-28 20:35:49.273201+00
5fbb5c97-73a4-4ced-aa93-fa5719aa9583	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "3ffa6959-db5b-492a-a963-944763a7c7b1", "resource_type": "policy"}}	pending	0	2025-08-28 20:36:23.0652+00	\N	2025-08-28 20:36:23.0652+00	2025-08-28 20:36:23.0652+00
37c7266f-4e87-4aa4-a227-ac3b1a623975	\N	webhook	agent.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "522e9c4c-c82a-4ff7-8718-ad6665658f2b", "resource_type": "agent"}}	pending	0	2025-08-28 20:36:23.07189+00	\N	2025-08-28 20:36:23.07189+00	2025-08-28 20:36:23.07189+00
5e7f8623-9313-4f76-97a1-538203c2127a	\N	webhook	role.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "4519f7b7-580b-401c-9477-d8910e4c4073", "resource_type": "role"}}	pending	0	2025-08-28 20:36:23.074699+00	\N	2025-08-28 20:36:23.074699+00	2025-08-28 20:36:23.074699+00
fb0f85ff-5c7d-4bda-93fc-3317623a56fd	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "4519f7b7-580b-401c-9477-d8910e4c4073", "agent_id": "522e9c4c-c82a-4ff7-8718-ad6665658f2b", "group_id": "b504593d-3a44-455d-8fe1-59656ea1753a", "policy_id": "3ffa6959-db5b-492a-a963-944763a7c7b1"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 20:36:23.07764+00	\N	2025-08-28 20:36:23.07764+00	2025-08-28 20:36:23.07764+00
255c0ab7-39f1-40ad-b0d5-4f231868dbef	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "3ffa6959-db5b-492a-a963-944763a7c7b1", "resource_type": "policy"}}	pending	0	2025-08-28 20:36:23.085235+00	\N	2025-08-28 20:36:23.085235+00	2025-08-28 20:36:23.085235+00
5f6df345-9126-4467-a031-5398a12e98ca	\N	webhook	agent.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "522e9c4c-c82a-4ff7-8718-ad6665658f2b", "resource_type": "agent"}}	pending	0	2025-08-28 20:36:24.104324+00	\N	2025-08-28 20:36:24.104324+00	2025-08-28 20:36:24.104324+00
1d75f248-4e35-457e-a820-9a8440ea98df	\N	webhook	assignment.unlinked	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": {"role_id": "4519f7b7-580b-401c-9477-d8910e4c4073", "agent_id": "522e9c4c-c82a-4ff7-8718-ad6665658f2b", "group_id": "b504593d-3a44-455d-8fe1-59656ea1753a", "policy_id": "3ffa6959-db5b-492a-a963-944763a7c7b1"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 20:36:24.104324+00	\N	2025-08-28 20:36:24.104324+00	2025-08-28 20:36:24.104324+00
1e1988c8-72a5-4586-ba40-d796f2df4040	\N	webhook	role.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "4519f7b7-580b-401c-9477-d8910e4c4073", "resource_type": "role"}}	pending	0	2025-08-28 20:36:24.115449+00	\N	2025-08-28 20:36:24.115449+00	2025-08-28 20:36:24.115449+00
b25ce725-fffb-46fe-9c88-b745e81f2725	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "1604904e-f5d0-4842-b59c-a11390052789", "resource_type": "policy"}}	pending	0	2025-08-28 20:36:39.977802+00	\N	2025-08-28 20:36:39.977802+00	2025-08-28 20:36:39.977802+00
3c099004-cad3-42c7-8ce9-beb17f3ec4aa	\N	webhook	agent.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "caea56ae-3fca-4429-a971-63876ead6279", "resource_type": "agent"}}	pending	0	2025-08-28 20:36:39.985562+00	\N	2025-08-28 20:36:39.985562+00	2025-08-28 20:36:39.985562+00
9bdc962e-aff1-4b4e-b1cd-5d2433c9d50d	\N	webhook	role.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "6e5b3ebc-7db5-4b8a-9ae2-411b5a93ac62", "resource_type": "role"}}	pending	0	2025-08-28 20:36:39.988616+00	\N	2025-08-28 20:36:39.988616+00	2025-08-28 20:36:39.988616+00
bbebc117-dc96-458f-9926-f9b55cac99ba	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "6e5b3ebc-7db5-4b8a-9ae2-411b5a93ac62", "agent_id": "caea56ae-3fca-4429-a971-63876ead6279", "group_id": "3ec7f725-252e-43a7-bcd7-82e8d7b82d9f", "policy_id": "1604904e-f5d0-4842-b59c-a11390052789"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 20:36:39.993922+00	\N	2025-08-28 20:36:39.993922+00	2025-08-28 20:36:39.993922+00
29a54ed2-a0c0-47bd-98a7-d2bb30349df3	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "1604904e-f5d0-4842-b59c-a11390052789", "resource_type": "policy"}}	pending	0	2025-08-28 20:36:40.007286+00	\N	2025-08-28 20:36:40.007286+00	2025-08-28 20:36:40.007286+00
b5a962aa-8f6f-4727-aa95-7375df246c0d	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "4eccd6ca-bc28-428d-9e0d-0cdc4257fa33", "resource_type": "policy"}}	pending	0	2025-08-28 20:36:41.032397+00	\N	2025-08-28 20:36:41.032397+00	2025-08-28 20:36:41.032397+00
dad4dc10-c42e-4ba2-bdc0-199c3eed7843	\N	webhook	agent.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "caea56ae-3fca-4429-a971-63876ead6279", "resource_type": "agent"}}	pending	0	2025-08-28 20:36:41.050291+00	\N	2025-08-28 20:36:41.050291+00	2025-08-28 20:36:41.050291+00
84727901-6951-469a-8371-4f90474f31d8	\N	webhook	assignment.unlinked	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": {"role_id": "6e5b3ebc-7db5-4b8a-9ae2-411b5a93ac62", "agent_id": "caea56ae-3fca-4429-a971-63876ead6279", "group_id": "3ec7f725-252e-43a7-bcd7-82e8d7b82d9f", "policy_id": "1604904e-f5d0-4842-b59c-a11390052789"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 20:36:41.050291+00	\N	2025-08-28 20:36:41.050291+00	2025-08-28 20:36:41.050291+00
7473cf3b-2f08-4abe-a212-5a621934f288	\N	webhook	role.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "6e5b3ebc-7db5-4b8a-9ae2-411b5a93ac62", "resource_type": "role"}}	pending	0	2025-08-28 20:36:41.054338+00	\N	2025-08-28 20:36:41.054338+00	2025-08-28 20:36:41.054338+00
735e5a50-2b1f-46d7-8e8a-d2d2eca9a047	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "4eccd6ca-bc28-428d-9e0d-0cdc4257fa33", "resource_type": "policy"}}	pending	0	2025-08-28 20:36:41.060083+00	\N	2025-08-28 20:36:41.060083+00	2025-08-28 20:36:41.060083+00
1005b469-bd9f-4c83-9295-e069c2a83648	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "80073610-ee06-49db-bd2a-531c5c39a76c", "resource_type": "policy"}}	pending	0	2025-08-28 20:38:03.465342+00	\N	2025-08-28 20:38:03.465342+00	2025-08-28 20:38:03.465342+00
1450147d-b107-4e76-9c1c-0b0178793516	\N	webhook	agent.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "908fdc68-1338-4a54-b3cb-6f6f632b96fd", "resource_type": "agent"}}	pending	0	2025-08-28 20:38:03.473726+00	\N	2025-08-28 20:38:03.473726+00	2025-08-28 20:38:03.473726+00
c4cc985e-530a-4bf3-bd7c-5a74234baf16	\N	webhook	role.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "8f482811-77fa-4661-b79e-597049b7aaef", "resource_type": "role"}}	pending	0	2025-08-28 20:38:03.478046+00	\N	2025-08-28 20:38:03.478046+00	2025-08-28 20:38:03.478046+00
b8ca44f8-6f56-442a-b86d-c309ce6bb6c4	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "8f482811-77fa-4661-b79e-597049b7aaef", "agent_id": "908fdc68-1338-4a54-b3cb-6f6f632b96fd", "group_id": "5c51e454-aa47-4bfe-b81a-815be0cce9cb", "policy_id": "80073610-ee06-49db-bd2a-531c5c39a76c"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 20:38:03.482336+00	\N	2025-08-28 20:38:03.482336+00	2025-08-28 20:38:03.482336+00
e59f8b65-f983-479d-9287-e13768ee4ce0	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "80073610-ee06-49db-bd2a-531c5c39a76c", "resource_type": "policy"}}	pending	0	2025-08-28 20:38:03.489504+00	\N	2025-08-28 20:38:03.489504+00	2025-08-28 20:38:03.489504+00
01a648ae-088a-4a38-8d36-ce63c1ff88f7	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "0c1e7f1c-18a6-4df9-973d-55fe90202b0f", "resource_type": "policy"}}	pending	0	2025-08-28 20:38:04.515031+00	\N	2025-08-28 20:38:04.515031+00	2025-08-28 20:38:04.515031+00
846392bf-ee28-478e-a7e8-9fe1c0e4fc75	\N	webhook	agent.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "908fdc68-1338-4a54-b3cb-6f6f632b96fd", "resource_type": "agent"}}	pending	0	2025-08-28 20:38:05.536005+00	\N	2025-08-28 20:38:05.536005+00	2025-08-28 20:38:05.536005+00
f50aac15-7e73-45e7-85e4-776a237fecd0	\N	webhook	assignment.unlinked	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": {"role_id": "8f482811-77fa-4661-b79e-597049b7aaef", "agent_id": "908fdc68-1338-4a54-b3cb-6f6f632b96fd", "group_id": "5c51e454-aa47-4bfe-b81a-815be0cce9cb", "policy_id": "80073610-ee06-49db-bd2a-531c5c39a76c"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 20:38:05.536005+00	\N	2025-08-28 20:38:05.536005+00	2025-08-28 20:38:05.536005+00
31d17309-ad6c-4417-bedb-ec717c0c39d3	\N	webhook	role.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "8f482811-77fa-4661-b79e-597049b7aaef", "resource_type": "role"}}	pending	0	2025-08-28 20:38:05.563573+00	\N	2025-08-28 20:38:05.563573+00	2025-08-28 20:38:05.563573+00
a673a444-adb1-4cfb-9675-78340d118c1b	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "0c1e7f1c-18a6-4df9-973d-55fe90202b0f", "resource_type": "policy"}}	pending	0	2025-08-28 20:38:05.569562+00	\N	2025-08-28 20:38:05.569562+00	2025-08-28 20:38:05.569562+00
a9042287-eba5-4fe2-a6ac-58dd37228365	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "3c35d5ca-164b-4b75-bf0b-596fa2ee2627", "resource_type": "policy"}}	pending	0	2025-08-28 20:38:45.811519+00	\N	2025-08-28 20:38:45.811519+00	2025-08-28 20:38:45.811519+00
47255fa3-3ea5-4d9d-aadd-3d4ef6693f58	\N	webhook	agent.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "09142445-77ea-4e31-b4e3-1ba07c389068", "resource_type": "agent"}}	pending	0	2025-08-28 20:38:45.819198+00	\N	2025-08-28 20:38:45.819198+00	2025-08-28 20:38:45.819198+00
dc6383a3-c556-4be4-9671-0c3b8d739627	\N	webhook	role.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "30086819-845b-42fc-8f09-9a042841eab5", "resource_type": "role"}}	pending	0	2025-08-28 20:38:45.822419+00	\N	2025-08-28 20:38:45.822419+00	2025-08-28 20:38:45.822419+00
0b74742e-b17c-4a8e-8366-e4e12dd53422	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "30086819-845b-42fc-8f09-9a042841eab5", "agent_id": "09142445-77ea-4e31-b4e3-1ba07c389068", "group_id": "05c16aef-9eb0-4452-9b46-57aeb4f6e3e5", "policy_id": "3c35d5ca-164b-4b75-bf0b-596fa2ee2627"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 20:38:45.825731+00	\N	2025-08-28 20:38:45.825731+00	2025-08-28 20:38:45.825731+00
0648811a-f85d-4686-a8ad-6eb967f16c78	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "3c35d5ca-164b-4b75-bf0b-596fa2ee2627", "resource_type": "policy"}}	pending	0	2025-08-28 20:38:45.835047+00	\N	2025-08-28 20:38:45.835047+00	2025-08-28 20:38:45.835047+00
1eeaa779-18d7-4eec-8a77-4c6bc04f5c14	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "7ee9ae41-de86-4244-90f8-25c2da0623b2", "resource_type": "policy"}}	pending	0	2025-08-28 20:38:46.854796+00	\N	2025-08-28 20:38:46.854796+00	2025-08-28 20:38:46.854796+00
2901e265-9a79-42a6-9f42-a0a725bce8f5	\N	webhook	agent.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "09142445-77ea-4e31-b4e3-1ba07c389068", "resource_type": "agent"}}	pending	0	2025-08-28 20:38:47.878617+00	\N	2025-08-28 20:38:47.878617+00	2025-08-28 20:38:47.878617+00
3201f6ef-30a6-4366-b03c-2fd0b007fce7	\N	webhook	assignment.unlinked	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": {"role_id": "30086819-845b-42fc-8f09-9a042841eab5", "agent_id": "09142445-77ea-4e31-b4e3-1ba07c389068", "group_id": "05c16aef-9eb0-4452-9b46-57aeb4f6e3e5", "policy_id": "3c35d5ca-164b-4b75-bf0b-596fa2ee2627"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 20:38:47.878617+00	\N	2025-08-28 20:38:47.878617+00	2025-08-28 20:38:47.878617+00
922f6aca-de68-45f5-bde6-71760930bb47	\N	webhook	role.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "30086819-845b-42fc-8f09-9a042841eab5", "resource_type": "role"}}	pending	0	2025-08-28 20:38:47.901955+00	\N	2025-08-28 20:38:47.901955+00	2025-08-28 20:38:47.901955+00
90a2306f-e08f-4a6b-8457-1379ced23750	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "7ee9ae41-de86-4244-90f8-25c2da0623b2", "resource_type": "policy"}}	pending	0	2025-08-28 20:38:47.906441+00	\N	2025-08-28 20:38:47.906441+00	2025-08-28 20:38:47.906441+00
d832d0a4-3b4e-4c9c-9f8d-f037b3a4b0eb	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "6f2c6077-2364-478f-96e6-c2196d623b4e", "resource_type": "policy"}}	pending	0	2025-08-28 20:45:38.827176+00	\N	2025-08-28 20:45:38.827176+00	2025-08-28 20:45:38.827176+00
904034fb-0eb6-46e6-8dd5-d9215806977f	\N	webhook	agent.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "bee0c915-2c54-446f-98c0-bc6530a01d43", "resource_type": "agent"}}	pending	0	2025-08-28 20:45:38.833137+00	\N	2025-08-28 20:45:38.833137+00	2025-08-28 20:45:38.833137+00
dfb4b2ef-f056-41d0-9e3c-19440e4d6cba	\N	webhook	role.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "63db9c9d-b64d-452c-9d54-194005f0c329", "resource_type": "role"}}	pending	0	2025-08-28 20:45:38.835879+00	\N	2025-08-28 20:45:38.835879+00	2025-08-28 20:45:38.835879+00
3839b374-deac-4c6a-93de-d4c87d58d37b	\N	webhook	agent.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "bee0c915-2c54-446f-98c0-bc6530a01d43", "resource_type": "agent"}}	pending	0	2025-08-28 20:45:38.84003+00	\N	2025-08-28 20:45:38.84003+00	2025-08-28 20:45:38.84003+00
d558c607-d981-4d9a-bb62-d9ebd06f34b2	\N	webhook	role.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "63db9c9d-b64d-452c-9d54-194005f0c329", "resource_type": "role"}}	pending	0	2025-08-28 20:45:38.843089+00	\N	2025-08-28 20:45:38.843089+00	2025-08-28 20:45:38.843089+00
8367dced-8ca0-4752-a333-79618ad613d9	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "6f2c6077-2364-478f-96e6-c2196d623b4e", "resource_type": "policy"}}	pending	0	2025-08-28 20:45:38.844675+00	\N	2025-08-28 20:45:38.844675+00	2025-08-28 20:45:38.844675+00
2a863b65-07c5-4996-9aac-79ed84b93f2b	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "96c01e28-12b6-4aac-a83f-15bb4527f26a", "resource_type": "policy"}}	pending	0	2025-08-28 20:50:22.925421+00	\N	2025-08-28 20:50:22.925421+00	2025-08-28 20:50:22.925421+00
ce39d13b-3d97-4e54-ba56-7590444fdc70	\N	webhook	agent.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "b5f6e071-2525-4168-9cac-c6482dce1739", "resource_type": "agent"}}	pending	0	2025-08-28 20:50:22.937983+00	\N	2025-08-28 20:50:22.937983+00	2025-08-28 20:50:22.937983+00
a998d445-0d23-4edf-8625-190b223abc44	\N	webhook	role.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "b19d20da-80cc-4626-ac74-4d57160829c8", "resource_type": "role"}}	pending	0	2025-08-28 20:50:22.940934+00	\N	2025-08-28 20:50:22.940934+00	2025-08-28 20:50:22.940934+00
590de0ea-c1a1-4eec-9c83-6e8778034a29	\N	webhook	agent.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "b5f6e071-2525-4168-9cac-c6482dce1739", "resource_type": "agent"}}	pending	0	2025-08-28 20:50:22.945167+00	\N	2025-08-28 20:50:22.945167+00	2025-08-28 20:50:22.945167+00
999a1cc6-db81-4cc1-b30a-204356a31912	\N	webhook	role.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "b19d20da-80cc-4626-ac74-4d57160829c8", "resource_type": "role"}}	pending	0	2025-08-28 20:50:22.947732+00	\N	2025-08-28 20:50:22.947732+00	2025-08-28 20:50:22.947732+00
6b018d81-487f-44ba-b281-15fedd226d23	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "96c01e28-12b6-4aac-a83f-15bb4527f26a", "resource_type": "policy"}}	pending	0	2025-08-28 20:50:22.949454+00	\N	2025-08-28 20:50:22.949454+00	2025-08-28 20:50:22.949454+00
19e561f9-99b9-4398-916f-0f3853b16fdc	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "3e428b15-e58c-4ef9-ab58-bfe6303ecf4f", "resource_type": "policy"}}	pending	0	2025-08-28 20:50:47.788242+00	\N	2025-08-28 20:50:47.788242+00	2025-08-28 20:50:47.788242+00
add4a787-40e6-4322-a2ed-3cfaa026b3c3	\N	webhook	agent.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "f68ae0c1-9a7f-43c8-83f6-509b9ddf1ede", "resource_type": "agent"}}	pending	0	2025-08-28 20:50:47.795063+00	\N	2025-08-28 20:50:47.795063+00	2025-08-28 20:50:47.795063+00
f12e9d72-ba2f-465a-8f86-6d975b4b3a9a	\N	webhook	role.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "942e55df-944d-413a-9270-b00a25156c7d", "resource_type": "role"}}	pending	0	2025-08-28 20:50:47.798005+00	\N	2025-08-28 20:50:47.798005+00	2025-08-28 20:50:47.798005+00
8336ec24-8354-4311-9d59-2fbe7b6de97d	\N	webhook	assignment.linked	1	{"data": {"change_type": "created"}, "subject": {"resource_id": {"role_id": "942e55df-944d-413a-9270-b00a25156c7d", "agent_id": "f68ae0c1-9a7f-43c8-83f6-509b9ddf1ede", "group_id": "22106b70-899a-4851-8f03-d858b0589f01", "policy_id": "3e428b15-e58c-4ef9-ab58-bfe6303ecf4f"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 20:50:47.801058+00	\N	2025-08-28 20:50:47.801058+00	2025-08-28 20:50:47.801058+00
a4f350ca-0f1f-46ae-b43d-98b2ba50c477	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "3e428b15-e58c-4ef9-ab58-bfe6303ecf4f", "resource_type": "policy"}}	pending	0	2025-08-28 20:50:47.809063+00	\N	2025-08-28 20:50:47.809063+00	2025-08-28 20:50:47.809063+00
a1f7e816-00c7-48c3-9dde-2df7d5059477	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "2a052eef-c31d-44e3-92f8-5a9aec35d792", "resource_type": "policy"}}	pending	0	2025-08-28 20:50:48.831468+00	\N	2025-08-28 20:50:48.831468+00	2025-08-28 20:50:48.831468+00
a1ee3e7b-7e2c-4ed7-8ffd-973cf3ae3521	\N	webhook	agent.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "f68ae0c1-9a7f-43c8-83f6-509b9ddf1ede", "resource_type": "agent"}}	pending	0	2025-08-28 20:50:49.852183+00	\N	2025-08-28 20:50:49.852183+00	2025-08-28 20:50:49.852183+00
b4e15e46-1224-4f8f-89e0-a9549888a94c	\N	webhook	assignment.unlinked	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": {"role_id": "942e55df-944d-413a-9270-b00a25156c7d", "agent_id": "f68ae0c1-9a7f-43c8-83f6-509b9ddf1ede", "group_id": "22106b70-899a-4851-8f03-d858b0589f01", "policy_id": "3e428b15-e58c-4ef9-ab58-bfe6303ecf4f"}, "resource_type": "agent_role_policy"}}	pending	0	2025-08-28 20:50:49.852183+00	\N	2025-08-28 20:50:49.852183+00	2025-08-28 20:50:49.852183+00
a8b57859-ad16-460b-a92c-777e9f248773	\N	webhook	role.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "942e55df-944d-413a-9270-b00a25156c7d", "resource_type": "role"}}	pending	0	2025-08-28 20:50:49.872261+00	\N	2025-08-28 20:50:49.872261+00	2025-08-28 20:50:49.872261+00
6ec22a1a-f568-43b4-9220-326a7a7a1c58	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "2a052eef-c31d-44e3-92f8-5a9aec35d792", "resource_type": "policy"}}	pending	0	2025-08-28 20:50:49.87847+00	\N	2025-08-28 20:50:49.87847+00	2025-08-28 20:50:49.87847+00
221166a6-530b-4577-91f7-643733345b0f	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "40a63e03-d616-4877-9f4b-cbf5fcad8cdc", "resource_type": "policy"}}	pending	0	2025-08-29 08:30:16.830696+00	\N	2025-08-29 08:30:16.830696+00	2025-08-29 08:30:16.830696+00
7c269bdf-7319-4211-b275-2ff4e22426d4	\N	webhook	policy.created	1	{"data": {"change_type": "created"}, "subject": {"resource_id": "86d75236-d62b-4f41-8eb9-70047dc986bc", "resource_type": "policy"}}	pending	0	2025-08-29 08:34:22.8302+00	\N	2025-08-29 08:34:22.8302+00	2025-08-29 08:34:22.8302+00
\.


--
-- Data for Name: mcp_chat_sessions; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.mcp_chat_sessions (session_id, user_id, status, created_at, updated_at, metadata) FROM stdin;
\.


--
-- Data for Name: mcp_flow_steps; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.mcp_flow_steps (step_id, session_id, step_number, step_name, status, input_data, output_data, processing_time_ms, policies_applied, violations_detected, created_at, completed_at) FROM stdin;
\.


--
-- Data for Name: openai_api_calls; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.openai_api_calls (call_id, session_id, step_id, model_name, prompt_tokens, completion_tokens, total_tokens, cost_estimate, response_time_ms, status_code, created_at) FROM stdin;
\.


--
-- Data for Name: policies; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policies (policy_id, name, description, category, policy_type, definition, version, is_active, severity, applies_to_roles, created_at, updated_at, created_by, updated_by, rego_code, blob_container, blob_path, blob_url, rego_template_id, opa_sync_status, last_rego_generation, rego_generation_error, rego_version, original_policy_id, cloned_from_policy_name, deleted_at, guardrail_id) FROM stdin;
f1b2c3d4-e5f6-7890-abcd-111111111111	BCBS Minimum Necessary with Phone Redaction	Secure sharing of medical records with automatic PHI redaction	HIPAA Privacy Compliance	opa	{"audit_redaction": true, "redaction_rules": {"phone": "({area_code}) ***-****"}, "redaction_fields": ["phone"], "emergency_override": true, "redaction_required": true, "external_sharing_allowed": false, "sharing_authorized_roles": ["HIPAA_CLINICAL_REVIEWER", "HIPAA_MEDICAL_DIRECTOR", "HIPAA_COMPLIANCE_OFFICER"]}	1	t	high	{HIPAA_CLINICAL_REVIEWER,HIPAA_MEDICAL_DIRECTOR,HIPAA_COMPLIANCE_OFFICER}	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	a1b2c3d4-e5f6-7890-abcd-111111111111	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
f1b2c3d4-e5f6-7890-abcd-222222222222	BCBS Minimum Necessary with Email Redaction	Ensures only minimum necessary PHI is disclosed based on user role	HIPAA Privacy Compliance	opa	{"visibility_rules": {"CASE_MANAGER": ["contact_info", "insurance_info"], "MEDICAL_DIRECTOR": ["all_fields"], "CLINICAL_REVIEWER": ["medical_info", "basic_demographics"], "COMPLIANCE_OFFICER": ["access_logs", "audit_trails"]}, "minimum_data_only": true, "redaction_methods": {"email": "****@****.com"}, "purpose_limitation": true, "pii_fields_to_redact": ["email"], "role_based_visibility": true, "auto_redaction_enabled": true}	1	t	high	{HIPAA_CLINICAL_REVIEWER,HIPAA_MEDICAL_DIRECTOR,HIPAA_CASE_MANAGER}	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	a1b2c3d4-e5f6-7890-abcd-111111111111	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
f1b2c3d4-e5f6-7890-abcd-333333333333	BCBS Minimum Necessary with Auto-Redaction	Ensures only minimum necessary PHI is disclosed based on user role	HIPAA Privacy Compliance	opa	{"visibility_rules": {"CASE_MANAGER": ["contact_info", "insurance_info"], "MEDICAL_DIRECTOR": ["all_fields"], "CLINICAL_REVIEWER": ["medical_info", "basic_demographics"], "COMPLIANCE_OFFICER": ["access_logs", "audit_trails"]}, "minimum_data_only": true, "redaction_methods": {"dob": "{month}/{day}/****", "ssn": "***-**-{last_4}", "email": "****@****.com", "phone": "({area_code}) ***-****", "address": "{city}, {state} {zip}", "insurance_id": "{first_3}*****"}, "purpose_limitation": true, "pii_fields_to_redact": ["ssn", "address", "phone", "email", "dob", "insurance_id"], "role_based_visibility": true, "auto_redaction_enabled": true}	1	t	high	{HIPAA_CLINICAL_REVIEWER,HIPAA_MEDICAL_DIRECTOR,HIPAA_CASE_MANAGER}	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	a1b2c3d4-e5f6-7890-abcd-111111111111	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
bd7363f8-6104-449e-873a-db0539f0fa17	p1	p1	data_privacy	opa	{"type": "data_privacy", "severity": "medium", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}	1	t	medium	{admin}	2025-08-27 18:11:04.643479+00	2025-08-27 18:11:04.643479+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
759c48a8-4e86-4430-8da0-73973abba594	p2	p2	access_control	opa	{"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}	1	t	low	{admin}	2025-08-27 20:07:59.801197+00	2025-08-27 20:07:59.801197+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
4855b086-ab50-4488-9094-58f0be3f2cf3	Test Medical Privacy Policy	Testing complete policy lifecycle with database schemas	medical_privacy	medical_privacy	{"type": "medical_privacy", "enabled": true, "severity": "high", "allowed_roles": ["doctor", "nurse", "admin"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders", "medical_record_number"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}	1	\N	high	\N	2025-08-27 21:38:06.072074+00	2025-08-27 21:38:06.072074+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
9b3d812f-bde0-43e9-a211-5d48379f1320	p3	p3	medical_privacy	opa	{"type": "medical_privacy", "severity": "medium", "allowed_roles": ["doctor"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}	1	t	medium	{doctor}	2025-08-27 22:01:37.878715+00	2025-08-27 22:01:37.878715+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d	p4	p4	access_control	opa	{"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}	1	t	low	{admin}	2025-08-27 22:02:54.811707+00	2025-08-27 22:02:54.811707+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
00accc7f-7fd6-42cd-acbd-e9016b48eaec	Test Database Schema Policy	Testing policy creation with database schemas	Access Control	access_control	{"type": "access_control", "enabled": true, "severity": "medium", "allowed_roles": ["admin", "user"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}	1	\N	medium	\N	2025-08-27 22:17:01.52259+00	2025-08-27 22:17:01.52259+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
3c7b0cb1-0c84-4c64-8ebb-bedc7c262e53	Test DB Schema Policy 1756333061	Testing policy creation with database schemas	Access Control	access_control	{"type": "access_control", "enabled": true, "severity": "medium", "allowed_roles": ["admin", "user"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}	1	\N	medium	\N	2025-08-27 22:17:41.276739+00	2025-08-27 22:17:41.276739+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
f4c1a348-7f94-4ed5-908c-4aee04e82f37	Test DB Schema Policy 1756333083	Testing policy creation with database schemas	Access Control	access_control	{"type": "access_control", "enabled": true, "severity": "medium", "allowed_roles": ["admin", "user"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}	1	\N	medium	\N	2025-08-27 22:18:03.518299+00	2025-08-27 22:18:03.518299+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
62124064-1825-4bfa-99be-580b1a1c625d	p5	p5	access_control	opa	{"type": "access_control", "severity": "medium", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}	1	t	medium	{admin}	2025-08-27 22:30:58.610437+00	2025-08-27 22:30:58.610437+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
5c36b6fd-c0ce-40e1-9681-75c2d500d588	Test Fix Policy **********	Testing after fixing JsonStructureGuide	data_privacy	opa	{"type": "data_privacy", "enabled": true, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}	1	t	high	{admin,manager}	2025-08-27 22:30:11.908103+00	2025-08-28 04:04:53.021326+00	00000000-0000-0000-0000-000000000000	00000000-0000-0000-0000-000000000000	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
6fe10905-ed59-42e8-bc52-e037c86e69fc	p6	p6	compliance	opa	{"type": "compliance", "enabled": true, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}	1	t	low	{compliance_officer}	2025-08-28 04:17:49.436762+00	2025-08-28 06:10:46.049987+00	00000000-0000-0000-0000-000000000000	00000000-0000-0000-0000-000000000000	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2	Demo Medical Privacy Policy	Test medical privacy policy for demo flow - UPDATED	Medical Privacy	medical_privacy	{"type": "medical_privacy", "enabled": true, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}	1	t	high	\N	2025-08-28 04:27:16.865765+00	2025-08-28 06:29:34.94031+00	00000000-0000-0000-0000-000000000000	00000000-0000-0000-0000-000000000000	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
9340a076-0ac3-4c43-8845-6143b2bc032a	p7	p7	data_privacy	opa	{"type": "data_privacy", "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}	1	t	low	{admin}	2025-08-28 19:37:57.463989+00	2025-08-28 19:37:57.463989+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
c3bd8586-ce3f-4d21-bdb0-f1afb644fc4a	p8	p8	test_schema_1756409008	opa	{"age": 12, "name": "test_name", "type": "test_type", "active": false, "severity": "low"}	1	t	low	{}	2025-08-28 19:59:32.192178+00	2025-08-28 19:59:32.192178+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
2ada8019-e800-4aed-a43d-69f7c461b7f0	p9	p9	test_schema_1756409008	opa	{"age": 0, "name": "test_name", "type": "test_schema_1756409008", "active": false, "severity": "low"}	1	t	low	{}	2025-08-28 20:06:24.582913+00	2025-08-28 20:06:24.582913+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
177115f8-1121-4fa0-8e81-f6db326fabf6	p10	p10	test_schema_1756409008	opa	{"age": 0, "name": "", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}	1	t	low	{}	2025-08-28 20:15:26.894453+00	2025-08-28 20:15:40.781468+00	00000000-0000-0000-0000-000000000000	00000000-0000-0000-0000-000000000000	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
f2d309ee-d92c-4f88-aadc-d714e08d286c	e2e_test_1756413012146_patient_data_protection	E2E Test Policy - Patient Data Protection (2025-08-28T20:30:12.146Z)	healthcare_compliance	e2e_test_1756413012146_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	critical	\N	2025-08-28 20:30:12.219829+00	2025-08-28 20:30:12.232005+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:30:12.232005+00	\N
ea7ec861-35f9-4897-9c04-b5b05823ca32	e2e_test_1756413202760_patient_data_protection	E2E Test Policy - Patient Data Protection (2025-08-28T20:33:22.760Z)	healthcare_compliance	e2e_test_1756413202760_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	critical	\N	2025-08-28 20:33:22.832913+00	2025-08-28 20:33:22.839088+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:33:22.839088+00	\N
d58edf2e-641b-496b-b92e-d0c297f9684a	e2e_test_1756413223928_patient_data_protection	E2E Test Policy - Patient Data Protection (2025-08-28T20:33:43.929Z)	healthcare_compliance	e2e_test_1756413223928_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	critical	\N	2025-08-28 20:33:44.010399+00	2025-08-28 20:33:44.024953+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:33:44.024953+00	\N
afc5a3cf-82e8-450b-a846-2c4529ac170b	e2e_test_1756413259601_patient_data_protection	E2E Test Policy - Patient Data Protection (2025-08-28T20:34:19.601Z)	healthcare_compliance	e2e_test_1756413259601_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	critical	\N	2025-08-28 20:34:19.678302+00	2025-08-28 20:34:19.705098+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:34:19.705098+00	\N
8d723cde-418b-4c56-94de-f6ff3edb7703	e2e_test_1756413298660_patient_data_protection	E2E Test Policy - Patient Data Protection (2025-08-28T20:34:58.661Z)	healthcare_compliance	e2e_test_1756413298660_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	critical	\N	2025-08-28 20:34:58.737495+00	2025-08-28 20:34:58.764096+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:34:58.764096+00	\N
19fe1b8d-89f4-4019-8f59-2d08382d356d	e2e_test_1756413322576_patient_data_protection	E2E Test Policy - Patient Data Protection (2025-08-28T20:35:22.577Z)	healthcare_compliance	e2e_test_1756413322576_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	critical	\N	2025-08-28 20:35:22.651548+00	2025-08-28 20:35:22.680147+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:35:22.680147+00	\N
e1414965-9a7d-4fdb-aa06-666be8dc37bf	e2e_test_1756413348147_patient_data_protection	E2E Test Policy - Patient Data Protection (2025-08-28T20:35:48.148Z)	healthcare_compliance	e2e_test_1756413348147_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	critical	\N	2025-08-28 20:35:48.218259+00	2025-08-28 20:35:48.247241+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:35:48.247241+00	\N
3ffa6959-db5b-492a-a963-944763a7c7b1	e2e_test_1756413382997_patient_data_protection	E2E Test Policy - Patient Data Protection (2025-08-28T20:36:22.997Z)	healthcare_compliance	e2e_test_1756413382997_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	critical	\N	2025-08-28 20:36:23.0652+00	2025-08-28 20:36:23.085235+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:36:23.085235+00	\N
1604904e-f5d0-4842-b59c-a11390052789	e2e_test_1756413399924_patient_data_protection	E2E Test Policy - Patient Data Protection (2025-08-28T20:36:39.928Z)	healthcare_compliance	e2e_test_1756413399924_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	critical	\N	2025-08-28 20:36:39.977802+00	2025-08-28 20:36:40.007286+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:36:40.007286+00	\N
4eccd6ca-bc28-428d-9e0d-0cdc4257fa33	e2e_test_1756413399924_group_test_policy	Policy for testing group deactivation	healthcare_compliance	e2e_test_1756413399924_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	medium	\N	2025-08-28 20:36:41.032397+00	2025-08-28 20:36:41.060083+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:36:41.060083+00	\N
80073610-ee06-49db-bd2a-531c5c39a76c	e2e_test_1756413483366_patient_data_protection	E2E Test Policy - Patient Data Protection (2025-08-28T20:38:03.366Z)	healthcare_compliance	e2e_test_1756413483366_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	critical	\N	2025-08-28 20:38:03.465342+00	2025-08-28 20:38:03.489504+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:38:03.489504+00	\N
0c1e7f1c-18a6-4df9-973d-55fe90202b0f	e2e_test_1756413483366_group_test_policy	Policy for testing group deactivation	healthcare_compliance	e2e_test_1756413483366_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	medium	\N	2025-08-28 20:38:04.515031+00	2025-08-28 20:38:05.569562+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:38:05.569562+00	\N
3c35d5ca-164b-4b75-bf0b-596fa2ee2627	e2e_test_1756413525743_patient_data_protection	E2E Test Policy - Patient Data Protection (2025-08-28T20:38:45.744Z)	healthcare_compliance	e2e_test_1756413525743_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	critical	\N	2025-08-28 20:38:45.811519+00	2025-08-28 20:38:45.835047+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:38:45.835047+00	\N
7ee9ae41-de86-4244-90f8-25c2da0623b2	e2e_test_1756413525743_group_test_policy	Policy for testing group deactivation	healthcare_compliance	e2e_test_1756413525743_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	medium	\N	2025-08-28 20:38:46.854796+00	2025-08-28 20:38:47.906441+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:38:47.906441+00	\N
6f2c6077-2364-478f-96e6-c2196d623b4e	e2e_test_1756413938766_patient_data_protection	E2E Test Policy - Patient Data Protection (2025-08-28T20:45:38.767Z)	healthcare_compliance	e2e_test_1756413938766_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	critical	\N	2025-08-28 20:45:38.827176+00	2025-08-28 20:45:38.844675+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:45:38.844675+00	\N
96c01e28-12b6-4aac-a83f-15bb4527f26a	e2e_test_1756414222865_patient_data_protection	E2E Test Policy - Patient Data Protection (2025-08-28T20:50:22.866Z)	healthcare_compliance	e2e_test_1756414222865_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	critical	\N	2025-08-28 20:50:22.925421+00	2025-08-28 20:50:22.949454+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:50:22.949454+00	\N
3e428b15-e58c-4ef9-ab58-bfe6303ecf4f	e2e_test_1756414247713_patient_data_protection	E2E Test Policy - Patient Data Protection (2025-08-28T20:50:47.713Z)	healthcare_compliance	e2e_test_1756414247713_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	critical	\N	2025-08-28 20:50:47.788242+00	2025-08-28 20:50:47.809063+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:50:47.809063+00	\N
2a052eef-c31d-44e3-92f8-5a9aec35d792	e2e_test_1756414247713_group_test_policy	Policy for testing group deactivation	healthcare_compliance	e2e_test_1756414247713_healthcare_compliance	{"patient_data": {"access_logging": true, "retention_days": 2555, "authorized_roles": ["doctor", "nurse", "admin"], "encryption_required": true}, "compliance_level": "hipaa"}	1	\N	medium	\N	2025-08-28 20:50:48.831468+00	2025-08-28 20:50:49.87847+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	2025-08-28 20:50:49.87847+00	\N
40a63e03-d616-4877-9f4b-cbf5fcad8cdc	p11	p11	test_schema_1756409008	opa	{"age": 11, "name": "", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}	1	t	low	{}	2025-08-29 08:30:16.830696+00	2025-08-29 08:30:16.830696+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
86d75236-d62b-4f41-8eb9-70047dc986bc	p12	p12	test_schema_1756409008	opa	{"age": 12, "name": "", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}	1	t	low	{}	2025-08-29 08:34:22.8302+00	2025-08-29 08:34:22.8302+00	00000000-0000-0000-0000-000000000000	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
\.


--
-- Data for Name: policy_executions; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policy_executions (execution_id, session_id, step_id, policy_id, execution_status, input_data, output_data, execution_time_ms, error_message, created_at) FROM stdin;
\.


--
-- Data for Name: policy_group_policies; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policy_group_policies (group_id, policy_id) FROM stdin;
d1b2c3d4-e5f6-7890-abcd-111111111111	f1b2c3d4-e5f6-7890-abcd-111111111111
d1b2c3d4-e5f6-7890-abcd-111111111111	f1b2c3d4-e5f6-7890-abcd-222222222222
d1b2c3d4-e5f6-7890-abcd-111111111111	f1b2c3d4-e5f6-7890-abcd-333333333333
d1b2c3d4-e5f6-7890-abcd-111111111111	bd7363f8-6104-449e-873a-db0539f0fa17
d1b2c3d4-e5f6-7890-abcd-111111111111	759c48a8-4e86-4430-8da0-73973abba594
d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	4855b086-ab50-4488-9094-58f0be3f2cf3
d1b2c3d4-e5f6-7890-abcd-111111111111	9b3d812f-bde0-43e9-a211-5d48379f1320
d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d
d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	62124064-1825-4bfa-99be-580b1a1c625d
d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	5c36b6fd-c0ce-40e1-9681-75c2d500d588
d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	9340a076-0ac3-4c43-8845-6143b2bc032a
d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	c3bd8586-ce3f-4d21-bdb0-f1afb644fc4a
d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	2ada8019-e800-4aed-a43d-69f7c461b7f0
d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	177115f8-1121-4fa0-8e81-f6db326fabf6
1bd513d1-1cf3-473d-828a-d0420fe00265	ea7ec861-35f9-4897-9c04-b5b05823ca32
0f3d98da-c217-4ed7-9adf-432da4f42cba	d58edf2e-641b-496b-b92e-d0c297f9684a
8b1c2a2c-8741-4d76-b92f-a5b806ed431a	afc5a3cf-82e8-450b-a846-2c4529ac170b
7dcc8340-2f3a-4a2d-9e85-b821940a6560	8d723cde-418b-4c56-94de-f6ff3edb7703
b82ce312-7cda-43a6-b3ef-a97b58e19e17	19fe1b8d-89f4-4019-8f59-2d08382d356d
3884f39f-c810-46e8-ad63-9353c491574a	e1414965-9a7d-4fdb-aa06-666be8dc37bf
b504593d-3a44-455d-8fe1-59656ea1753a	3ffa6959-db5b-492a-a963-944763a7c7b1
3ec7f725-252e-43a7-bcd7-82e8d7b82d9f	1604904e-f5d0-4842-b59c-a11390052789
3ec7f725-252e-43a7-bcd7-82e8d7b82d9f	4eccd6ca-bc28-428d-9e0d-0cdc4257fa33
5c51e454-aa47-4bfe-b81a-815be0cce9cb	80073610-ee06-49db-bd2a-531c5c39a76c
5c51e454-aa47-4bfe-b81a-815be0cce9cb	0c1e7f1c-18a6-4df9-973d-55fe90202b0f
05c16aef-9eb0-4452-9b46-57aeb4f6e3e5	3c35d5ca-164b-4b75-bf0b-596fa2ee2627
05c16aef-9eb0-4452-9b46-57aeb4f6e3e5	7ee9ae41-de86-4244-90f8-25c2da0623b2
33b0b43b-f336-49e0-ad22-a8ab1ca8fc70	6f2c6077-2364-478f-96e6-c2196d623b4e
dda421b1-14c6-4ede-b3fe-84b4fc2e4805	96c01e28-12b6-4aac-a83f-15bb4527f26a
22106b70-899a-4851-8f03-d858b0589f01	3e428b15-e58c-4ef9-ab58-bfe6303ecf4f
22106b70-899a-4851-8f03-d858b0589f01	2a052eef-c31d-44e3-92f8-5a9aec35d792
d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	40a63e03-d616-4877-9f4b-cbf5fcad8cdc
d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	86d75236-d62b-4f41-8eb9-70047dc986bc
\.


--
-- Data for Name: policy_groups; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policy_groups (group_id, name, description, is_template, severity, status, version, tags, created_at, updated_at, created_by, deleted_at) FROM stdin;
d1b2c3d4-e5f6-7890-abcd-111111111111	HIPAA Compliance Policy Suite	Comprehensive HIPAA privacy and security policies for healthcare operations	f	medium	active	v1.0.0	{}	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	a1b2c3d4-e5f6-7890-abcd-111111111111	\N
d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	Test Medical Group	Test policy group for medical privacy policies	f	medium	active	v1.0.0	{}	2025-08-27 22:01:09.725675+00	2025-08-27 22:01:09.725675+00	\N	\N
11a01996-cb91-40ef-88c1-8e71328ca7f4	PI Bundle	PI Bun	t	medium	active	v1.0.0	{}	2025-08-27 22:04:32.76169+00	2025-08-27 22:04:32.76169+00	\N	\N
bf6bde98-2093-4da8-b6d8-3929ef674aff	Test Policy Group Demo	Test group for demo flow verification	f	medium	active	v1.0.0	{}	2025-08-28 04:25:26.763433+00	2025-08-28 04:25:26.763433+00	\N	\N
309aef0b-8e87-4d69-b6e7-21b7c89417cb	e2e_test_1756413012146_healthcare_group	E2E Test Policy Group - Healthcare Policies (2025-08-28T20:30:12.146Z)	f	medium	deprecated	v1.0.0	{}	2025-08-28 20:30:12.211003+00	2025-08-28 20:30:12.234999+00	\N	\N
1bd513d1-1cf3-473d-828a-d0420fe00265	e2e_test_1756413202760_healthcare_group	E2E Test Policy Group - Healthcare Policies (2025-08-28T20:33:22.760Z)	f	medium	deprecated	v1.0.0	{}	2025-08-28 20:33:22.823076+00	2025-08-28 20:33:22.84266+00	\N	\N
0f3d98da-c217-4ed7-9adf-432da4f42cba	e2e_test_1756413223928_healthcare_group	E2E Test Policy Group - Healthcare Policies (2025-08-28T20:33:43.929Z)	f	medium	deprecated	v1.0.0	{}	2025-08-28 20:33:44.002493+00	2025-08-28 20:33:44.026644+00	\N	\N
8b1c2a2c-8741-4d76-b92f-a5b806ed431a	e2e_test_1756413259601_healthcare_group	E2E Test Policy Group - Healthcare Policies (2025-08-28T20:34:19.601Z)	f	medium	deprecated	v1.0.0	{}	2025-08-28 20:34:19.665733+00	2025-08-28 20:34:19.707072+00	\N	\N
7dcc8340-2f3a-4a2d-9e85-b821940a6560	e2e_test_1756413298660_healthcare_group	E2E Test Policy Group - Healthcare Policies (2025-08-28T20:34:58.661Z)	f	medium	deprecated	v1.0.0	{}	2025-08-28 20:34:58.729883+00	2025-08-28 20:34:58.765645+00	\N	\N
b82ce312-7cda-43a6-b3ef-a97b58e19e17	e2e_test_1756413322576_healthcare_group	E2E Test Policy Group - Healthcare Policies (2025-08-28T20:35:22.577Z)	f	medium	deprecated	v1.0.0	{}	2025-08-28 20:35:22.641147+00	2025-08-28 20:35:22.681601+00	\N	\N
3884f39f-c810-46e8-ad63-9353c491574a	e2e_test_1756413348147_healthcare_group	E2E Test Policy Group - Healthcare Policies (2025-08-28T20:35:48.148Z)	f	medium	deprecated	v1.0.0	{}	2025-08-28 20:35:48.21145+00	2025-08-28 20:35:49.284867+00	\N	\N
b504593d-3a44-455d-8fe1-59656ea1753a	e2e_test_1756413382997_healthcare_group	E2E Test Policy Group - Healthcare Policies (2025-08-28T20:36:22.997Z)	f	medium	deprecated	v1.0.0	{}	2025-08-28 20:36:23.057045+00	2025-08-28 20:36:24.124122+00	\N	\N
3ec7f725-252e-43a7-bcd7-82e8d7b82d9f	e2e_test_1756413399924_healthcare_group	E2E Test Policy Group - Healthcare Policies (2025-08-28T20:36:39.928Z)	f	medium	deprecated	v1.0.0	{}	2025-08-28 20:36:39.970172+00	2025-08-28 20:36:41.062527+00	\N	\N
5c51e454-aa47-4bfe-b81a-815be0cce9cb	e2e_test_1756413483366_healthcare_group	E2E Test Policy Group - Healthcare Policies (2025-08-28T20:38:03.366Z)	f	medium	deprecated	v1.0.0	{}	2025-08-28 20:38:03.457462+00	2025-08-28 20:38:05.573979+00	\N	\N
05c16aef-9eb0-4452-9b46-57aeb4f6e3e5	e2e_test_1756413525743_healthcare_group	E2E Test Policy Group - Healthcare Policies (2025-08-28T20:38:45.744Z)	f	medium	deprecated	v1.0.0	{}	2025-08-28 20:38:45.802887+00	2025-08-28 20:38:47.925028+00	\N	\N
33b0b43b-f336-49e0-ad22-a8ab1ca8fc70	e2e_test_1756413938766_healthcare_group	E2E Test Policy Group - Healthcare Policies (2025-08-28T20:45:38.767Z)	f	medium	deprecated	v1.0.0	{}	2025-08-28 20:45:38.819847+00	2025-08-28 20:45:38.846335+00	\N	\N
dda421b1-14c6-4ede-b3fe-84b4fc2e4805	e2e_test_1756414222865_healthcare_group	E2E Test Policy Group - Healthcare Policies (2025-08-28T20:50:22.866Z)	f	medium	deprecated	v1.0.0	{}	2025-08-28 20:50:22.918244+00	2025-08-28 20:50:22.95112+00	\N	\N
22106b70-899a-4851-8f03-d858b0589f01	e2e_test_1756414247713_healthcare_group	E2E Test Policy Group - Healthcare Policies (2025-08-28T20:50:47.713Z)	f	medium	deprecated	v1.0.0	{}	2025-08-28 20:50:47.771718+00	2025-08-28 20:50:49.902842+00	\N	\N
\.


--
-- Data for Name: policy_schemas; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policy_schemas (id, schema_name, schema_content, description, guardrail_id, is_active, created_at, updated_at) FROM stdin;
9a9f7702-998f-407a-8253-10f61d94000f	medical_privacy	{"type": "object", "title": "Medical Privacy Policy", "required": ["type", "severity", "allowed_roles", "protected_fields"], "properties": {"type": {"type": "string", "const": "medical_privacy", "description": "Policy type identifier"}, "enabled": {"type": "boolean", "default": true, "description": "Whether the policy is currently active"}, "severity": {"enum": ["low", "medium", "high", "critical"], "type": "string", "default": "medium", "description": "Policy severity level"}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "allowed_roles": {"type": "array", "items": {"enum": ["doctor", "nurse", "admin", "pharmacist", "lab_tech", "specialist", "resident"], "type": "string"}, "minItems": 1, "description": "Roles with access to medical data"}, "data_handling": {"type": "object", "properties": {"anonymization": {"type": "boolean", "default": false, "description": "Anonymize data for research purposes"}, "pseudonymization": {"type": "boolean", "default": true, "description": "Use pseudonyms for patient identification"}, "data_minimization": {"type": "boolean", "default": true, "description": "Collect only necessary data"}}}, "hipaa_compliance": {"type": "boolean", "default": true, "description": "Enforce HIPAA compliance requirements"}, "protected_fields": {"type": "array", "items": {"enum": ["diagnosis", "medication", "lab_orders", "medical_record_number", "treatment_plan", "billing_info", "patient_notes", "prescriptions", "vital_signs", "allergies", "family_history", "immunizations"], "type": "string"}, "minItems": 1, "description": "Medical fields requiring special protection"}, "audit_requirements": {"type": "object", "required": ["log_access", "retention_period"], "properties": {"log_access": {"type": "boolean", "default": true, "description": "Log all access to medical data"}, "access_timeout": {"type": "integer", "default": 30, "maximum": 60, "minimum": 5, "description": "Session timeout in minutes"}, "retention_period": {"type": "integer", "default": 7, "maximum": 10, "minimum": 1, "description": "Audit log retention period in years"}, "encryption_required": {"type": "boolean", "default": true, "description": "Require encryption for medical data"}}}}, "description": "Template for HIPAA-compliant medical privacy policies", "additionalProperties": false}	Template for HIPAA-compliant medical privacy policies	\N	t	2025-08-27 21:19:34.653695+00	2025-08-27 21:19:34.653695+00
f7f9e2a0-f912-4c8c-a078-6545ed80513c	data_privacy	{"type": "object", "title": "Data Privacy Policy", "required": ["type", "severity", "allowed_roles", "data_classification", "protected_fields"], "properties": {"type": {"type": "string", "const": "data_privacy", "description": "Policy type identifier"}, "enabled": {"type": "boolean", "default": true, "description": "Whether the policy is currently active"}, "severity": {"enum": ["low", "medium", "high", "critical"], "type": "string", "default": "medium", "description": "Policy severity level"}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "allowed_roles": {"type": "array", "items": {"enum": ["admin", "manager", "analyst", "user", "viewer", "editor"], "type": "string"}, "minItems": 1, "description": "Roles with access to sensitive data"}, "data_retention": {"type": "object", "required": ["retention_period"], "properties": {"archive_after": {"type": "integer", "default": 12, "maximum": 60, "minimum": 1, "description": "Archive data after this many months"}, "auto_deletion": {"type": "boolean", "default": true, "description": "Automatically delete expired data"}, "retention_period": {"type": "integer", "default": 24, "maximum": 120, "minimum": 1, "description": "Data retention period in months"}}}, "protected_fields": {"type": "array", "items": {"enum": ["personal_info", "financial_data", "contact_details", "identification", "preferences", "behavioral_data", "location_data", "biometric_data"], "type": "string"}, "minItems": 1, "description": "Data fields requiring protection"}, "data_classification": {"enum": ["public", "internal", "confidential", "restricted", "secret"], "type": "string", "default": "confidential", "description": "Classification level of the data"}, "consent_requirements": {"type": "object", "required": ["explicit_consent"], "properties": {"consent_expiry": {"type": "integer", "default": 12, "maximum": 60, "minimum": 1, "description": "Consent validity period in months"}, "explicit_consent": {"type": "boolean", "default": true, "description": "Require explicit user consent"}, "withdrawal_allowed": {"type": "boolean", "default": true, "description": "Allow users to withdraw consent"}}}}, "description": "Template for general data privacy and protection policies", "additionalProperties": false}	Template for general data privacy and protection policies	\N	t	2025-08-27 21:19:34.667795+00	2025-08-27 21:19:34.667795+00
f8870964-9455-4164-8a81-ba811fffdc98	access_control	{"type": "object", "title": "Access Control Policy", "required": ["type", "severity", "allowed_roles"], "properties": {"type": {"type": "string", "const": "access_control", "description": "Policy type identifier"}, "enabled": {"type": "boolean", "default": true, "description": "Whether the policy is currently active"}, "severity": {"enum": ["low", "medium", "high", "critical"], "type": "string", "default": "medium", "description": "Policy severity level"}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "ip_whitelist": {"type": "array", "items": {"type": "string", "pattern": "^(?:[0-9]{1,3}\\\\.){3}[0-9]{1,3}(?:/[0-9]{1,2})?$"}, "description": "Allowed IP addresses or CIDR ranges"}, "allowed_roles": {"type": "array", "items": {"enum": ["admin", "manager", "user", "viewer", "editor", "guest", "moderator"], "type": "string"}, "minItems": 1, "description": "Roles with access to the resource"}, "time_restrictions": {"type": "object", "required": ["start_time", "end_time"], "properties": {"end_time": {"type": "string", "default": "17:00", "pattern": "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", "description": "End time for access (24-hour format)"}, "timezone": {"type": "string", "default": "UTC", "description": "Timezone for time restrictions"}, "start_time": {"type": "string", "default": "09:00", "pattern": "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", "description": "Start time for access (24-hour format)"}, "allowed_days": {"type": "array", "items": {"enum": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"], "type": "string"}, "default": ["monday", "tuesday", "wednesday", "thursday", "friday"], "description": "Days when access is allowed"}}}, "restricted_actions": {"type": "array", "items": {"enum": ["create", "read", "update", "delete", "export", "import", "share", "print"], "type": "string"}, "description": "Actions that are restricted for this resource"}, "session_management": {"type": "object", "required": ["max_session_duration", "inactivity_timeout"], "properties": {"inactivity_timeout": {"type": "integer", "default": 15, "maximum": 60, "minimum": 1, "description": "Inactivity timeout in minutes"}, "concurrent_sessions": {"type": "integer", "default": 3, "maximum": 10, "minimum": 1, "description": "Maximum concurrent sessions per user"}, "max_session_duration": {"type": "integer", "default": 60, "maximum": 480, "minimum": 5, "description": "Maximum session duration in minutes"}}}}, "description": "Template for role-based access control policies", "additionalProperties": false}	Template for role-based access control policies	\N	t	2025-08-27 21:19:34.681926+00	2025-08-27 21:19:34.681926+00
328e67f3-eac3-4eb6-9f75-ae1bda8455ac	compliance	{"type": "object", "title": "Compliance Policy", "required": ["type", "severity", "allowed_roles", "regulatory_framework", "compliance_requirements"], "properties": {"type": {"type": "string", "const": "compliance", "description": "Policy type identifier"}, "enabled": {"type": "boolean", "default": true, "description": "Whether the policy is currently active"}, "severity": {"enum": ["low", "medium", "high", "critical"], "type": "string", "default": "medium", "description": "Policy severity level"}, "penalties": {"type": "object", "properties": {"monetary_fines": {"type": "boolean", "default": true, "description": "Apply monetary fines for violations"}, "max_fine_amount": {"type": "number", "minimum": 0, "description": "Maximum fine amount in currency units"}, "suspension_period": {"type": "integer", "maximum": 365, "minimum": 1, "description": "Account suspension period in days"}}}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "allowed_roles": {"type": "array", "items": {"enum": ["compliance_officer", "auditor", "admin", "manager", "supervisor"], "type": "string"}, "minItems": 1, "description": "Roles responsible for compliance"}, "audit_frequency": {"enum": ["monthly", "quarterly", "semi_annually", "annually"], "type": "string", "default": "quarterly", "description": "Frequency of compliance audits"}, "regulatory_framework": {"enum": ["gdpr", "ccpa", "sox", "hipaa", "pci_dss", "iso27001", "ferpa"], "type": "string", "description": "Regulatory framework this policy enforces"}, "reporting_requirements": {"type": "object", "required": ["incident_reporting", "reporting_timeframe"], "properties": {"incident_reporting": {"type": "boolean", "default": true, "description": "Require incident reporting"}, "reporting_timeframe": {"type": "integer", "default": 24, "maximum": 72, "minimum": 1, "description": "Hours to report incidents"}, "regulatory_notifications": {"type": "boolean", "default": true, "description": "Notify regulatory bodies of incidents"}}}, "compliance_requirements": {"type": "array", "items": {"enum": ["data_encryption", "access_logging", "audit_trails", "consent_management", "data_minimization", "right_to_forget", "breach_notification", "vendor_management"], "type": "string"}, "minItems": 1, "description": "Specific compliance requirements to enforce"}}, "description": "Template for regulatory compliance policies", "additionalProperties": false}	Template for regulatory compliance policies	\N	t	2025-08-27 21:19:34.692956+00	2025-08-27 21:19:34.692956+00
a1957c7d-fd87-497e-be86-fc0a2f43262f	test_schema_1756409008	{"type": "object", "title": "Test Schema", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["name", "severity", "enabled"], "properties": {"age": {"type": "integer", "minimum": 0, "description": "Age field"}, "name": {"type": "string", "description": "Name field"}, "type": {"type": "string", "description": "Policy type identifier"}, "active": {"type": "boolean", "description": "Active status"}, "enabled": {"type": "boolean", "default": true, "description": "Whether the policy is enabled/disabled - controls UI toggle button"}, "severity": {"enum": ["low", "medium", "high", "critical"], "type": "string", "description": "Policy severity level"}}, "description": "A test schema for validation with severity and enabled fields"}	Test schema with enabled property for UI toggle control	\N	t	2025-08-28 19:23:28.637556+00	2025-08-28 20:13:48.233765+00
85d72dfd-1e95-468d-a95d-68adcbfb3c81	e2e_test_1756413012146_healthcare_compliance	{"type": "object", "title": "Healthcare Compliance Policy", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["patient_data", "compliance_level"], "properties": {"patient_data": {"type": "object", "required": ["encryption_required", "access_logging", "retention_days", "authorized_roles"], "properties": {"access_logging": {"type": "boolean", "description": "Whether access to patient data must be logged"}, "retention_days": {"type": "integer", "maximum": 2555, "minimum": 1, "description": "Data retention period in days"}, "authorized_roles": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "List of authorized roles for access"}, "encryption_required": {"type": "boolean", "description": "Whether patient data must be encrypted"}}, "additionalProperties": false}, "compliance_level": {"enum": ["hipaa", "gdpr", "both"], "type": "string", "description": "Compliance framework requirements"}}, "additionalProperties": false}	E2E Test Schema - Healthcare Compliance (2025-08-28T20:30:12.146Z)	\N	f	2025-08-28 20:30:12.184506+00	2025-08-28 20:30:12.237862+00
8c93d24a-45a2-4045-aae5-3aed5194f937	e2e_test_1756413202760_healthcare_compliance	{"type": "object", "title": "Healthcare Compliance Policy", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["patient_data", "compliance_level"], "properties": {"patient_data": {"type": "object", "required": ["encryption_required", "access_logging", "retention_days", "authorized_roles"], "properties": {"access_logging": {"type": "boolean", "description": "Whether access to patient data must be logged"}, "retention_days": {"type": "integer", "maximum": 2555, "minimum": 1, "description": "Data retention period in days"}, "authorized_roles": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "List of authorized roles for access"}, "encryption_required": {"type": "boolean", "description": "Whether patient data must be encrypted"}}, "additionalProperties": false}, "compliance_level": {"enum": ["hipaa", "gdpr", "both"], "type": "string", "description": "Compliance framework requirements"}}, "additionalProperties": false}	E2E Test Schema - Healthcare Compliance (2025-08-28T20:33:22.760Z)	\N	f	2025-08-28 20:33:22.794655+00	2025-08-28 20:33:22.845085+00
70b98a1d-c4fa-4e9a-81af-c72ea70d6f99	e2e_test_1756413223928_healthcare_compliance	{"type": "object", "title": "Healthcare Compliance Policy", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["patient_data", "compliance_level"], "properties": {"patient_data": {"type": "object", "required": ["encryption_required", "access_logging", "retention_days", "authorized_roles"], "properties": {"access_logging": {"type": "boolean", "description": "Whether access to patient data must be logged"}, "retention_days": {"type": "integer", "maximum": 2555, "minimum": 1, "description": "Data retention period in days"}, "authorized_roles": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "List of authorized roles for access"}, "encryption_required": {"type": "boolean", "description": "Whether patient data must be encrypted"}}, "additionalProperties": false}, "compliance_level": {"enum": ["hipaa", "gdpr", "both"], "type": "string", "description": "Compliance framework requirements"}}, "additionalProperties": false}	E2E Test Schema - Healthcare Compliance (2025-08-28T20:33:43.928Z)	\N	f	2025-08-28 20:33:43.959768+00	2025-08-28 20:33:44.028222+00
53dcd318-18e3-4266-ac2a-5b413eb93965	e2e_test_1756413259601_healthcare_compliance	{"type": "object", "title": "Healthcare Compliance Policy", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["patient_data", "compliance_level"], "properties": {"patient_data": {"type": "object", "required": ["encryption_required", "access_logging", "retention_days", "authorized_roles"], "properties": {"access_logging": {"type": "boolean", "description": "Whether access to patient data must be logged"}, "retention_days": {"type": "integer", "maximum": 2555, "minimum": 1, "description": "Data retention period in days"}, "authorized_roles": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "List of authorized roles for access"}, "encryption_required": {"type": "boolean", "description": "Whether patient data must be encrypted"}}, "additionalProperties": false}, "compliance_level": {"enum": ["hipaa", "gdpr", "both"], "type": "string", "description": "Compliance framework requirements"}}, "additionalProperties": false}	E2E Test Schema - Healthcare Compliance (2025-08-28T20:34:19.601Z)	\N	f	2025-08-28 20:34:19.643268+00	2025-08-28 20:34:19.708922+00
4432deb3-4e27-4135-9612-28a4434358a9	e2e_test_1756413298660_healthcare_compliance	{"type": "object", "title": "Healthcare Compliance Policy", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["patient_data", "compliance_level"], "properties": {"patient_data": {"type": "object", "required": ["encryption_required", "access_logging", "retention_days", "authorized_roles"], "properties": {"access_logging": {"type": "boolean", "description": "Whether access to patient data must be logged"}, "retention_days": {"type": "integer", "maximum": 2555, "minimum": 1, "description": "Data retention period in days"}, "authorized_roles": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "List of authorized roles for access"}, "encryption_required": {"type": "boolean", "description": "Whether patient data must be encrypted"}}, "additionalProperties": false}, "compliance_level": {"enum": ["hipaa", "gdpr", "both"], "type": "string", "description": "Compliance framework requirements"}}, "additionalProperties": false}	E2E Test Schema - Healthcare Compliance (2025-08-28T20:34:58.660Z)	\N	f	2025-08-28 20:34:58.70454+00	2025-08-28 20:34:58.767065+00
32d0c771-53ad-4ef3-9d1c-e0334b90641d	e2e_test_1756413322576_healthcare_compliance	{"type": "object", "title": "Healthcare Compliance Policy", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["patient_data", "compliance_level"], "properties": {"patient_data": {"type": "object", "required": ["encryption_required", "access_logging", "retention_days", "authorized_roles"], "properties": {"access_logging": {"type": "boolean", "description": "Whether access to patient data must be logged"}, "retention_days": {"type": "integer", "maximum": 2555, "minimum": 1, "description": "Data retention period in days"}, "authorized_roles": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "List of authorized roles for access"}, "encryption_required": {"type": "boolean", "description": "Whether patient data must be encrypted"}}, "additionalProperties": false}, "compliance_level": {"enum": ["hipaa", "gdpr", "both"], "type": "string", "description": "Compliance framework requirements"}}, "additionalProperties": false}	E2E Test Schema - Healthcare Compliance (2025-08-28T20:35:22.576Z)	\N	f	2025-08-28 20:35:22.621567+00	2025-08-28 20:35:22.68309+00
44ccaa50-a280-4630-8319-53d8be253367	e2e_test_1756413348147_healthcare_compliance	{"type": "object", "title": "Healthcare Compliance Policy", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["patient_data", "compliance_level"], "properties": {"patient_data": {"type": "object", "required": ["encryption_required", "access_logging", "retention_days", "authorized_roles"], "properties": {"access_logging": {"type": "boolean", "description": "Whether access to patient data must be logged"}, "retention_days": {"type": "integer", "maximum": 2555, "minimum": 1, "description": "Data retention period in days"}, "authorized_roles": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "List of authorized roles for access"}, "encryption_required": {"type": "boolean", "description": "Whether patient data must be encrypted"}}, "additionalProperties": false}, "compliance_level": {"enum": ["hipaa", "gdpr", "both"], "type": "string", "description": "Compliance framework requirements"}}, "additionalProperties": false}	E2E Test Schema - Healthcare Compliance (2025-08-28T20:35:48.147Z)	\N	f	2025-08-28 20:35:48.180093+00	2025-08-28 20:35:49.290006+00
ccba865f-27a0-4bf5-bb4f-4e44d752920a	e2e_test_1756413382997_healthcare_compliance	{"type": "object", "title": "Healthcare Compliance Policy", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["patient_data", "compliance_level"], "properties": {"patient_data": {"type": "object", "required": ["encryption_required", "access_logging", "retention_days", "authorized_roles"], "properties": {"access_logging": {"type": "boolean", "description": "Whether access to patient data must be logged"}, "retention_days": {"type": "integer", "maximum": 2555, "minimum": 1, "description": "Data retention period in days"}, "authorized_roles": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "List of authorized roles for access"}, "encryption_required": {"type": "boolean", "description": "Whether patient data must be encrypted"}}, "additionalProperties": false}, "compliance_level": {"enum": ["hipaa", "gdpr", "both"], "type": "string", "description": "Compliance framework requirements"}}, "additionalProperties": false}	E2E Test Schema - Healthcare Compliance (2025-08-28T20:36:22.997Z)	\N	f	2025-08-28 20:36:23.032523+00	2025-08-28 20:36:24.128293+00
b32ad7bd-2876-41e0-a94f-674e7ac10eab	e2e_test_1756413399924_healthcare_compliance	{"type": "object", "title": "Healthcare Compliance Policy", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["patient_data", "compliance_level"], "properties": {"patient_data": {"type": "object", "required": ["encryption_required", "access_logging", "retention_days", "authorized_roles"], "properties": {"access_logging": {"type": "boolean", "description": "Whether access to patient data must be logged"}, "retention_days": {"type": "integer", "maximum": 2555, "minimum": 1, "description": "Data retention period in days"}, "authorized_roles": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "List of authorized roles for access"}, "encryption_required": {"type": "boolean", "description": "Whether patient data must be encrypted"}}, "additionalProperties": false}, "compliance_level": {"enum": ["hipaa", "gdpr", "both"], "type": "string", "description": "Compliance framework requirements"}}, "additionalProperties": false}	E2E Test Schema - Healthcare Compliance (2025-08-28T20:36:39.924Z)	\N	f	2025-08-28 20:36:39.943448+00	2025-08-28 20:36:41.064688+00
225b459f-22f8-4f7c-8c83-f9ec12bb4c09	e2e_test_1756413483366_healthcare_compliance	{"type": "object", "title": "Healthcare Compliance Policy", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["patient_data", "compliance_level"], "properties": {"patient_data": {"type": "object", "required": ["encryption_required", "access_logging", "retention_days", "authorized_roles"], "properties": {"access_logging": {"type": "boolean", "description": "Whether access to patient data must be logged"}, "retention_days": {"type": "integer", "maximum": 2555, "minimum": 1, "description": "Data retention period in days"}, "authorized_roles": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "List of authorized roles for access"}, "encryption_required": {"type": "boolean", "description": "Whether patient data must be encrypted"}}, "additionalProperties": false}, "compliance_level": {"enum": ["hipaa", "gdpr", "both"], "type": "string", "description": "Compliance framework requirements"}}, "additionalProperties": false}	E2E Test Schema - Healthcare Compliance (2025-08-28T20:38:03.366Z)	\N	f	2025-08-28 20:38:03.4259+00	2025-08-28 20:38:05.578986+00
7f49998f-dea2-4b8e-8236-514263badbd1	e2e_test_1756413525743_invalid_schema	{"invalid": "not a valid json schema"}	Invalid schema test	\N	f	2025-08-28 20:38:47.917598+00	2025-08-29 05:28:28.398009+00
d4e1dc92-ad9e-4966-b2c3-92cf111ac844	e2e_test_1756413525743_healthcare_compliance	{"type": "object", "title": "Healthcare Compliance Policy", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["patient_data", "compliance_level"], "properties": {"patient_data": {"type": "object", "required": ["encryption_required", "access_logging", "retention_days", "authorized_roles"], "properties": {"access_logging": {"type": "boolean", "description": "Whether access to patient data must be logged"}, "retention_days": {"type": "integer", "maximum": 2555, "minimum": 1, "description": "Data retention period in days"}, "authorized_roles": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "List of authorized roles for access"}, "encryption_required": {"type": "boolean", "description": "Whether patient data must be encrypted"}}, "additionalProperties": false}, "compliance_level": {"enum": ["hipaa", "gdpr", "both"], "type": "string", "description": "Compliance framework requirements"}}, "additionalProperties": false}	Duplicate schema test	\N	f	2025-08-28 20:38:45.779468+00	2025-08-28 20:38:47.927215+00
59777f98-1c72-4030-a616-67833b7bb776	e2e_test_1756413938766_healthcare_compliance	{"type": "object", "title": "Healthcare Compliance Policy", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["patient_data", "compliance_level"], "properties": {"patient_data": {"type": "object", "required": ["encryption_required", "access_logging", "retention_days", "authorized_roles"], "properties": {"access_logging": {"type": "boolean", "description": "Whether access to patient data must be logged"}, "retention_days": {"type": "integer", "maximum": 2555, "minimum": 1, "description": "Data retention period in days"}, "authorized_roles": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "List of authorized roles for access"}, "encryption_required": {"type": "boolean", "description": "Whether patient data must be encrypted"}}, "additionalProperties": false}, "compliance_level": {"enum": ["hipaa", "gdpr", "both"], "type": "string", "description": "Compliance framework requirements"}}, "additionalProperties": false}	E2E Test Schema - Healthcare Compliance (2025-08-28T20:45:38.766Z)	\N	f	2025-08-28 20:45:38.799048+00	2025-08-28 20:45:38.847711+00
4071f6da-bc79-4a37-a1a2-f51ef7272830	e2e_test_1756414222865_healthcare_compliance	{"type": "object", "title": "Healthcare Compliance Policy", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["patient_data", "compliance_level"], "properties": {"patient_data": {"type": "object", "required": ["encryption_required", "access_logging", "retention_days", "authorized_roles"], "properties": {"access_logging": {"type": "boolean", "description": "Whether access to patient data must be logged"}, "retention_days": {"type": "integer", "maximum": 2555, "minimum": 1, "description": "Data retention period in days"}, "authorized_roles": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "List of authorized roles for access"}, "encryption_required": {"type": "boolean", "description": "Whether patient data must be encrypted"}}, "additionalProperties": false}, "compliance_level": {"enum": ["hipaa", "gdpr", "both"], "type": "string", "description": "Compliance framework requirements"}}, "additionalProperties": false}	E2E Test Schema - Healthcare Compliance (2025-08-28T20:50:22.865Z)	\N	f	2025-08-28 20:50:22.898883+00	2025-08-28 20:50:22.952916+00
3f21ad19-586c-49fa-a42c-c618c4d591e5	e2e_test_1756414247713_invalid_schema	{"invalid": "not a valid json schema"}	Invalid schema test	\N	t	2025-08-28 20:50:49.895165+00	2025-08-28 20:50:49.895165+00
e02f59ef-c2e1-4467-bcdc-eef8a22963e6	e2e_test_1756414247713_healthcare_compliance	{"type": "object", "title": "Healthcare Compliance Policy", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["patient_data", "compliance_level"], "properties": {"patient_data": {"type": "object", "required": ["encryption_required", "access_logging", "retention_days", "authorized_roles"], "properties": {"access_logging": {"type": "boolean", "description": "Whether access to patient data must be logged"}, "retention_days": {"type": "integer", "maximum": 2555, "minimum": 1, "description": "Data retention period in days"}, "authorized_roles": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "List of authorized roles for access"}, "encryption_required": {"type": "boolean", "description": "Whether patient data must be encrypted"}}, "additionalProperties": false}, "compliance_level": {"enum": ["hipaa", "gdpr", "both"], "type": "string", "description": "Compliance framework requirements"}}, "additionalProperties": false}	Duplicate schema test	\N	f	2025-08-28 20:50:47.736418+00	2025-08-28 20:50:49.904523+00
\.


--
-- Data for Name: policy_templates; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policy_templates (template_id, name, description, category, template_definition, is_system_template, created_at, created_by, guardrail_id) FROM stdin;
15ad737f-41fe-4eaf-966c-3115d5322821	PII Masking Template	Template for masking personally identifiable information	data_masking	{"type": "data_masking", "severity": "critical", "mask_format": "XXX-XX-{last4}", "field_patterns": ["ssn", "social_security_number", "phone", "email"], "regex_patterns": ["\\\\\\\\b\\\\\\\\d{3}-\\\\\\\\d{2}-\\\\\\\\d{4}\\\\\\\\b", "\\\\\\\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\\\\\\\.[A-Z|a-z]{2,}\\\\\\\\b"]}	t	2025-08-27 17:57:54.326132+00	\N	\N
9e14d93e-4b8a-4b95-8187-bc8a96e26a4c	Phone Number Masking Template	Template for masking phone number fields	data_masking	{"type": "data_masking", "severity": "high", "mask_format": "XXX-XX-{last4}", "field_patterns": ["phone", "phone_number"], "regex_patterns": ["\\\\\\\\b\\\\\\\\d{3}-\\\\\\\\d{2}-\\\\\\\\d{4}\\\\\\\\b"]}	t	2025-08-27 17:57:54.326132+00	\N	\N
4e0dd1de-7a23-4fa7-860c-a4e2db5f12c9	Content Safety Template	Template for detecting harmful or inappropriate content	content_safety	{"type": "content_filtering", "action": "block_and_log", "severity": "high", "blocked_categories": ["hate_speech", "violence", "self_harm", "sexual"], "confidence_threshold": 0.7}	t	2025-08-27 17:57:54.326132+00	\N	\N
f5c4f2d9-6baa-4499-a20d-d7993a9ffe85	Medical Info Protection Template	Template for protecting sensitive medical information	medical_privacy	{"type": "medical_privacy", "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}	t	2025-08-27 17:57:54.326132+00	\N	\N
a3d39f57-2e2c-44de-9461-8043534ef287	Data Encryption Template	Template for encrypting sensitive data fields	data_encryption	{"type": "data_encryption", "severity": "critical", "fields_to_encrypt": ["credit_card", "bank_account", "ssn"], "key_rotation_days": 90, "encryption_algorithm": "AES-256"}	t	2025-08-27 17:57:54.326132+00	\N	\N
83736ee5-e550-4ea6-958b-ff9083fe0359	Access Logging Template	Template for comprehensive access logging	access_logging	{"type": "access_logging", "severity": "high", "log_level": "detailed", "log_fields": ["user_id", "timestamp", "resource", "action", "ip_address"], "retention_days": 2555, "hipaa_compliant": true}	t	2025-08-27 17:57:54.326132+00	\N	\N
01048e08-5df5-4a19-b3ce-20eaade78ddd	Data Retention Template	Template for data retention policies	data_retention	{"type": "data_retention", "severity": "critical", "data_types": ["patient_records", "medical_history", "billing_info"], "auto_deletion": true, "backup_retention_days": 365, "retention_period_days": 2555}	t	2025-08-27 17:57:54.326132+00	\N	\N
aaff4aed-e41d-4832-8b9e-2c45f631c902	Access Control Template	Template for role-based access control	access_control	{"type": "access_control", "severity": "high", "ip_whitelist": ["***********/24"], "allowed_roles": ["admin", "manager"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00"}, "restricted_actions": ["delete", "export"]}	t	2025-08-27 17:57:54.326132+00	\N	\N
9326875a-2a8d-4a7f-84cc-86995c8f4802	Compliance Template	Template for regulatory compliance policies	compliance	{"type": "compliance", "severity": "critical", "regulations": ["HIPAA", "GDPR", "SOX"], "penalty_severity": "high", "audit_requirements": true, "documentation_required": true}	t	2025-08-27 17:57:54.326132+00	\N	\N
e1b2c3d4-e5f6-7890-abcd-111111111111	HIPAA PHI Access Control Template	Template for controlling access to Protected Health Information	HIPAA Privacy Compliance	{"parameters": {"audit_redaction": {"type": "boolean", "default": true}, "redaction_rules": {"type": "object", "default": {"phone": "({area_code}) ***-****"}}, "redaction_fields": {"type": "array", "default": ["phone"]}, "emergency_override": {"type": "boolean", "default": true}, "redaction_required": {"type": "boolean", "default": true}, "external_sharing_allowed": {"type": "boolean", "default": false}, "sharing_authorized_roles": {"type": "array", "default": ["HIPAA_CLINICAL_REVIEWER", "HIPAA_MEDICAL_DIRECTOR"]}}}	f	2025-08-27 17:57:54.32967+00	a1b2c3d4-e5f6-7890-abcd-111111111111	\N
e1b2c3d4-e5f6-7890-abcd-222222222222	HIPAA Medical Record Sharing Template	Template for sharing medical records with PHI redaction capabilities	HIPAA Privacy Compliance	{"parameters": {"visibility_rules": {"type": "object", "default": {"HIPAA_CASE_MANAGER": ["contact_info", "insurance_info", "email"], "HIPAA_MEDICAL_DIRECTOR": ["all_fields"], "HIPAA_CLINICAL_REVIEWER": ["medical_info", "basic_demographics", "email"], "HIPAA_COMPLIANCE_OFFICER": ["access_logs", "audit_trails", "email"]}}, "minimum_data_only": {"type": "boolean", "default": true}, "redaction_methods": {"type": "object", "default": {"email": "****@****.com"}}, "purpose_limitation": {"type": "boolean", "default": true}, "pii_fields_to_redact": {"type": "array", "default": ["email"]}, "role_based_visibility": {"type": "boolean", "default": true}, "auto_redaction_enabled": {"type": "boolean", "default": true}}}	f	2025-08-27 17:57:54.32967+00	a1b2c3d4-e5f6-7890-abcd-111111111111	\N
e1b2c3d4-e5f6-7890-abcd-333333333333	HIPAA Patient Consent Management Template	Template for managing patient consent and authorization	HIPAA Privacy Compliance	{"parameters": {"visibility_rules": {"type": "object", "default": {"HIPAA_CASE_MANAGER": ["contact_info", "insurance_info", "email", "phone", "address", "dob", "insurance_id"], "HIPAA_MEDICAL_DIRECTOR": ["all_fields"], "HIPAA_CLINICAL_REVIEWER": ["medical_info", "basic_demographics", "email", "phone", "address", "dob", "insurance_id"], "HIPAA_COMPLIANCE_OFFICER": ["access_logs", "audit_trails", "email", "phone", "address", "dob", "insurance_id"]}}, "minimum_data_only": {"type": "boolean", "default": true}, "redaction_methods": {"type": "object", "default": {"dob": "{month}/{day}/****", "ssn": "***-**-{last_4}", "email": "****@****.com", "phone": "({area_code}) ***-****", "address": "{city}, {state} {zip}", "insurance_id": "{first_3}*****"}}, "purpose_limitation": {"type": "boolean", "default": true}, "pii_fields_to_redact": {"type": "array", "default": ["ssn", "address", "phone", "email", "dob", "insurance_id"]}, "role_based_visibility": {"type": "boolean", "default": true}, "auto_redaction_enabled": {"type": "boolean", "default": true}}}	f	2025-08-27 17:57:54.32967+00	a1b2c3d4-e5f6-7890-abcd-111111111111	\N
\.


--
-- Data for Name: policy_violations; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policy_violations (violation_id, policy_id, user_id, violation_type, details, severity, resolved, created_at, resolved_at, resolved_by) FROM stdin;
\.


--
-- Data for Name: rego_templates; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.rego_templates (template_id, name, description, policy_category, template_content, variables, is_active, created_at, updated_at, created_by) FROM stdin;
\.


--
-- Data for Name: roles; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.roles (role_id, code, name, description, is_system_role, created_at, updated_at, created_by, permissions) FROM stdin;
b1b2c3d4-e5f6-7890-abcd-111111111111	HIPAA_COMPLIANCE_OFFICER	HIPAA Privacy and Compliance Officer	Responsible for HIPAA compliance monitoring and privacy oversight	f	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	{}
b1b2c3d4-e5f6-7890-abcd-222222222222	HIPAA_CLINICAL_REVIEWER	Clinical Staff with PHI Access	Healthcare staff authorized to review patient medical information	f	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	{}
b1b2c3d4-e5f6-7890-abcd-333333333333	HIPAA_MEDICAL_DIRECTOR	Senior Physician with Full Access	Senior medical staff with unrestricted access to patient data	f	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	{}
b1b2c3d4-e5f6-7890-abcd-444444444444	HIPAA_CASE_MANAGER	Patient Care Coordinator	Staff responsible for coordinating patient care across services	f	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	{}
b1b2c3d4-e5f6-7890-abcd-555555555555	HIPAA_ADMIN	System Administrator	Technical staff with system administration privileges	f	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	{}
\.


--
-- Data for Name: system_metrics; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.system_metrics (metric_id, metric_type, metric_name, metric_value, dimensions, created_at) FROM stdin;
\.


--
-- Data for Name: test_results; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.test_results (id, experiment_id, dataset_entry_id, test_case_type, input, expected_output, actual_output, context, retrieval_context, tools_called, expected_outcome, scenario, status, score, latency_ms, token_count, metadata, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: user_roles; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.user_roles (user_id, role_id) FROM stdin;
a1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-555555555555
a1b2c3d4-e5f6-7890-abcd-222222222222	b1b2c3d4-e5f6-7890-abcd-333333333333
a1b2c3d4-e5f6-7890-abcd-222222222222	b1b2c3d4-e5f6-7890-abcd-222222222222
a1b2c3d4-e5f6-7890-abcd-333333333333	b1b2c3d4-e5f6-7890-abcd-111111111111
************************************	b1b2c3d4-e5f6-7890-abcd-222222222222
a1b2c3d4-e5f6-7890-abcd-555555555555	b1b2c3d4-e5f6-7890-abcd-444444444444
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.users (user_id, azure_ad_id, email, first_name, last_name, role, is_active, created_at, updated_at, created_by, updated_by, department, status, risk_score, last_login, two_factor_enabled) FROM stdin;
00000000-0000-0000-0000-000000000000	system	<EMAIL>	System	User	system	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00	\N	\N	\N	active	0.00	\N	f
a1b2c3d4-e5f6-7890-abcd-111111111111	azure-admin-placeholder-001	<EMAIL>	HIPAA	Administrator	admin	t	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	\N	Information Technology	active	0.00	\N	t
a1b2c3d4-e5f6-7890-abcd-222222222222	azure-sarah-martinez-002	<EMAIL>	Sarah	Martinez	user	t	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	\N	Medical Affairs	active	1.50	\N	t
a1b2c3d4-e5f6-7890-abcd-333333333333	azure-jennifer-chen-003	<EMAIL>	Jennifer	Chen	user	t	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	\N	Privacy & Compliance	active	0.50	\N	t
************************************	azure-michael-rodriguez-004	<EMAIL>	Michael	Rodriguez	user	t	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	\N	Clinical Review	active	1.00	\N	f
a1b2c3d4-e5f6-7890-abcd-555555555555	azure-lisa-thompson-005	<EMAIL>	Lisa	Thompson	user	t	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	\N	Care Management	active	0.80	\N	t
\.


--
-- Name: enum_categories_category_id_seq; Type: SEQUENCE SET; Schema: public; Owner: dbadmin
--

SELECT pg_catalog.setval('public.enum_categories_category_id_seq', 7, true);


--
-- Name: enum_values_value_id_seq; Type: SEQUENCE SET; Schema: public; Owner: dbadmin
--

SELECT pg_catalog.setval('public.enum_values_value_id_seq', 42, true);


--
-- Name: evaluations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: dbadmin
--

SELECT pg_catalog.setval('public.evaluations_id_seq', 1, false);


--
-- Name: agent_access agent_access_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_access
    ADD CONSTRAINT agent_access_pkey PRIMARY KEY (agent_id, role_id);


--
-- Name: agent_access agent_access_unique; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_access
    ADD CONSTRAINT agent_access_unique UNIQUE (agent_id, role_id);


--
-- Name: agent_policies agent_policies_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_policies
    ADD CONSTRAINT agent_policies_pkey PRIMARY KEY (agent_id, policy_id);


--
-- Name: agent_policies agent_policies_unique; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_policies
    ADD CONSTRAINT agent_policies_unique UNIQUE (agent_id, policy_id, link_type);


--
-- Name: agent_role_policies agent_role_policies_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_role_policies
    ADD CONSTRAINT agent_role_policies_pkey PRIMARY KEY (agent_id, role_id, group_id, policy_id);


--
-- Name: agents agents_name_unique; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agents
    ADD CONSTRAINT agents_name_unique UNIQUE (name);


--
-- Name: agents agents_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agents
    ADD CONSTRAINT agents_pkey PRIMARY KEY (agent_id);


--
-- Name: alembic_version alembic_version_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.alembic_version
    ADD CONSTRAINT alembic_version_pkey PRIMARY KEY (version_num);


--
-- Name: audit_log audit_log_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.audit_log
    ADD CONSTRAINT audit_log_pkey PRIMARY KEY (log_id);


--
-- Name: chat_messages chat_messages_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.chat_messages
    ADD CONSTRAINT chat_messages_pkey PRIMARY KEY (message_id);


--
-- Name: dataset_entries dataset_entries_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dataset_entries
    ADD CONSTRAINT dataset_entries_pkey PRIMARY KEY (id);


--
-- Name: datasets datasets_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.datasets
    ADD CONSTRAINT datasets_pkey PRIMARY KEY (id);


--
-- Name: documents documents_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_pkey PRIMARY KEY (document_id);


--
-- Name: enum_categories enum_categories_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.enum_categories
    ADD CONSTRAINT enum_categories_pkey PRIMARY KEY (category_id);


--
-- Name: enum_categories enum_categories_policy_type_field_path_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.enum_categories
    ADD CONSTRAINT enum_categories_policy_type_field_path_key UNIQUE (policy_type, field_path);


--
-- Name: enum_values enum_values_category_id_value_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.enum_values
    ADD CONSTRAINT enum_values_category_id_value_key UNIQUE (category_id, value);


--
-- Name: enum_values enum_values_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.enum_values
    ADD CONSTRAINT enum_values_pkey PRIMARY KEY (value_id);


--
-- Name: evaluation_metrics evaluation_metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.evaluation_metrics
    ADD CONSTRAINT evaluation_metrics_pkey PRIMARY KEY (id);


--
-- Name: evaluations evaluations_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.evaluations
    ADD CONSTRAINT evaluations_pkey PRIMARY KEY (id);


--
-- Name: experiment_evaluations experiment_evaluations_experiment_id_metric_id_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.experiment_evaluations
    ADD CONSTRAINT experiment_evaluations_experiment_id_metric_id_key UNIQUE (experiment_id, metric_id);


--
-- Name: experiment_evaluations experiment_evaluations_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.experiment_evaluations
    ADD CONSTRAINT experiment_evaluations_pkey PRIMARY KEY (id);


--
-- Name: experiments experiments_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.experiments
    ADD CONSTRAINT experiments_pkey PRIMARY KEY (id);


--
-- Name: guardrail_services guardrail_services_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.guardrail_services
    ADD CONSTRAINT guardrail_services_pkey PRIMARY KEY (id);


--
-- Name: guardrail_services guardrail_services_service_id_version_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.guardrail_services
    ADD CONSTRAINT guardrail_services_service_id_version_key UNIQUE (service_id, version);


--
-- Name: integration_dlq integration_dlq_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.integration_dlq
    ADD CONSTRAINT integration_dlq_pkey PRIMARY KEY (id);


--
-- Name: integration_outbox integration_outbox_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.integration_outbox
    ADD CONSTRAINT integration_outbox_pkey PRIMARY KEY (id);


--
-- Name: mcp_chat_sessions mcp_chat_sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.mcp_chat_sessions
    ADD CONSTRAINT mcp_chat_sessions_pkey PRIMARY KEY (session_id);


--
-- Name: mcp_flow_steps mcp_flow_steps_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.mcp_flow_steps
    ADD CONSTRAINT mcp_flow_steps_pkey PRIMARY KEY (step_id);


--
-- Name: openai_api_calls openai_api_calls_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.openai_api_calls
    ADD CONSTRAINT openai_api_calls_pkey PRIMARY KEY (call_id);


--
-- Name: policies policies_name_version_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_name_version_key UNIQUE (name, version);


--
-- Name: policies policies_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_pkey PRIMARY KEY (policy_id);


--
-- Name: policy_executions policy_executions_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_executions
    ADD CONSTRAINT policy_executions_pkey PRIMARY KEY (execution_id);


--
-- Name: policy_group_policies policy_group_policies_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_group_policies
    ADD CONSTRAINT policy_group_policies_pkey PRIMARY KEY (group_id, policy_id);


--
-- Name: policy_group_policies policy_group_policies_unique; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_group_policies
    ADD CONSTRAINT policy_group_policies_unique UNIQUE (group_id, policy_id);


--
-- Name: policy_groups policy_groups_name_unique; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_groups
    ADD CONSTRAINT policy_groups_name_unique UNIQUE (name);


--
-- Name: policy_groups policy_groups_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_groups
    ADD CONSTRAINT policy_groups_pkey PRIMARY KEY (group_id);


--
-- Name: policy_schemas policy_schemas_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_schemas
    ADD CONSTRAINT policy_schemas_pkey PRIMARY KEY (id);


--
-- Name: policy_schemas policy_schemas_schema_name_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_schemas
    ADD CONSTRAINT policy_schemas_schema_name_key UNIQUE (schema_name);


--
-- Name: policy_templates policy_templates_name_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_templates
    ADD CONSTRAINT policy_templates_name_key UNIQUE (name);


--
-- Name: policy_templates policy_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_templates
    ADD CONSTRAINT policy_templates_pkey PRIMARY KEY (template_id);


--
-- Name: policy_violations policy_violations_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_violations
    ADD CONSTRAINT policy_violations_pkey PRIMARY KEY (violation_id);


--
-- Name: rego_templates rego_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.rego_templates
    ADD CONSTRAINT rego_templates_pkey PRIMARY KEY (template_id);


--
-- Name: roles roles_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_code_key UNIQUE (code);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (role_id);


--
-- Name: system_metrics system_metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.system_metrics
    ADD CONSTRAINT system_metrics_pkey PRIMARY KEY (metric_id);


--
-- Name: test_results test_results_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.test_results
    ADD CONSTRAINT test_results_pkey PRIMARY KEY (id);


--
-- Name: user_roles user_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_pkey PRIMARY KEY (user_id, role_id);


--
-- Name: user_roles user_roles_user_role_unique; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_user_role_unique UNIQUE (user_id, role_id);


--
-- Name: users users_azure_ad_id_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_azure_ad_id_key UNIQUE (azure_ad_id);


--
-- Name: users users_email_unique; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_unique UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (user_id);


--
-- Name: idx_agent_access_role; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_agent_access_role ON public.agent_access USING btree (role_id);


--
-- Name: idx_agent_policies_agent; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_agent_policies_agent ON public.agent_policies USING btree (agent_id);


--
-- Name: idx_agent_policies_policy; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_agent_policies_policy ON public.agent_policies USING btree (policy_id);


--
-- Name: idx_agents_active; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_agents_active ON public.agents USING btree (is_active);


--
-- Name: idx_agents_type; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_agents_type ON public.agents USING btree (agent_type);


--
-- Name: idx_arp_agent; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_arp_agent ON public.agent_role_policies USING btree (agent_id);


--
-- Name: idx_arp_agent_role; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_arp_agent_role ON public.agent_role_policies USING btree (agent_id, role_id);


--
-- Name: idx_arp_group; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_arp_group ON public.agent_role_policies USING btree (group_id);


--
-- Name: idx_arp_policy; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_arp_policy ON public.agent_role_policies USING btree (policy_id);


--
-- Name: idx_audit_action; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_action ON public.audit_log USING btree (action);


--
-- Name: idx_audit_log_request_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_log_request_id ON public.audit_log USING btree (request_id);


--
-- Name: idx_audit_log_session_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_log_session_id ON public.audit_log USING btree (session_id);


--
-- Name: idx_audit_log_user_role; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_log_user_role ON public.audit_log USING btree (user_role);


--
-- Name: idx_audit_resource; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_resource ON public.audit_log USING btree (resource_type, resource_id);


--
-- Name: idx_audit_timestamp; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_timestamp ON public.audit_log USING btree ("timestamp");


--
-- Name: idx_audit_user_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_user_id ON public.audit_log USING btree (user_id);


--
-- Name: idx_chat_messages_session; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_chat_messages_session ON public.chat_messages USING btree (session_id);


--
-- Name: idx_dataset_entries_dataset; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_dataset_entries_dataset ON public.dataset_entries USING btree (dataset_id);


--
-- Name: idx_datasets_created_at; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_datasets_created_at ON public.datasets USING btree (created_at DESC);


--
-- Name: idx_datasets_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_datasets_status ON public.datasets USING btree (status);


--
-- Name: idx_documents_sensitive; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_documents_sensitive ON public.documents USING btree (is_sensitive);


--
-- Name: idx_documents_type; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_documents_type ON public.documents USING btree (document_type);


--
-- Name: idx_evaluations_experiment; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_evaluations_experiment ON public.evaluations USING btree (experiment_id);


--
-- Name: idx_experiment_evaluations_experiment; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_experiment_evaluations_experiment ON public.experiment_evaluations USING btree (experiment_id);


--
-- Name: idx_experiments_dataset; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_experiments_dataset ON public.experiments USING btree (dataset_id);


--
-- Name: idx_experiments_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_experiments_status ON public.experiments USING btree (status);


--
-- Name: idx_integration_outbox_created_at; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_integration_outbox_created_at ON public.integration_outbox USING btree (created_at);


--
-- Name: idx_integration_outbox_next_attempt; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_integration_outbox_next_attempt ON public.integration_outbox USING btree (next_attempt_at);


--
-- Name: idx_integration_outbox_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_integration_outbox_status ON public.integration_outbox USING btree (status);


--
-- Name: idx_integration_outbox_status_next; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_integration_outbox_status_next ON public.integration_outbox USING btree (status, next_attempt_at);


--
-- Name: idx_mcp_chat_sessions_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_mcp_chat_sessions_user ON public.mcp_chat_sessions USING btree (user_id);


--
-- Name: idx_mcp_flow_steps_session; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_mcp_flow_steps_session ON public.mcp_flow_steps USING btree (session_id);


--
-- Name: idx_mcp_flow_steps_session_step; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE UNIQUE INDEX idx_mcp_flow_steps_session_step ON public.mcp_flow_steps USING btree (session_id, step_number);


--
-- Name: idx_openai_api_calls_session; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_openai_api_calls_session ON public.openai_api_calls USING btree (session_id);


--
-- Name: idx_pg_policies_policy; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_pg_policies_policy ON public.policy_group_policies USING btree (policy_id);


--
-- Name: idx_policies_active; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_active ON public.policies USING btree (is_active);


--
-- Name: idx_policies_blob_path; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_blob_path ON public.policies USING btree (blob_path);


--
-- Name: idx_policies_category; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_category ON public.policies USING btree (category);


--
-- Name: idx_policies_last_generation; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_last_generation ON public.policies USING btree (last_rego_generation);


--
-- Name: idx_policies_original_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_original_id ON public.policies USING btree (original_policy_id);


--
-- Name: idx_policies_rego_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_rego_status ON public.policies USING btree (opa_sync_status);


--
-- Name: idx_policies_roles; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_roles ON public.policies USING gin (applies_to_roles);


--
-- Name: idx_policies_severity; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_severity ON public.policies USING btree (severity);


--
-- Name: idx_policy_executions_policy; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policy_executions_policy ON public.policy_executions USING btree (policy_id);


--
-- Name: idx_policy_executions_session; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policy_executions_session ON public.policy_executions USING btree (session_id);


--
-- Name: idx_policy_groups_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policy_groups_status ON public.policy_groups USING btree (status);


--
-- Name: idx_policy_schemas_active; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policy_schemas_active ON public.policy_schemas USING btree (schema_name) WHERE (is_active = true);


--
-- Name: idx_policy_schemas_guardrail; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policy_schemas_guardrail ON public.policy_schemas USING btree (guardrail_id) WHERE (guardrail_id IS NOT NULL);


--
-- Name: idx_system_metrics_type_name; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_system_metrics_type_name ON public.system_metrics USING btree (metric_type, metric_name);


--
-- Name: idx_test_results_experiment; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_test_results_experiment ON public.test_results USING btree (experiment_id);


--
-- Name: idx_test_results_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_test_results_status ON public.test_results USING btree (status);


--
-- Name: idx_users_azure_ad_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_users_azure_ad_id ON public.users USING btree (azure_ad_id);


--
-- Name: idx_users_email; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_users_email ON public.users USING btree (email);


--
-- Name: idx_users_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_users_status ON public.users USING btree (status);


--
-- Name: idx_violations_policy_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_violations_policy_id ON public.policy_violations USING btree (policy_id);


--
-- Name: idx_violations_resolved; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_violations_resolved ON public.policy_violations USING btree (resolved);


--
-- Name: idx_violations_user_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_violations_user_id ON public.policy_violations USING btree (user_id);


--
-- Name: agents trg_integration_agents; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER trg_integration_agents AFTER INSERT OR DELETE OR UPDATE ON public.agents FOR EACH ROW EXECUTE FUNCTION public.trg_agents_enqueue();


--
-- Name: agent_role_policies trg_integration_arp; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER trg_integration_arp AFTER INSERT OR DELETE ON public.agent_role_policies FOR EACH ROW EXECUTE FUNCTION public.trg_arp_enqueue();


--
-- Name: integration_outbox trg_outbox_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER trg_outbox_updated_at BEFORE UPDATE ON public.integration_outbox FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: policies trg_policies_integration; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER trg_policies_integration AFTER INSERT OR DELETE OR UPDATE ON public.policies FOR EACH ROW EXECUTE FUNCTION public.trg_policies_enqueue();


--
-- Name: roles trg_roles_integration; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER trg_roles_integration AFTER INSERT OR DELETE OR UPDATE ON public.roles FOR EACH ROW EXECUTE FUNCTION public.trg_roles_enqueue();


--
-- Name: documents update_documents_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON public.documents FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: enum_categories update_enum_categories_updated_at_trigger; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_enum_categories_updated_at_trigger BEFORE UPDATE ON public.enum_categories FOR EACH ROW EXECUTE FUNCTION public.update_enum_categories_updated_at();


--
-- Name: enum_values update_enum_values_updated_at_trigger; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_enum_values_updated_at_trigger BEFORE UPDATE ON public.enum_values FOR EACH ROW EXECUTE FUNCTION public.update_enum_values_updated_at();


--
-- Name: mcp_chat_sessions update_mcp_chat_sessions_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_mcp_chat_sessions_updated_at BEFORE UPDATE ON public.mcp_chat_sessions FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: policies update_policies_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_policies_updated_at BEFORE UPDATE ON public.policies FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: policy_groups update_policy_groups_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_policy_groups_updated_at BEFORE UPDATE ON public.policy_groups FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: policy_schemas update_policy_schemas_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_policy_schemas_updated_at BEFORE UPDATE ON public.policy_schemas FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: rego_templates update_rego_templates_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_rego_templates_updated_at BEFORE UPDATE ON public.rego_templates FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: roles update_roles_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON public.roles FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: users update_users_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: agent_access agent_access_agent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_access
    ADD CONSTRAINT agent_access_agent_id_fkey FOREIGN KEY (agent_id) REFERENCES public.agents(agent_id) ON DELETE CASCADE;


--
-- Name: agent_access agent_access_granted_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_access
    ADD CONSTRAINT agent_access_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES public.users(user_id);


--
-- Name: agent_access agent_access_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_access
    ADD CONSTRAINT agent_access_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(role_id) ON DELETE CASCADE;


--
-- Name: agent_policies agent_policies_agent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_policies
    ADD CONSTRAINT agent_policies_agent_id_fkey FOREIGN KEY (agent_id) REFERENCES public.agents(agent_id) ON DELETE CASCADE;


--
-- Name: agent_policies agent_policies_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_policies
    ADD CONSTRAINT agent_policies_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id) ON DELETE CASCADE;


--
-- Name: agent_role_policies agent_role_policies_agent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_role_policies
    ADD CONSTRAINT agent_role_policies_agent_id_fkey FOREIGN KEY (agent_id) REFERENCES public.agents(agent_id) ON DELETE CASCADE;


--
-- Name: agent_role_policies agent_role_policies_group_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_role_policies
    ADD CONSTRAINT agent_role_policies_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.policy_groups(group_id) ON DELETE CASCADE;


--
-- Name: agent_role_policies agent_role_policies_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_role_policies
    ADD CONSTRAINT agent_role_policies_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id) ON DELETE CASCADE;


--
-- Name: agent_role_policies agent_role_policies_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_role_policies
    ADD CONSTRAINT agent_role_policies_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(role_id) ON DELETE CASCADE;


--
-- Name: agents agents_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agents
    ADD CONSTRAINT agents_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: audit_log audit_log_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.audit_log
    ADD CONSTRAINT audit_log_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id);


--
-- Name: chat_messages chat_messages_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.chat_messages
    ADD CONSTRAINT chat_messages_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.mcp_chat_sessions(session_id) ON DELETE CASCADE;


--
-- Name: dataset_entries dataset_entries_dataset_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dataset_entries
    ADD CONSTRAINT dataset_entries_dataset_id_fkey FOREIGN KEY (dataset_id) REFERENCES public.datasets(id) ON DELETE CASCADE;


--
-- Name: documents documents_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: documents documents_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: enum_values enum_values_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.enum_values
    ADD CONSTRAINT enum_values_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.enum_categories(category_id) ON DELETE CASCADE;


--
-- Name: evaluations evaluations_experiment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.evaluations
    ADD CONSTRAINT evaluations_experiment_id_fkey FOREIGN KEY (experiment_id) REFERENCES public.experiments(id) ON DELETE CASCADE;


--
-- Name: experiment_evaluations experiment_evaluations_experiment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.experiment_evaluations
    ADD CONSTRAINT experiment_evaluations_experiment_id_fkey FOREIGN KEY (experiment_id) REFERENCES public.experiments(id) ON DELETE CASCADE;


--
-- Name: experiment_evaluations experiment_evaluations_metric_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.experiment_evaluations
    ADD CONSTRAINT experiment_evaluations_metric_id_fkey FOREIGN KEY (metric_id) REFERENCES public.evaluation_metrics(id);


--
-- Name: experiments experiments_dataset_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.experiments
    ADD CONSTRAINT experiments_dataset_id_fkey FOREIGN KEY (dataset_id) REFERENCES public.datasets(id) ON DELETE CASCADE;


--
-- Name: agent_role_policies fk_group_policy_pair; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_role_policies
    ADD CONSTRAINT fk_group_policy_pair FOREIGN KEY (group_id, policy_id) REFERENCES public.policy_group_policies(group_id, policy_id) ON DELETE CASCADE;


--
-- Name: mcp_chat_sessions mcp_chat_sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.mcp_chat_sessions
    ADD CONSTRAINT mcp_chat_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id);


--
-- Name: mcp_flow_steps mcp_flow_steps_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.mcp_flow_steps
    ADD CONSTRAINT mcp_flow_steps_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.mcp_chat_sessions(session_id) ON DELETE CASCADE;


--
-- Name: openai_api_calls openai_api_calls_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.openai_api_calls
    ADD CONSTRAINT openai_api_calls_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.mcp_chat_sessions(session_id) ON DELETE CASCADE;


--
-- Name: openai_api_calls openai_api_calls_step_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.openai_api_calls
    ADD CONSTRAINT openai_api_calls_step_id_fkey FOREIGN KEY (step_id) REFERENCES public.mcp_flow_steps(step_id) ON DELETE CASCADE;


--
-- Name: policies policies_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: policies policies_original_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_original_policy_id_fkey FOREIGN KEY (original_policy_id) REFERENCES public.policies(policy_id);


--
-- Name: policies policies_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: policy_executions policy_executions_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_executions
    ADD CONSTRAINT policy_executions_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id);


--
-- Name: policy_executions policy_executions_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_executions
    ADD CONSTRAINT policy_executions_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.mcp_chat_sessions(session_id) ON DELETE CASCADE;


--
-- Name: policy_executions policy_executions_step_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_executions
    ADD CONSTRAINT policy_executions_step_id_fkey FOREIGN KEY (step_id) REFERENCES public.mcp_flow_steps(step_id) ON DELETE CASCADE;


--
-- Name: policy_group_policies policy_group_policies_group_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_group_policies
    ADD CONSTRAINT policy_group_policies_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.policy_groups(group_id) ON DELETE CASCADE;


--
-- Name: policy_group_policies policy_group_policies_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_group_policies
    ADD CONSTRAINT policy_group_policies_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id) ON DELETE CASCADE;


--
-- Name: policy_groups policy_groups_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_groups
    ADD CONSTRAINT policy_groups_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: policy_templates policy_templates_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_templates
    ADD CONSTRAINT policy_templates_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: policy_violations policy_violations_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_violations
    ADD CONSTRAINT policy_violations_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id);


--
-- Name: policy_violations policy_violations_resolved_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_violations
    ADD CONSTRAINT policy_violations_resolved_by_fkey FOREIGN KEY (resolved_by) REFERENCES public.users(user_id);


--
-- Name: policy_violations policy_violations_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_violations
    ADD CONSTRAINT policy_violations_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id);


--
-- Name: rego_templates rego_templates_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.rego_templates
    ADD CONSTRAINT rego_templates_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: roles roles_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: test_results test_results_dataset_entry_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.test_results
    ADD CONSTRAINT test_results_dataset_entry_id_fkey FOREIGN KEY (dataset_entry_id) REFERENCES public.dataset_entries(id) ON DELETE CASCADE;


--
-- Name: test_results test_results_experiment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.test_results
    ADD CONSTRAINT test_results_experiment_id_fkey FOREIGN KEY (experiment_id) REFERENCES public.experiments(id) ON DELETE CASCADE;


--
-- Name: user_roles user_roles_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(role_id) ON DELETE CASCADE;


--
-- Name: user_roles user_roles_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE;


--
-- Name: users users_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: users users_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- PostgreSQL database dump complete
--

\unrestrict ThIkomKOgptyb5DuadqdZbXCuctDeOhtTakvDEaQfBt1WPOTN6FovlbLpUWVZxm

