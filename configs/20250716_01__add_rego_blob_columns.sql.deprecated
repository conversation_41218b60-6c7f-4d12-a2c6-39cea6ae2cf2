-- Migration: Add Rego and Blob Storage columns to policies table
-- Date: 2025-07-16

ALTER TABLE policies 
    ADD COLUMN rego_code TEXT,
    ADD COLUMN blob_container VARCHAR(255) DEFAULT 'rego-policies',
    ADD COLUMN blob_path VARCHAR(500),
    ADD COLUMN blob_url VARCHAR(1000),
    ADD COLUMN rego_template_id VARCHAR(100),
    ADD COLUMN opa_sync_status VARCHAR(50) DEFAULT 'pending',
    ADD COLUMN last_rego_generation TIMESTAMP WITH TIME ZONE,
    ADD COLUMN rego_generation_error TEXT,
    ADD COLUMN rego_version INTEGER DEFAULT 1;

CREATE INDEX idx_policies_rego_status ON policies(opa_sync_status);
CREATE INDEX idx_policies_last_generation ON policies(last_rego_generation);
CREATE INDEX idx_policies_blob_path ON policies(blob_path);
