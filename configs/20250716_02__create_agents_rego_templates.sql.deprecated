-- Migration: Create agents and rego_templates tables for OPA enhancement
-- Date: 2025-07-16

CREATE TABLE agents (
    agent_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    agent_type VARCHAR(100) DEFAULT 'policy_engine',
    endpoint_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    configuration JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id)
);

CREATE INDEX idx_agents_active ON agents(is_active);
CREATE INDEX idx_agents_type ON agents(agent_type);

CREATE TABLE rego_templates (
    template_id VARCHAR(100) PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    policy_category VARCHAR(100) NOT NULL,
    template_content TEXT NOT NULL,
    variables JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id)
);

CREATE TRIGGER update_rego_templates_updated_at BEFORE UPDATE ON rego_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
