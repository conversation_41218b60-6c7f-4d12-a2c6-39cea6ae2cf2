-- Migration: Insert default agent and rego templates for initial setup
-- Date: 2025-07-16


INSERT INTO agents (name, description, agent_type, is_active, created_by)
VALUES (
    'default-agent',
    'Default policy agent for initial implementation',
    'policy_engine',
    true,
    '00000000-0000-0000-0000-000000000000'
);

-- Insert default rego templates
INSERT INTO rego_templates (template_id, name, description, policy_category, template_content, variables, created_by)
VALUES 
(
    'data_masking_v1',
    'Data Masking Template v1',
    'Template for data masking policies',
    'data_masking',
    E'-- Data Masking Rego Template --\npackage vitea.policies.{{policy_id}}.data_masking\n\nimport rego.v1\n\n# Policy: {{policy_name}}\n# Description: {{description}}\n# Generated: {{generated_at}}\n\nfield_patterns := {{field_patterns}}\nregex_patterns := {{regex_patterns}}\nmask_format := "{{mask_format}}"\napplies_to_roles := {{applies_to_roles}}\n\nmask_required if {\n    input.user.role in applies_to_roles\n    input.field_name in field_patterns\n}\n\nmasked_value := result if {\n    mask_required\n    some pattern in regex_patterns\n    regex.match(pattern, input.value)\n    result := apply_mask_format(input.value, mask_format)\n}\n\napply_mask_format(value, format) := result if {\n    format == "XXX-XX-{last4}"\n    result := format("XXX-XX-%s", substring(value, count(value) - 4, 4))\n}',
    '["policy_id", "policy_name", "description", "generated_at", "field_patterns", "regex_patterns", "mask_format", "applies_to_roles"]',
    '00000000-0000-0000-0000-000000000000'
),
(
    'access_control_v1',
    'Access Control Template v1',
    'Template for access control policies',
    'access_control',
    E'-- Access Control Rego Template --\npackage vitea.policies.{{policy_id}}.access_control\n\nimport rego.v1\n\n# Policy: {{policy_name}}\n# Description: {{description}}\n# Generated: {{generated_at}}\n\nrestricted_fields := {{restricted_fields}}\nallowed_roles := {{allowed_roles}}\nseverity := "{{severity}}"\n\ndeny contains violation if {\n    input.field_name in restricted_fields\n    not input.user.role in allowed_roles\n    violation := {\n        "message": format("Access denied to field ''%s'' for role ''%s''", input.field_name, input.user.role),\n        "user_role": input.user.role\n    }\n}\n\nallow if {\n    input.field_name in restricted_fields\n    input.user.role in allowed_roles\n}',
    '["policy_id", "policy_name", "description", "generated_at", "restricted_fields", "allowed_roles", "severity"]',
    '00000000-0000-0000-0000-000000000000'
);
