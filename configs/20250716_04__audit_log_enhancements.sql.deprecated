-- Migration: Audit log enhancements for schema changes and Rego operations
-- Date: 2025-07-16

-- Insert audit event for schema update
INSERT INTO audit_log (user_id, action, resource_type, resource_id, new_values)
VALUES (
    '00000000-0000-0000-0000-000000000000',
    'schema_update',
    'database',
    null,
    '{"update": "Added Rego and Blob Storage columns to policies table, created agents and rego_templates tables"}'
);

-- Add function for logging Rego operations
CREATE OR REPLACE FUNCTION log_rego_operation(
    user_uuid UUID,
    operation_details JSONB
) RETURNS VOID AS $$
BEGIN
    INSERT INTO audit_log (user_id, action, resource_type, resource_id, new_values, timestamp)
    VALUES (user_uuid, 'policy_rego', 'policy_rego', operation_details->>'policy_id', operation_details, CURRENT_TIMESTAMP);
END;
$$ LANGUAGE plpgsql;
