-- Migration: Database functions for Rego generation and rollback
-- Date: 2025-07-16

-- Function to generate Rego code for a policy
CREATE OR REPLACE FUNCTION generate_rego_for_policy(policy_uuid UUID)
RETURNS JSON AS $$
DECLARE
    policy_record policies%ROWTYPE;
    template_record rego_templates%ROWTYPE;
    generated_rego TEXT;
    blob_path_var VARCHAR(500);
    result JSON;
BEGIN
    -- Get policy details
    SELECT * INTO policy_record FROM policies WHERE policy_id = policy_uuid;
    IF NOT FOUND THEN
        RETURN json_build_object('success', false, 'error', 'Policy not found');
    END IF;
    -- Get template based on policy category
    SELECT * INTO template_record FROM rego_templates WHERE policy_category = policy_record.category AND is_active = true LIMIT 1;
    IF NOT FOUND THEN
        RETURN json_build_object('success', false, 'error', 'Template not found');
    END IF;
    -- Increment version before generating Rego
    UPDATE policies SET version = version + 1 WHERE policy_id = policy_uuid RETURNING * INTO policy_record;
    -- Simulate template rendering (actual implementation should use server-side code)
    generated_rego := template_record.template_content;
    blob_path_var := CONCAT('active/policy_', policy_record.policy_id, '_', policy_record.category, '_v', policy_record.version, '.rego');
    -- Update policy with generated Rego
    UPDATE policies SET rego_code = generated_rego, blob_path = blob_path_var, rego_template_id = template_record.template_id, last_rego_generation = CURRENT_TIMESTAMP, opa_sync_status = 'generated', rego_version = policy_record.version WHERE policy_id = policy_uuid;
    -- Log operation
    --PERFORM log_rego_operation('00000000-0000-0000-0000-000000000000', json_build_object('policy_id', policy_uuid, 'action', 'generate_rego', 'status', 'success'));
    RETURN json_build_object('success', true, 'rego_preview', generated_rego, 'blob_path', blob_path_var);
END;
$$ LANGUAGE plpgsql;

-- Function to rollback Rego generation for a policy
CREATE OR REPLACE FUNCTION rollback_rego_generation(policy_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE policies SET rego_code = NULL, blob_path = NULL, rego_template_id = NULL, last_rego_generation = NULL, opa_sync_status = 'pending', rego_generation_error = NULL WHERE policy_id = policy_uuid;
    PERFORM log_rego_operation('00000000-0000-0000-0000-000000000000', json_build_object('policy_id', policy_uuid, 'action', 'rollback_rego', 'status', 'success'));
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;
