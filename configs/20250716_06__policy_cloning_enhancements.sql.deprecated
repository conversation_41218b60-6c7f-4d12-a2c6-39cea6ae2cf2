-- Policy Cloning and Enhanced Audit Logging Migration
-- This migration adds support for policy cloning and enhanced HIPAA-compliant audit logging

-- Add original_policy_id column to policies table for cloning support
ALTER TABLE policies ADD COLUMN IF NOT EXISTS original_policy_id UUID REFERENCES policies(policy_id);
ALTER TABLE policies ADD COLUMN IF NOT EXISTS cloned_from_policy_name VARCHAR(255);

-- Add index for efficient cloning queries
CREATE INDEX IF NOT EXISTS idx_policies_original_id ON policies(original_policy_id);

-- Enhance audit_log table for HIPAA compliance
ALTER TABLE audit_log ADD COLUMN IF NOT EXISTS session_id VARCHAR(255);
ALTER TABLE audit_log ADD COLUMN IF NOT EXISTS request_id VARCHAR(255);
ALTER TABLE audit_log ADD COLUMN IF NOT EXISTS user_role VARCHAR(50);
ALTER TABLE audit_log ADD COLUMN IF NOT EXISTS resource_name <PERSON><PERSON>HA<PERSON>(255);
ALTER TABLE audit_log ADD COLUMN IF NOT EXISTS access_level VARCHAR(50);
ALTER TABLE audit_log ADD COLUMN IF NOT EXISTS data_classification VARCHAR(50);

-- Add indexes for HIPAA audit queries
CREATE INDEX IF NOT EXISTS idx_audit_log_session_id ON audit_log(session_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_request_id ON audit_log(request_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_user_role ON audit_log(user_role);
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp_range ON audit_log(timestamp);

-- Create enhanced audit logging function
CREATE OR REPLACE FUNCTION log_hipaa_audit_event(
    p_user_id UUID,
    p_action VARCHAR(100),
    p_resource_type VARCHAR(100),
    p_resource_id UUID,
    p_old_values JSONB,
    p_new_values JSONB,
    p_session_id VARCHAR(255),
    p_request_id VARCHAR(255),
    p_user_role VARCHAR(50),
    p_resource_name VARCHAR(255),
    p_access_level VARCHAR(50),
    p_data_classification VARCHAR(50)
) RETURNS VOID AS $$
BEGIN
    INSERT INTO audit_log (
        user_id, action, resource_type, resource_id, old_values, new_values,
        ip_address, user_agent, timestamp, session_id, request_id, user_role,
        resource_name, access_level, data_classification
    ) VALUES (
        p_user_id, p_action, p_resource_type, p_resource_id, p_old_values, p_new_values,
        inet_client_addr(), current_setting('application_name', true), CURRENT_TIMESTAMP,
        p_session_id, p_request_id, p_user_role, p_resource_name, p_access_level, p_data_classification
    );
END;
$$ LANGUAGE plpgsql;

-- Create function to get policy templates by category
CREATE OR REPLACE FUNCTION get_policy_template_by_category(p_category VARCHAR(100))
RETURNS TABLE(template_id UUID, name VARCHAR(255), description TEXT, template_definition JSONB) AS $$
BEGIN
    RETURN QUERY
    SELECT pt.template_id, pt.name, pt.description, pt.template_definition
    FROM policy_templates pt
    WHERE pt.category = p_category
    AND pt.is_system_template = true
    ORDER BY pt.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Create function to clone a policy
CREATE OR REPLACE FUNCTION clone_policy(
    p_original_policy_id UUID,
    p_new_name VARCHAR(255),
    p_new_description TEXT,
    p_cloned_by_user_id UUID
) RETURNS UUID AS $$
DECLARE
    v_new_policy_id UUID;
    v_original_policy RECORD;
BEGIN
    -- Get original policy data
    SELECT * INTO v_original_policy
    FROM policies
    WHERE policy_id = p_original_policy_id
    AND deleted_at IS NULL;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Original policy not found or deleted';
    END IF;
    
    -- Create new policy as clone
    INSERT INTO policies (
        name, description, category, policy_type, definition, version,
        is_active, severity, applies_to_roles, created_by, updated_by,
        original_policy_id, cloned_from_policy_name
    ) VALUES (
        p_new_name, p_new_description, v_original_policy.category,
        v_original_policy.policy_type, v_original_policy.definition, 1,
        false, v_original_policy.severity, v_original_policy.applies_to_roles,
        p_cloned_by_user_id, p_cloned_by_user_id,
        p_original_policy_id, v_original_policy.name
    ) RETURNING policy_id INTO v_new_policy_id;
    
    -- Log the cloning event
    PERFORM log_hipaa_audit_event(
        p_cloned_by_user_id,
        'POLICY_CLONED',
        'policy',
        v_new_policy_id,
        jsonb_build_object('original_policy_id', p_original_policy_id, 'original_policy_name', v_original_policy.name),
        jsonb_build_object('new_policy_name', p_new_name, 'new_policy_id', v_new_policy_id),
        NULL, NULL, 'admin', 'policy_cloning', 'write', 'sensitive'
    );
    
    RETURN v_new_policy_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to search policies with pagination
CREATE OR REPLACE FUNCTION search_policies(
    p_search_term VARCHAR(255),
    p_category VARCHAR(100),
    p_severity VARCHAR(20),
    p_is_active BOOLEAN,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
) RETURNS TABLE(
    policy_id UUID,
    name VARCHAR(255),
    description TEXT,
    category VARCHAR(100),
    severity VARCHAR(20),
    is_active BOOLEAN,
    policy_type VARCHAR(50),
    definition JSONB,
    applies_to_roles TEXT[],
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    total_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    WITH filtered_policies AS (
        SELECT p.*,
               COUNT(*) OVER() as total_count
        FROM policies p
        WHERE p.deleted_at IS NULL
        AND (p_search_term IS NULL OR 
             p.name ILIKE '%' || p_search_term || '%' OR 
             p.description ILIKE '%' || p_search_term || '%')
        AND (p_category IS NULL OR p.category = p_category)
        AND (p_severity IS NULL OR p.severity = p_severity)
        AND (p_is_active IS NULL OR p.is_active = p_is_active)
    )
    SELECT fp.policy_id, fp.name, fp.description, fp.category, fp.severity,
           fp.is_active, fp.policy_type, fp.definition, fp.applies_to_roles,
           fp.created_at, fp.updated_at, fp.total_count
    FROM filtered_policies fp
    ORDER BY fp.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Policy templates are now managed in a separate file: policy_templates.sql
-- This ensures all templates are consolidated and easier to maintain

-- Update existing policies to ensure they have proper audit trail
UPDATE policies 
SET updated_at = CURRENT_TIMESTAMP 
WHERE updated_at IS NULL; 