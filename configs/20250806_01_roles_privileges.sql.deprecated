-- Migration 20250806_01_roles_privileges.sql
-- Purpose : Introduce role catalog and map users to roles (many-to-many).
--           Adds generic ENUMs that will be reused by other migrations.
--           Idempotent: Safe to run multiple times.
-- -----------------------------------------------------------------------------

BEGIN;

-- 1. ENUM types ---------------------------------------------------------------

-- Access level for Agent ↔ Role ACL
DO $$ BEGIN
    CREATE TYPE access_level_enum AS ENUM ('view', 'manage');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Generic severity levels (used later by policy_groups)
DO $$ BEGIN
    CREATE TYPE severity_level_enum AS ENUM ('low', 'medium', 'high', 'critical');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Lifecycle status (active / deprecated / draft)
DO $$ BEGIN
    CREATE TYPE lifecycle_status_enum AS ENUM ('draft', 'active', 'deprecated');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 2. Roles catalog ------------------------------------------------------------
CREATE TABLE IF NOT EXISTS roles (
    role_id     UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code        VARCHAR(100) UNIQUE NOT NULL,   -- e.g. ADMIN, CISO
    name        VARCHAR(255),
    description TEXT,
    created_at  TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 3. User ↔ Role mapping ------------------------------------------------------
CREATE TABLE IF NOT EXISTS user_roles (
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(role_id) ON DELETE CASCADE,
    PRIMARY KEY (user_id, role_id)
);

COMMIT;
