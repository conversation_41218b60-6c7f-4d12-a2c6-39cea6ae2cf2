-- Migration 20250806_02_policy_groups.sql
-- Purpose : Create policy_groups table and linking table policy_group_policies.
-- -----------------------------------------------------------------------------

BEGIN;

-- 1. Table : policy_groups -----------------------------------------------------
CREATE TABLE IF NOT EXISTS policy_groups (
    group_id    UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name        VARCHA<PERSON>(255) NOT NULL UNIQUE,
    description TEXT,
    is_template BOOLEAN      DEFAULT false,
    severity    severity_level_enum DEFAULT 'medium',
    status      lifecycle_status_enum DEFAULT 'active',
    version     VARCHAR(20) DEFAULT 'v1.0.0',
    tags        TEXT[]  DEFAULT '{}',
    created_at  TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at  TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by  UUID REFERENCES users(user_id)
);

-- Trigger to keep updated_at current
DO $$ BEGIN
    CREATE TRIGGER update_policy_groups_updated_at
        BEFORE UPDATE ON policy_groups
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 2. Linking table : policy_group_policies ------------------------------------
CREATE TABLE IF NOT EXISTS policy_group_policies (
    group_id  UUID REFERENCES policy_groups(group_id) ON DELETE CASCADE,
    policy_id UUID REFERENCES policies(policy_id)     ON DELETE CASCADE,
    PRIMARY KEY (group_id, policy_id)
);

CREATE INDEX IF NOT EXISTS idx_pg_policies_policy ON policy_group_policies(policy_id);

COMMIT;
