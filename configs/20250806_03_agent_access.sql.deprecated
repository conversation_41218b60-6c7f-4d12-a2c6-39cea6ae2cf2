-- Migration 20250806_03_agent_access.sql
-- Purpose : Create agent_access (Role ↔ Agent ACL) and agent_policies (Agent ↔ Policy links)
-- -----------------------------------------------------------------------------

BEGIN;

-- 1. Additional ENUM -----------------------------------------------------------
DO $$ BEGIN
    CREATE TYPE link_type_enum AS ENUM ('direct', 'via_group');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 2. ACL table : agent_access --------------------------------------------------
CREATE TABLE IF NOT EXISTS agent_access (
    agent_id     UUID REFERENCES agents(agent_id) ON DELETE CASCADE,
    role_id      UUID REFERENCES roles(role_id)  ON DELETE CASCADE,
    access_level access_level_enum DEFAULT 'view',
    PRIMARY KEY (agent_id, role_id)
);
CREATE INDEX IF NOT EXISTS idx_agent_access_role ON agent_access(role_id);

-- 3. Link table : agent_policies ---------------------------------------------
CREATE TABLE IF NOT EXISTS agent_policies (
    agent_id  UUID REFERENCES agents(agent_id)   ON DELETE CASCADE,
    policy_id UUID REFERENCES policies(policy_id) ON DELETE CASCADE,
    link_type link_type_enum NOT NULL,
    PRIMARY KEY (agent_id, policy_id, link_type)
);

-- Helpful partial index for precedence rule (direct wins)
CREATE INDEX IF NOT EXISTS idx_agent_policies_direct ON agent_policies(agent_id, policy_id)
    WHERE link_type = 'direct';

COMMIT;
