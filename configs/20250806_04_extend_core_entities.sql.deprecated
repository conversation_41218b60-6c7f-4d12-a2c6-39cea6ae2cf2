-- Migration 20250806_04_extend_core_entities.sql
-- Purpose : Add additional attributes to existing tables (users, agents).
-- -----------------------------------------------------------------------------

BEGIN;

-- 1. ENUMs for status values ---------------------------------------------------
DO $$ BEGIN
    CREATE TYPE user_status_enum AS ENUM ('active', 'suspended', 'pending');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE agent_status_enum AS ENUM ('active', 'pending', 'maintenance', 'deprecated');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 2. Alter users --------------------------------------------------------------
ALTER TABLE users
    ADD COLUMN IF NOT EXISTS department VARCHAR(255),
    ADD COLUMN IF NOT EXISTS status     user_status_enum DEFAULT 'active',
    ADD COLUMN IF NOT EXISTS risk_score NUMERIC(4,2) DEFAULT 0.0,
    ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE,
    ADD COLUMN IF NOT EXISTS two_factor_enabled BOOLEAN DEFAULT false;

-- 3. Alter agents -------------------------------------------------------------
ALTER TABLE agents
    ADD COLUMN IF NOT EXISTS vendor      VARCHAR(255),
    ADD COLUMN IF NOT EXISTS department  VARCHAR(255),
    ADD COLUMN IF NOT EXISTS risk_score  NUMERIC(4,2) DEFAULT 0.0,
    ADD COLUMN IF NOT EXISTS status      agent_status_enum DEFAULT 'active';

COMMIT;
