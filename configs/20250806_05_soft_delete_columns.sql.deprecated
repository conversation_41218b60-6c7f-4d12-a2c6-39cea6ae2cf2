-- Migration 20250806_05_soft_delete_columns.sql
-- Purpose : Introduce soft-delete capability for policy_groups and agents.
--           Adds deleted_at timestamp column; no data mutation performed.
-- ----------------------------------------------------------------------------

BEGIN;

ALTER TABLE IF NOT EXISTS policy_groups
    ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

ALTER TABLE IF NOT EXISTS agents
    ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

COMMIT;
