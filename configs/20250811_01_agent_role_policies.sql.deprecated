-- Migration: Create agent_role_policies for role-scoped assignments
-- Date: 2025-08-11

BEGIN;

-- Table to map an agent's role to a specific policy via a policy group
CREATE TABLE IF NOT EXISTS agent_role_policies (
  agent_id  UUID NOT NULL REFERENCES agents(agent_id) ON DELETE CASCADE,
  role_id   UUID NOT NULL REFERENCES roles(role_id)  ON DELETE CASCADE,
  group_id  UUID NOT NULL REFERENCES policy_groups(group_id) ON DELETE CASCADE,
  policy_id UUID NOT NULL REFERENCES policies(policy_id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (agent_id, role_id, group_id, policy_id)
);

-- Ensure the (group_id, policy_id) pair is valid according to policy_group_policies
ALTER TABLE agent_role_policies
  ADD CONSTRAINT fk_group_policy_pair
  FOREIGN KEY (group_id, policy_id)
  REFERENCES policy_group_policies (group_id, policy_id)
  ON DELETE CASCADE;

COMMIT;

