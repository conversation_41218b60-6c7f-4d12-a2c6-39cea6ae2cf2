BEGIN;

-- Uniqueness is already enforced by the PK; include only if you want a named UNIQUE as well.
-- ALTER TABLE agent_role_policies
--   ADD CONSTRAINT uq_arp_unique
--   UNIQUE (agent_id, role_id, group_id, policy_id);

CREATE INDEX IF NOT EXISTS idx_arp_agent
  ON agent_role_policies(agent_id);

CREATE INDEX IF NOT EXISTS idx_arp_agent_role
  ON agent_role_policies(agent_id, role_id);

CREATE INDEX IF NOT EXISTS idx_arp_group
  ON agent_role_policies(group_id);

CREATE INDEX IF NOT EXISTS idx_arp_policy
  ON agent_role_policies(policy_id);

COMMIT;