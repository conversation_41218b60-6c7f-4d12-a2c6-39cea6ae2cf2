-- External Integrations v1: Outbox, DLQ, Triggers, and supporting columns
-- Date: 2025-08-12

BEGIN;

-- 0) Ensure utility extensions (UUID)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1) Ensure timestamp update function exists (needed by earlier migrations)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 2) Note: guardrail_id already added in 20250811_03_policies_add_guardrail_id.sql

-- 3) Outbox and DLQ tables
CREATE TABLE IF NOT EXISTS integration_outbox (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID,
  destination TEXT NOT NULL DEFAULT 'webhook', -- future: 'bus'
  event_type TEXT NOT NULL,
  event_version INTEGER NOT NULL DEFAULT 1,
  payload_json JSONB NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending', -- pending|delivered|failed|dead
  attempts INTEGER NOT NULL DEFAULT 0,
  next_attempt_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  last_error TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_integration_outbox_status_next ON integration_outbox(status, next_attempt_at);
CREATE INDEX IF NOT EXISTS idx_integration_outbox_created_at ON integration_outbox(created_at);

CREATE TABLE IF NOT EXISTS integration_dlq (
  id UUID PRIMARY KEY,
  tenant_id UUID,
  destination TEXT NOT NULL,
  event_type TEXT NOT NULL,
  event_version INTEGER NOT NULL,
  payload_json JSONB NOT NULL,
  attempts INTEGER NOT NULL,
  last_error TEXT,
  dead_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
);

-- 4) Update trigger for timestamps
DROP TRIGGER IF EXISTS trg_outbox_updated_at ON integration_outbox;
CREATE TRIGGER trg_outbox_updated_at BEFORE UPDATE ON integration_outbox
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 5) Helper to enqueue events
CREATE OR REPLACE FUNCTION integration_enqueue_event(p_event_type TEXT, p_payload JSONB)
RETURNS VOID AS $$
BEGIN
  INSERT INTO integration_outbox (event_type, payload_json, status, attempts, next_attempt_at)
  VALUES (p_event_type, p_payload, 'pending', 0, CURRENT_TIMESTAMP);
END;
$$ LANGUAGE plpgsql;

-- 6) Trigger functions per table

-- Agents
CREATE OR REPLACE FUNCTION trg_agents_enqueue()
RETURNS TRIGGER AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'agent.created';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','agent','resource_id', NEW.agent_id),
      'data', jsonb_build_object('change_type','created')
    );
  ELSIF TG_OP = 'UPDATE' THEN
    v_event := 'agent.updated';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','agent','resource_id', NEW.agent_id),
      'data', jsonb_build_object('change_type','updated')
    );
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'agent.deleted';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','agent','resource_id', OLD.agent_id),
      'data', jsonb_build_object('change_type','deleted')
    );
  END IF;
  PERFORM integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trg_agents_integration ON agents;
CREATE TRIGGER trg_agents_integration
AFTER INSERT OR UPDATE OR DELETE ON agents
FOR EACH ROW EXECUTE FUNCTION trg_agents_enqueue();

-- Roles
CREATE OR REPLACE FUNCTION trg_roles_enqueue()
RETURNS TRIGGER AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'role.created';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','role','resource_id', NEW.role_id), 'data', jsonb_build_object('change_type','created'));
  ELSIF TG_OP = 'UPDATE' THEN
    v_event := 'role.updated';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','role','resource_id', NEW.role_id), 'data', jsonb_build_object('change_type','updated'));
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'role.deleted';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','role','resource_id', OLD.role_id), 'data', jsonb_build_object('change_type','deleted'));
  END IF;
  PERFORM integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trg_roles_integration ON roles;
CREATE TRIGGER trg_roles_integration
AFTER INSERT OR UPDATE OR DELETE ON roles
FOR EACH ROW EXECUTE FUNCTION trg_roles_enqueue();

-- Policies
CREATE OR REPLACE FUNCTION trg_policies_enqueue()
RETURNS TRIGGER AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'policy.created';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','policy','resource_id', NEW.policy_id), 'data', jsonb_build_object('change_type','created'));
  ELSIF TG_OP = 'UPDATE' THEN
    v_event := 'policy.updated';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','policy','resource_id', NEW.policy_id), 'data', jsonb_build_object('change_type','updated'));
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'policy.deleted';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','policy','resource_id', OLD.policy_id), 'data', jsonb_build_object('change_type','deleted'));
  END IF;
  PERFORM integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trg_policies_integration ON policies;
CREATE TRIGGER trg_policies_integration
AFTER INSERT OR UPDATE OR DELETE ON policies
FOR EACH ROW EXECUTE FUNCTION trg_policies_enqueue();

-- Agent role-policy assignments (links)
CREATE OR REPLACE FUNCTION trg_arp_enqueue()
RETURNS TRIGGER AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'assignment.linked';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','agent_role_policy','resource_id', jsonb_build_object('agent_id', NEW.agent_id, 'role_id', NEW.role_id, 'group_id', NEW.group_id, 'policy_id', NEW.policy_id)), 'data', jsonb_build_object('change_type','created'));
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'assignment.unlinked';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','agent_role_policy','resource_id', jsonb_build_object('agent_id', OLD.agent_id, 'role_id', OLD.role_id, 'group_id', OLD.group_id, 'policy_id', OLD.policy_id)), 'data', jsonb_build_object('change_type','deleted'));
  ELSE
    RETURN COALESCE(NEW, OLD);
  END IF;
  PERFORM integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trg_arp_integration ON agent_role_policies;
CREATE TRIGGER trg_arp_integration
AFTER INSERT OR DELETE ON agent_role_policies
FOR EACH ROW EXECUTE FUNCTION trg_arp_enqueue();

COMMIT;

