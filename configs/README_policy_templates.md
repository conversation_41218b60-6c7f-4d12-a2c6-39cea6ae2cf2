# Policy Templates Documentation

## Overview
This directory contains SQL files and JSON schemas for managing policy templates in the Vitea application. The system has evolved from simple JSON templates to a comprehensive schema-based validation system that provides both templates and validation rules.

## Files

### Core Template Files
- **`policy_templates.sql`** - Contains all policy template definitions consolidated from multiple schema files
- **`policy_schemas.json`** - JSON schema definitions for policy validation and structure
- **`enhanced-database-schema.sql`** - Core database structure including the `policy_templates` table definition

### Migration Files
- **`20250716_06__policy_cloning_enhancements.sql`** - Policy cloning and audit logging enhancements
- **`20250716_07__enum_management.sql`** - Enum management system for dynamic values
- **`fix_search_policies_function.sql`** - Updated search function with enhanced columns

## Enhanced Template System

### Schema-Based Validation
The system now uses JSON schemas (`policy_schemas.json`) to:
- **Validate policy definitions** against strict schemas
- **Provide autocomplete suggestions** for enum values
- **Generate default templates** based on schema structure
- **Ensure data integrity** across all policy types

### Supported Policy Types

#### 1. Medical Privacy (`medical_privacy`)
- **Purpose**: HIPAA-compliant medical data protection
- **Key Fields**: 
  - `allowed_roles`: ["doctor", "nurse", "admin", "pharmacist", "lab_tech", "specialist", "resident"]
  - `protected_fields`: ["diagnosis", "medication", "lab_orders", "medical_record_number", "treatment_plan", "billing_info", "patient_notes", "prescriptions", "vital_signs", "allergies", "family_history", "immunizations"]
  - `hipaa_compliance`: boolean
  - `audit_requirements`: nested object with retention and logging settings
  - `data_handling`: nested object with encryption and access controls

#### 2. Data Privacy (`data_privacy`)
- **Purpose**: General data privacy and protection
- **Key Fields**:
  - `data_types`: ["personal_info", "financial_data", "contact_details", "biometric_data", "location_data", "behavioral_data"]
  - `privacy_level`: "public", "internal", "confidential", "restricted"
  - `consent_required`: boolean
  - `data_retention`: nested object with retention policies

#### 3. Access Control (`access_control`)
- **Purpose**: Role-based and attribute-based access control
- **Key Fields**:
  - `access_levels`: ["read", "write", "admin", "view_only", "edit", "delete"]
  - `resource_types`: ["documents", "databases", "apis", "files", "reports", "dashboards"]
  - `time_restrictions`: nested object with time-based access
  - `location_restrictions`: nested object with geographic access

#### 4. Compliance (`compliance`)
- **Purpose**: Regulatory compliance and governance
- **Key Fields**:
  - `compliance_frameworks`: ["hipaa", "gdpr", "sox", "pci_dss", "iso27001", "nist"]
  - `audit_frequency`: "daily", "weekly", "monthly", "quarterly", "annually"
  - `reporting_requirements`: nested object with reporting settings
  - `penalty_handling`: nested object with violation responses

## Template Structure

### Database Template (Legacy)
Each template in the database contains:
- **name**: Human-readable template name
- **description**: Template purpose and usage
- **category**: Policy category identifier
- **template_definition**: JSON structure defining the policy
- **is_system_template**: Boolean flag (true for system templates)

### Schema Template (New)
Each schema in `policy_schemas.json` contains:
- **type**: Object type definition
- **title**: Human-readable title
- **description**: Schema purpose and usage
- **properties**: Field definitions with types, constraints, and enums
- **required**: Array of required field names
- **additionalProperties**: false (strict validation)

## Usage

### Frontend Integration
When a user selects a category in the policy creation form:
1. **Schema Loading**: Frontend loads the appropriate schema from `policy_schemas.json`
2. **Template Generation**: `generateDefaultPolicy()` creates a default template based on schema
3. **Validation**: Real-time validation using `validatePolicyDefinition()` against the schema
4. **Autocomplete**: Monaco editor provides suggestions based on schema enum values

### API Endpoints
```
GET /api/v1/policies/templates/{category}     # Get template (legacy)
GET /api/v1/policies/types                    # Get available policy types
POST /api/v1/policies                        # Create policy with validation
PUT /api/v1/policies/{id}                    # Update policy with validation
```

### Database Functions
```sql
-- Get template (legacy)
SELECT * FROM get_policy_template_by_category('medical_privacy');

-- Search policies with enhanced columns
SELECT * FROM search_policies('search_term', 'category', 'severity', true, 20, 0);

-- Clone policy
SELECT * FROM clone_policy('source_policy_id', 'new_policy_name', 'new_description');
```

## Enum Management System

### Database Tables
- **`enum_categories`**: Categories of enum values (e.g., "Medical Roles", "Data Types")
- **`enum_values`**: Individual enum values with metadata

### API Endpoints
```
GET /api/v1/enums/values/{policyType}/{fieldPath}    # Get enum values
GET /api/v1/enums/fields/{policyType}                # Get all enum fields
GET /api/v1/enums/categories                         # Get enum categories
POST /api/v1/enums/categories                        # Create enum category
POST /api/v1/enums/categories/{categoryId}/values    # Add enum value
PUT /api/v1/enums/values/{valueId}                   # Update enum value
DELETE /api/v1/enums/values/{valueId}                # Delete enum value
```

## Adding New Policy Types

### 1. Update Schema
Add to `policy_schemas.json`:
```json
{
  "new_policy_type": {
    "type": "object",
    "title": "New Policy Type",
    "description": "Description of the new policy type",
    "properties": {
      "type": { "type": "string", "const": "new_policy_type" },
      "severity": { "type": "string", "enum": ["low", "medium", "high", "critical"] },
      "description": { "type": "string" },
      "enabled": { "type": "boolean", "default": true },
      // Add your custom fields here
    },
    "required": ["type", "severity", "description"],
    "additionalProperties": false
  }
}
```

### 2. Update Template Generation
Add to `generateDefaultPolicy()` in both frontend and backend:
```javascript
case 'new_policy_type':
  return {
    type: 'new_policy_type',
    severity: 'medium',
    description: 'New policy description',
    enabled: true,
    // Add your default values here
  };
```

### 3. Add Enum Values (if needed)
```sql
INSERT INTO enum_categories (name, description, policy_type, field_path) VALUES
('New Policy Enums', 'Enum values for new policy type', 'new_policy_type', 'custom_field');

INSERT INTO enum_values (category_id, value, description, is_active) VALUES
(enum_category_id, 'value1', 'Description 1', true),
(enum_category_id, 'value2', 'Description 2', true);
```

### 4. Update Documentation
Add the new policy type to this README and any other relevant documentation.

## Validation Features

### Real-time Validation
- **Frontend**: Immediate validation as user types
- **Backend**: Server-side validation before database operations
- **Schema Compliance**: Ensures all policies follow defined schemas
- **Enum Validation**: Validates against database-driven enum values

### Error Handling
- **Visual Indicators**: Red borders and error messages for invalid fields
- **Success Feedback**: Green indicators when validation passes
- **Button States**: Save buttons enable/disable based on validation status

## Migration Path

### From Legacy to Schema-Based
1. **Existing Policies**: Continue to work with legacy templates
2. **New Policies**: Use schema-based validation and templates
3. **Gradual Migration**: Policies can be updated to use new schemas
4. **Backward Compatibility**: Legacy templates remain available

## Maintenance

### Schema Updates
- Modify `policy_schemas.json` to update validation rules
- Update both frontend and backend `generateDefaultPolicy()` functions
- Test with existing policies to ensure compatibility

### Template Updates
- Modify `policy_templates.sql` for legacy template changes
- Re-run the migration to update database templates
- Test template loading in the UI

### Enum Management
- Use the Admin UI to manage enum values dynamically
- Add new categories and values through the web interface
- Monitor enum usage and remove unused values

## Best Practices

### Schema Design
- Use `additionalProperties: false` for strict validation
- Define clear enum values for constrained fields
- Provide meaningful descriptions for all properties
- Use nested objects for complex structures

### Template Design
- Keep templates simple and focused
- Provide realistic default values
- Include all required fields
- Add helpful comments in JSON structure

### Validation Strategy
- Validate on both frontend and backend
- Provide immediate feedback to users
- Use debounced validation for performance
- Log validation errors for debugging 