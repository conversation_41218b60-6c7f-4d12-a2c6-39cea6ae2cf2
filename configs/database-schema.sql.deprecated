-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table
CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    azure_ad_id VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    role VARCHAR(50) NOT NULL DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    updated_by UUID REFERENCES users(user_id)
);

-- Create policies table
CREATE TABLE policies (
    policy_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VA<PERSON>HAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    policy_type VARCHAR(50) DEFAULT 'opa',
    definition JSONB NOT NULL,
    version INTEGER NOT NULL DEFAULT 1,
    is_active BOOLEAN DEFAULT false,
    severity VARCHAR(20) DEFAULT 'medium',
    applies_to_roles TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    updated_by UUID REFERENCES users(user_id),
    UNIQUE(name, version)
);

-- Create policy violations table
CREATE TABLE policy_violations (
    violation_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    policy_id UUID REFERENCES policies(policy_id),
    user_id UUID REFERENCES users(user_id),
    violation_type VARCHAR(100) NOT NULL,
    details JSONB,
    severity VARCHAR(20),
    resolved BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID REFERENCES users(user_id)
);

-- Create documents table
CREATE TABLE documents (
    document_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(500) NOT NULL,
    content TEXT,
    document_type VARCHAR(100),
    metadata JSONB,
    file_path VARCHAR(1000),
    is_sensitive BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    updated_by UUID REFERENCES users(user_id)
);

-- Create audit log table
CREATE TABLE audit_log (
    log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(user_id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100),
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_users_azure_ad_id ON users(azure_ad_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_policies_active ON policies(is_active);
CREATE INDEX idx_policies_category ON policies(category);
CREATE INDEX idx_policies_severity ON policies(severity);
CREATE INDEX idx_policies_roles ON policies USING GIN(applies_to_roles);
CREATE INDEX idx_violations_policy_id ON policy_violations(policy_id);
CREATE INDEX idx_violations_user_id ON policy_violations(user_id);
CREATE INDEX idx_violations_created_at ON policy_violations(created_at);
CREATE INDEX idx_violations_resolved ON policy_violations(resolved);
CREATE INDEX idx_documents_type ON documents(document_type);
CREATE INDEX idx_documents_sensitive ON documents(is_sensitive);
CREATE INDEX idx_documents_metadata ON documents USING GIN(metadata);
CREATE INDEX idx_audit_user_id ON audit_log(user_id);
CREATE INDEX idx_audit_action ON audit_log(action);
CREATE INDEX idx_audit_timestamp ON audit_log(timestamp);
CREATE INDEX idx_audit_resource ON audit_log(resource_type, resource_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_policies_updated_at BEFORE UPDATE ON policies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert system user (for policies, etc.)
INSERT INTO users (user_id, azure_ad_id, email, first_name, last_name, role)
VALUES ('00000000-0000-0000-0000-000000000000', 'system', '<EMAIL>', 'System', 'User', 'system');

-- Insert sample policies
/*INSERT INTO policies (name, description, category, policy_type, definition, is_active, severity, applies_to_roles, created_by, updated_by)
VALUES 
(
    'SSN Masking Policy',
    'Masks SSN fields for non-admin users',
    'data_masking',
    'opa',
    '{"type": "data_masking", "field_patterns": ["ssn", "social_security_number"], "regex_patterns": ["\\\\b\\\\d{3}-\\\\d{2}-\\\\d{4}\\\\b"], "mask_format": "XXX-XX-{last4}"}',
    true,
    'critical',
    ARRAY['user', 'guest'],
    '00000000-0000-0000-0000-000000000000',
    '00000000-0000-0000-0000-000000000000'
),
(
    'PII Access Control',
    'Restricts access to sensitive PII fields',
    'access_control',
    'opa',
    '{"type": "access_control", "restricted_fields": ["medical_record_number", "insurance_number"], "allowed_roles": ["admin", "manager"]}',
    true,
    'high',
    ARRAY['user', 'guest'],
    '00000000-0000-0000-0000-000000000000',
    '00000000-0000-0000-0000-000000000000'
);*/

-- Insert sample documents
INSERT INTO documents (title, content, document_type, is_sensitive, metadata, created_by, updated_by)
VALUES 
(
    'HIPAA Compliance Guidelines',
    'This document outlines HIPAA compliance requirements for healthcare organizations...',
    'guideline',
    false,
    '{"category": "compliance", "department": "legal"}',
    '00000000-0000-0000-0000-000000000000',
    '00000000-0000-0000-0000-000000000000'
),
(
    'Patient Care Protocol - Diabetes',
    'Standard care protocol for diabetes patients including medication guidelines...',
    'protocol',
    true,
    '{"category": "medical", "condition": "diabetes", "department": "endocrinology"}',
    '00000000-0000-0000-0000-000000000000',
    '00000000-0000-0000-0000-000000000000'
);
