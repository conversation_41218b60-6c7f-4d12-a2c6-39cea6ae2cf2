-- Enhanced Database Schema for MCP Flow and Policy Management
-- Add these tables to your existing database

-- MCP Chat Sessions Table
CREATE TABLE mcp_chat_sessions (
    session_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(user_id),
    status VARCHAR(20) DEFAULT 'active', -- active, completed, terminated
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}'
);

-- MCP Flow Steps Table (tracks the 12-step process)
CREATE TABLE mcp_flow_steps (
    step_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES mcp_chat_sessions(session_id),
    step_number INTEGER NOT NULL, -- 1-12 as per the flow
    step_name VARCHAR(100) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- pending, processing, completed, failed
    input_data JSONB,
    output_data JSONB,
    processing_time_ms INTEGER,
    policies_applied UUID[] DEFAULT '{}', -- Array of policy IDs applied
    violations_detected UUID[] DEFAULT '{}', -- Array of violation IDs
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Chat Messages Table
CREATE TABLE chat_messages (
    message_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES mcp_chat_sessions(session_id),
    role VARCHAR(20) NOT NULL, -- user, assistant, system
    content TEXT NOT NULL,
    original_content TEXT, -- before policy filtering
    policies_applied UUID[] DEFAULT '{}',
    is_filtered BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Policy Executions Table (detailed logging)
CREATE TABLE policy_executions (
    execution_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES mcp_chat_sessions(session_id),
    step_id UUID REFERENCES mcp_flow_steps(step_id),
    policy_id UUID REFERENCES policies(policy_id),
    execution_status VARCHAR(20), -- passed, failed, error
    input_data JSONB,
    output_data JSONB,
    execution_time_ms INTEGER,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Azure OpenAI API Calls Table (for monitoring and billing)
CREATE TABLE openai_api_calls (
    call_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES mcp_chat_sessions(session_id),
    step_id UUID REFERENCES mcp_flow_steps(step_id),
    model_name VARCHAR(50),
    prompt_tokens INTEGER,
    completion_tokens INTEGER,
    total_tokens INTEGER,
    cost_estimate DECIMAL(10,6),
    response_time_ms INTEGER,
    status_code INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Policy Templates Table (for easier policy creation)
CREATE TABLE policy_templates (
    template_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    template_definition JSONB NOT NULL,
    is_system_template BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id)
);

-- System Metrics Table (for dashboard analytics)
CREATE TABLE system_metrics (
    metric_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,6),
    metric_unit VARCHAR(20),
    tags JSONB DEFAULT '{}',
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_mcp_sessions_user_id ON mcp_chat_sessions(user_id);
CREATE INDEX idx_mcp_sessions_status ON mcp_chat_sessions(status);
CREATE INDEX idx_mcp_sessions_created_at ON mcp_chat_sessions(created_at);

CREATE INDEX idx_mcp_flow_steps_session_id ON mcp_flow_steps(session_id);
CREATE INDEX idx_mcp_flow_steps_step_number ON mcp_flow_steps(step_number);
CREATE INDEX idx_mcp_flow_steps_status ON mcp_flow_steps(status);

CREATE INDEX idx_chat_messages_session_id ON chat_messages(session_id);
CREATE INDEX idx_chat_messages_role ON chat_messages(role);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);

CREATE INDEX idx_policy_executions_session_id ON policy_executions(session_id);
CREATE INDEX idx_policy_executions_policy_id ON policy_executions(policy_id);
CREATE INDEX idx_policy_executions_status ON policy_executions(execution_status);

CREATE INDEX idx_openai_calls_session_id ON openai_api_calls(session_id);
CREATE INDEX idx_openai_calls_created_at ON openai_api_calls(created_at);

CREATE INDEX idx_policy_templates_category ON policy_templates(category);
CREATE INDEX idx_system_metrics_name ON system_metrics(metric_name);
CREATE INDEX idx_system_metrics_recorded_at ON system_metrics(recorded_at);

-- Create updated_at triggers for new tables
CREATE TRIGGER update_mcp_sessions_updated_at BEFORE UPDATE ON mcp_chat_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Policy templates are now managed in a separate file: policy_templates.sql
-- This ensures all templates are consolidated and easier to maintain

-- Insert sample system metrics
INSERT INTO system_metrics (metric_name, metric_value, metric_unit, tags) VALUES
('total_chat_sessions', 0, 'count', '{"period": "daily"}'),
('average_response_time', 0, 'milliseconds', '{"component": "api"}'),
('policy_violations_count', 0, 'count', '{"severity": "all"}'),
('openai_api_cost', 0, 'dollars', '{"period": "daily"}'),
('active_policies_count', 2, 'count', '{"status": "active"}');