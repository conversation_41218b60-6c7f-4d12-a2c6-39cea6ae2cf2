-- Drop the existing search_policies function first
DROP FUNCTION IF EXISTS search_policies(<PERSON><PERSON><PERSON><PERSON>(255), VARCHAR(100), VARCHAR(20), BOOLEAN, INTEGER, INTEGER);

-- Create the updated search_policies function with definition and other missing columns
CREATE OR REPLACE FUNCTION search_policies(
    p_search_term VARCHAR(255),
    p_category VARCHAR(100),
    p_severity VARCHAR(20),
    p_is_active BOOLEAN,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
) RETURNS TABLE(
    policy_id UUID,
    name VARCHAR(255),
    description TEXT,
    category VARCHAR(100),
    severity VARCHAR(20),
    is_active BOOLEAN,
    policy_type VARCHAR(50),
    definition JSONB,
    applies_to_roles TEXT[],
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    total_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    WITH filtered_policies AS (
        SELECT p.*,
               COUNT(*) OVER() as total_count
        FROM policies p
        WHERE p.deleted_at IS NULL
        AND (p_search_term IS NULL OR 
             p.name ILIKE '%' || p_search_term || '%' OR 
             p.description ILIKE '%' || p_search_term || '%')
        AND (p_category IS NULL OR p.category = p_category)
        AND (p_severity IS NULL OR p.severity = p_severity)
        AND (p_is_active IS NULL OR p.is_active = p_is_active)
    )
    SELECT fp.policy_id, fp.name, fp.description, fp.category, fp.severity,
           fp.is_active, fp.policy_type, fp.definition, fp.applies_to_roles,
           fp.created_at, fp.updated_at, fp.total_count
    FROM filtered_policies fp
    ORDER BY fp.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql; 