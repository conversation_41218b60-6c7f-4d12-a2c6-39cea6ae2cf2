-- Policy Templates for Vitea Application
-- This file contains all policy template definitions for the system

-- Insert comprehensive policy templates for all categories
INSERT INTO policy_templates (name, description, category, template_definition, is_system_template) VALUES
-- Data Masking Templates
(
    'PII Masking Template',
    'Template for masking personally identifiable information',
    'data_masking',
    '{
        "type": "data_masking",
        "field_patterns": ["ssn", "social_security_number", "phone", "email"],
        "regex_patterns": ["\\\\b\\\\d{3}-\\\\d{2}-\\\\d{4}\\\\b", "\\\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\\\.[A-Z|a-z]{2,}\\\\b"],
        "mask_format": "XXX-XX-{last4}",
        "severity": "critical"
    }',
    true
),
(
    'Phone Number Masking Template',
    'Template for masking phone number fields',
    'data_masking',
    '{
        "type": "data_masking",
        "mask_format": "XXX-XX-{last4}",
        "field_patterns": ["phone", "phone_number"],
        "regex_patterns": ["\\\\b\\\\d{3}-\\\\d{2}-\\\\d{4}\\\\b"],
        "severity": "high"
    }',
    true
),

-- Content Safety Templates
(
    'Content Safety Template',
    'Template for detecting harmful or inappropriate content',
    'content_safety',
    '{
        "type": "content_filtering",
        "blocked_categories": ["hate_speech", "violence", "self_harm", "sexual"],
        "confidence_threshold": 0.7,
        "action": "block_and_log",
        "severity": "high"
    }',
    true
),

-- Medical Privacy Templates
(
    'Medical Info Protection Template',
    'Template for protecting sensitive medical information',
    'medical_privacy',
    '{
        "type": "medical_privacy",
        "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"],
        "hipaa_compliance": true,
        "allowed_roles": ["doctor", "nurse", "admin"],
        "severity": "critical"
    }',
    true
),

-- Data Encryption Templates
(
    'Data Encryption Template',
    'Template for encrypting sensitive data fields',
    'data_encryption',
    '{
        "type": "data_encryption",
        "encryption_algorithm": "AES-256",
        "fields_to_encrypt": ["credit_card", "bank_account", "ssn"],
        "key_rotation_days": 90,
        "severity": "critical"
    }',
    true
),

-- Access Logging Templates
(
    'Access Logging Template',
    'Template for comprehensive access logging',
    'access_logging',
    '{
        "type": "access_logging",
        "log_level": "detailed",
        "log_fields": ["user_id", "timestamp", "resource", "action", "ip_address"],
        "retention_days": 2555,
        "hipaa_compliant": true,
        "severity": "high"
    }',
    true
),

-- Data Retention Templates
(
    'Data Retention Template',
    'Template for data retention policies',
    'data_retention',
    '{
        "type": "data_retention",
        "retention_period_days": 2555,
        "data_types": ["patient_records", "medical_history", "billing_info"],
        "auto_deletion": true,
        "backup_retention_days": 365,
        "severity": "critical"
    }',
    true
),

-- Access Control Templates
(
    'Access Control Template',
    'Template for role-based access control',
    'access_control',
    '{
        "type": "access_control",
        "allowed_roles": ["admin", "manager"],
        "restricted_actions": ["delete", "export"],
        "ip_whitelist": ["***********/24"],
        "time_restrictions": {
            "start_time": "09:00",
            "end_time": "17:00",
            "timezone": "UTC"
        },
        "severity": "high"
    }',
    true
),

-- Compliance Templates
(
    'Compliance Template',
    'Template for regulatory compliance policies',
    'compliance',
    '{
        "type": "compliance",
        "regulations": ["HIPAA", "GDPR", "SOX"],
        "audit_requirements": true,
        "documentation_required": true,
        "penalty_severity": "high",
        "severity": "critical"
    }',
    true
); 