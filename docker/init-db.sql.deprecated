-- Docker PostgreSQL Initialization Script
-- This file combines both schema files for container initialization

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\echo 'Loading base database schema...'
\i /docker-entrypoint-initdb.d/01-schema.sql

\echo 'Loading enhanced database schema...'
\i /docker-entrypoint-initdb.d/02-enhanced-schema.sql

\echo 'Database initialization completed successfully!'