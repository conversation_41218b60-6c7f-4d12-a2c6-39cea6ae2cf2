# API Documentation

## Overview

The Vitea.ai Policy Management API provides comprehensive endpoints for managing policies, templates, and enum values. The API is built with Node.js/Express.js and uses PostgreSQL for data storage.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

All API endpoints require admin authentication. Include the following header:

```
Authorization: Bearer admin-token
```

## Policy Management Endpoints

### List Policies

**GET** `/policies`

Retrieve a list of policies with optional filtering and pagination.

#### Query Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `search` | string | Search term for policy name/description | null |
| `category` | string | Filter by policy category | null |
| `severity` | string | Filter by severity level | null |
| `is_active` | boolean | Filter by active status | null |
| `limit` | integer | Number of policies per page | 20 |
| `offset` | integer | Number of policies to skip | 0 |

#### Response

```json
{
  "policies": [
    {
      "policy_id": "uuid",
      "name": "Medical Privacy Policy",
      "description": "HIPAA-compliant medical data protection",
      "category": "medical_privacy",
      "severity": "high",
      "is_active": true,
      "policy_type": "medical_privacy",
      "definition": {
        "type": "medical_privacy",
        "severity": "high",
        "description": "HIPAA-compliant medical data protection",
        "enabled": true,
        "allowed_roles": ["doctor", "nurse", "admin"],
        "protected_fields": ["diagnosis", "medication", "lab_orders"],
        "hipaa_compliance": true,
        "audit_requirements": {
          "retention_period": 7,
          "log_access_events": true,
          "encrypt_data": true
        },
        "data_handling": {
          "encryption_required": true,
          "access_logging": true,
          "data_masking": true
        }
      },
      "applies_to_roles": ["doctor", "nurse", "admin"],
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ],
  "total_count": 25,
  "limit": 20,
  "offset": 0
}
```

### Create Policy

**POST** `/policies`

Create a new policy with schema validation.

#### Request Body

```json
{
  "name": "New Medical Policy",
  "description": "HIPAA-compliant medical data protection",
  "category": "medical_privacy",
  "severity": "high",
  "policy_type": "medical_privacy",
  "definition": {
    "type": "medical_privacy",
    "severity": "high",
    "description": "HIPAA-compliant medical data protection",
    "enabled": true,
    "allowed_roles": ["doctor", "nurse", "admin"],
    "protected_fields": ["diagnosis", "medication", "lab_orders"],
    "hipaa_compliance": true,
    "audit_requirements": {
      "retention_period": 7,
      "log_access_events": true,
      "encrypt_data": true
    },
    "data_handling": {
      "encryption_required": true,
      "access_logging": true,
      "data_masking": true
    }
  },
  "applies_to_roles": ["doctor", "nurse", "admin"]
}
```

#### Response

```json
{
  "policy_id": "uuid",
  "name": "New Medical Policy",
  "description": "HIPAA-compliant medical data protection",
  "category": "medical_privacy",
  "severity": "high",
  "is_active": true,
  "policy_type": "medical_privacy",
  "definition": { /* policy definition */ },
  "applies_to_roles": ["doctor", "nurse", "admin"],
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### Get Policy

**GET** `/policies/:id`

Retrieve a specific policy by ID.

#### Response

```json
{
  "policy_id": "uuid",
  "name": "Medical Privacy Policy",
  "description": "HIPAA-compliant medical data protection",
  "category": "medical_privacy",
  "severity": "high",
  "is_active": true,
  "policy_type": "medical_privacy",
  "definition": { /* policy definition */ },
  "applies_to_roles": ["doctor", "nurse", "admin"],
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### Update Policy

**PUT** `/policies/:id`

Update an existing policy with schema validation.

#### Request Body

Same as Create Policy.

#### Response

```json
{
  "policy_id": "uuid",
  "name": "Updated Medical Policy",
  "description": "Updated HIPAA-compliant medical data protection",
  "category": "medical_privacy",
  "severity": "critical",
  "is_active": true,
  "policy_type": "medical_privacy",
  "definition": { /* updated policy definition */ },
  "applies_to_roles": ["doctor", "nurse", "admin", "specialist"],
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T11:00:00Z"
}
```

### Delete Policy

**DELETE** `/policies/:id`

Soft delete a policy (marks as deleted but doesn't remove from database).

#### Response

```json
{
  "message": "Policy deleted successfully",
  "policy_id": "uuid"
}
```

### Get Policy Templates

**GET** `/policies/templates/:category`

Get a policy template for a specific category (legacy endpoint).

#### Response

```json
{
  "name": "Medical Privacy Template",
  "description": "HIPAA-compliant medical data protection template",
  "category": "medical_privacy",
  "template_definition": {
    "type": "medical_privacy",
    "severity": "high",
    "description": "HIPAA-compliant medical data protection",
    "enabled": true,
    "allowed_roles": ["doctor", "nurse", "admin"],
    "protected_fields": ["diagnosis", "medication", "lab_orders"],
    "hipaa_compliance": true,
    "audit_requirements": {
      "retention_period": 7,
      "log_access_events": true,
      "encrypt_data": true
    },
    "data_handling": {
      "encryption_required": true,
      "access_logging": true,
      "data_masking": true
    }
  },
  "is_system_template": true
}
```

### Get Available Policy Types

**GET** `/policies/types`

Get all available policy types from the schema.

#### Response

```json
{
  "policy_types": [
    "medical_privacy",
    "data_privacy",
    "access_control",
    "compliance"
  ]
}
```

## Enum Management Endpoints

### Get Enum Values

**GET** `/enums/values/:policyType/:fieldPath`

Get enum values for a specific policy type and field path.

#### Response

```json
{
  "values": [
    {
      "value_id": 1,
      "value": "doctor",
      "description": "Medical doctor role",
      "is_active": true,
      "category": {
        "category_id": 1,
        "name": "Medical Roles",
        "description": "Medical staff roles",
        "policy_type": "medical_privacy",
        "field_path": "allowed_roles"
      }
    },
    {
      "value_id": 2,
      "value": "nurse",
      "description": "Nursing staff role",
      "is_active": true,
      "category": {
        "category_id": 1,
        "name": "Medical Roles",
        "description": "Medical staff roles",
        "policy_type": "medical_privacy",
        "field_path": "allowed_roles"
      }
    }
  ]
}
```

### Get All Enum Fields

**GET** `/enums/fields/:policyType`

Get all enum fields for a specific policy type.

#### Response

```json
{
  "fields": [
    {
      "field_path": "allowed_roles",
      "category_name": "Medical Roles",
      "category_description": "Medical staff roles",
      "values": [
        {
          "value": "doctor",
          "description": "Medical doctor role"
        },
        {
          "value": "nurse",
          "description": "Nursing staff role"
        }
      ]
    },
    {
      "field_path": "protected_fields",
      "category_name": "Medical Fields",
      "category_description": "Protected medical data fields",
      "values": [
        {
          "value": "diagnosis",
          "description": "Patient diagnosis information"
        },
        {
          "value": "medication",
          "description": "Prescribed medications"
        }
      ]
    }
  ]
}
```

### Get Enum Categories

**GET** `/enums/categories`

Get all enum categories.

#### Response

```json
{
  "categories": [
    {
      "category_id": 1,
      "name": "Medical Roles",
      "description": "Medical staff roles",
      "policy_type": "medical_privacy",
      "field_path": "allowed_roles",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    },
    {
      "category_id": 2,
      "name": "Medical Fields",
      "description": "Protected medical data fields",
      "policy_type": "medical_privacy",
      "field_path": "protected_fields",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### Create Enum Category

**POST** `/enums/categories`

Create a new enum category.

#### Request Body

```json
{
  "name": "New Category",
  "description": "Description of the new category",
  "policy_type": "medical_privacy",
  "field_path": "new_field"
}
```

#### Response

```json
{
  "category_id": 3,
  "name": "New Category",
  "description": "Description of the new category",
  "policy_type": "medical_privacy",
  "field_path": "new_field",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### Add Enum Value

**POST** `/enums/categories/:categoryId/values`

Add a new enum value to a category.

#### Request Body

```json
{
  "value": "new_value",
  "description": "Description of the new value"
}
```

#### Response

```json
{
  "value_id": 5,
  "value": "new_value",
  "description": "Description of the new value",
  "is_active": true,
  "category_id": 3,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### Update Enum Value

**PUT** `/enums/values/:valueId`

Update an existing enum value.

#### Request Body

```json
{
  "value": "updated_value",
  "description": "Updated description",
  "is_active": true
}
```

#### Response

```json
{
  "value_id": 5,
  "value": "updated_value",
  "description": "Updated description",
  "is_active": true,
  "category_id": 3,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T11:00:00Z"
}
```

### Delete Enum Value

**DELETE** `/enums/values/:valueId`

Soft delete an enum value.

#### Response

```json
{
  "message": "Enum value deleted successfully",
  "value_id": 5
}
```

## Error Responses

### Validation Error

```json
{
  "error": "Validation failed",
  "details": [
    {
      "field": "definition.allowed_roles",
      "message": "must be an array",
      "value": "doctor"
    },
    {
      "field": "definition.severity",
      "message": "must be one of: low, medium, high, critical",
      "value": "very_high"
    }
  ]
}
```

### Not Found Error

```json
{
  "error": "Policy not found",
  "policy_id": "uuid"
}
```

### Authentication Error

```json
{
  "error": "Admin access required"
}
```

### Server Error

```json
{
  "error": "Internal server error",
  "message": "Database connection failed"
}
```

## Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 201 | Created |
| 400 | Bad Request (validation error) |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 500 | Internal Server Error |

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **100 requests per minute** per IP address
- **1000 requests per hour** per IP address

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642234567
```

## Pagination

List endpoints support pagination with the following headers:

```
X-Total-Count: 150
X-Page-Count: 8
X-Current-Page: 1
X-Per-Page: 20
```

## Filtering

List endpoints support filtering with query parameters:

- `search`: Text search in name and description
- `category`: Filter by policy category
- `severity`: Filter by severity level
- `is_active`: Filter by active status
- `created_after`: Filter by creation date
- `updated_after`: Filter by update date

## Sorting

List endpoints support sorting with the `sort` parameter:

- `name`: Sort by policy name
- `created_at`: Sort by creation date
- `updated_at`: Sort by update date
- `severity`: Sort by severity level

Use `-` prefix for descending order (e.g., `-created_at`).

## Examples

### Create a Medical Privacy Policy

```bash
curl -X POST http://localhost:8000/api/v1/policies \
  -H "Authorization: Bearer admin-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "HIPAA Medical Policy",
    "description": "HIPAA-compliant medical data protection",
    "category": "medical_privacy",
    "severity": "high",
    "policy_type": "medical_privacy",
    "definition": {
      "type": "medical_privacy",
      "severity": "high",
      "description": "HIPAA-compliant medical data protection",
      "enabled": true,
      "allowed_roles": ["doctor", "nurse", "admin"],
      "protected_fields": ["diagnosis", "medication", "lab_orders"],
      "hipaa_compliance": true,
      "audit_requirements": {
        "retention_period": 7,
        "log_access_events": true,
        "encrypt_data": true
      },
      "data_handling": {
        "encryption_required": true,
        "access_logging": true,
        "data_masking": true
      }
    },
    "applies_to_roles": ["doctor", "nurse", "admin"]
  }'
```

### Get Enum Values for Medical Roles

```bash
curl -X GET "http://localhost:8000/api/v1/enums/values/medical_privacy/allowed_roles" \
  -H "Authorization: Bearer admin-token"
```

### Search Policies

```bash
curl -X GET "http://localhost:8000/api/v1/policies?search=medical&category=medical_privacy&severity=high&limit=10" \
  -H "Authorization: Bearer admin-token"
``` 