# React Component Guide

## Overview

This document provides comprehensive documentation for all React components in the Vitea.ai Policy Management System. The components are organized by functionality and include detailed usage examples.

## Core Components

### EnhancedPolicyModal

The main modal component for creating, editing, and cloning policies.

**Location**: `admin-ui-project/src/EnhancedPolicyModal.js`

**Props**:
```javascript
{
  isOpen: boolean,           // Whether modal is open
  onClose: function,         // Close handler
  existingPolicy: object,    // Policy to edit/clone
  onSave: function,          // Save handler
  availablePolicies: array   // Available policies for cloning
}
```

**Features**:
- Real-time form validation
- Monaco editor integration
- Schema-based template generation
- Policy cloning functionality
- Visual error indicators

**Usage**:
```javascript
<EnhancedPolicyModal
  isOpen={showModal}
  onClose={() => setShowModal(false)}
  existingPolicy={selectedPolicy}
  onSave={handleSavePolicy}
  availablePolicies={policies}
/>
```

**State Management**:
```javascript
const [formData, setFormData] = useState({
  name: '',
  description: '',
  category: '',
  severity: '',
  definition: '{\n \n}'
});

const [errors, setErrors] = useState({});
const [isLoading, setIsLoading] = useState(false);
const [templateLoaded, setTemplateLoaded] = useState(false);
```

### PolicyForm

Reusable form component for policy creation and editing.

**Location**: `admin-ui-project/src/components/PolicyForm.js`

**Props**:
```javascript
{
  formData: object,          // Form data object
  errors: object,           // Validation errors
  onChange: function,       // Change handler
  onCategoryChange: function, // Category change handler
  isEditing: boolean,       // Whether in edit mode
  isLoading: boolean        // Loading state
}
```

**Features**:
- Form field validation
- Category-based template loading
- Real-time error display
- Loading state handling

**Usage**:
```javascript
<PolicyForm
  formData={formData}
  errors={errors}
  onChange={handleFormChange}
  onCategoryChange={handleCategoryChange}
  isEditing={isEditing}
  isLoading={isLoading}
/>
```

### PolicyPicklist

Searchable picklist for selecting existing policies to clone.

**Location**: `admin-ui-project/src/components/PolicyPicklist.js`

**Props**:
```javascript
{
  policies: array,          // Available policies
  onSelect: function,       // Selection handler
  onClose: function,        // Close handler
  isLoading: boolean        // Loading state
}
```

**Features**:
- Searchable policy list
- Pagination support
- Loading states
- Empty state handling

**Usage**:
```javascript
<PolicyPicklist
  policies={availablePolicies}
  onSelect={handlePolicySelect}
  onClose={() => setShowPicklist(false)}
  isLoading={loadingPolicies}
/>
```

## Editor Components

### JsonEditorWithAutocomplete

Enhanced JSON editor with autocomplete and validation.

**Location**: `admin-ui-project/src/components/JsonEditorWithAutocomplete.js`

**Props**:
```javascript
{
  value: string,            // JSON string value
  onChange: function,       // Change handler
  onValidation: function,   // Validation handler
  policyType: string,       // Current policy type
  height: string           // Editor height
}
```

**Features**:
- Monaco editor integration
- Schema-based autocomplete
- Real-time validation
- Error highlighting
- Custom completion provider

**Usage**:
```javascript
<JsonEditorWithAutocomplete
  value={formData.definition}
  onChange={handleDefinitionChange}
  onValidation={handleValidation}
  policyType={formData.category}
  height="400px"
/>
```

**Autocomplete Features**:
```javascript
// Enum value suggestions
const enumSuggestions = [
  { label: 'doctor', detail: 'Medical doctor role' },
  { label: 'nurse', detail: 'Nursing staff role' },
  { label: 'admin', detail: 'Administrative staff role' }
];

// Structure suggestions
const structureSuggestions = [
  { label: 'allowed_roles', detail: 'Array of allowed roles' },
  { label: 'protected_fields', detail: 'Array of protected fields' },
  { label: 'hipaa_compliance', detail: 'Boolean for HIPAA compliance' }
];
```

### EnumSuggestions

Panel displaying clickable enum values for the selected policy type.

**Location**: `admin-ui-project/src/components/EnumSuggestions.js`

**Props**:
```javascript
{
  policyType: string,       // Current policy type
  onValueClick: function,   // Value click handler
  selectedField: string     // Currently selected field
}
```

**Features**:
- Dynamic enum value loading
- Clickable value badges
- Field-specific suggestions
- Loading states

**Usage**:
```javascript
<EnumSuggestions
  policyType={formData.category}
  onValueClick={handleEnumValueClick}
  selectedField={selectedField}
/>
```

### JsonStructureGuide

Panel displaying the default JSON structure for the selected policy type.

**Location**: `admin-ui-project/src/components/JsonStructureGuide.js`

**Props**:
```javascript
{
  policyType: string,       // Current policy type
  height: string           // Panel height
}
```

**Features**:
- Template structure display
- Syntax highlighting
- Copy functionality
- Responsive design

**Usage**:
```javascript
<JsonStructureGuide
  policyType={formData.category}
  height="300px"
/>
```

## Management Components

### EnumManagement

Modal component for managing enum categories and values.

**Location**: `admin-ui-project/src/components/EnumManagement.js`

**Props**:
```javascript
{
  isOpen: boolean,          // Whether modal is open
  onClose: function         // Close handler
}
```

**Features**:
- Category management
- Value management
- Add/edit/delete operations
- Loading states
- Error handling

**Usage**:
```javascript
<EnumManagement
  isOpen={showEnumManagement}
  onClose={() => setShowEnumManagement(false)}
/>
```

**State Management**:
```javascript
const [categories, setCategories] = useState([]);
const [values, setValues] = useState([]);
const [loading, setLoading] = useState(false);
const [error, setError] = useState(null);
const [editingCategory, setEditingCategory] = useState(null);
const [editingValue, setEditingValue] = useState(null);
```

### PolicyTable

Table component for displaying and managing policies.

**Location**: `admin-ui-project/src/components/PolicyTable.js`

**Props**:
```javascript
{
  policies: array,          // Policies to display
  onEdit: function,         // Edit handler
  onDelete: function,       // Delete handler
  onSearch: function,       // Search handler
  loading: boolean,         // Loading state
  pagination: object        // Pagination info
}
```

**Features**:
- Sortable columns
- Search functionality
- Pagination
- Action buttons
- Loading states

**Usage**:
```javascript
<PolicyTable
  policies={policies}
  onEdit={handleEditPolicy}
  onDelete={handleDeletePolicy}
  onSearch={handleSearch}
  loading={loading}
  pagination={pagination}
/>
```

## Utility Components

### LoadingSpinner

Reusable loading spinner component.

**Location**: `admin-ui-project/src/components/LoadingSpinner.js`

**Props**:
```javascript
{
  size: string,             // Spinner size (sm, md, lg)
  color: string,           // Spinner color
  text: string            // Loading text
}
```

**Usage**:
```javascript
<LoadingSpinner size="md" color="blue" text="Loading policies..." />
```

### ErrorMessage

Error message display component.

**Location**: `admin-ui-project/src/components/ErrorMessage.js`

**Props**:
```javascript
{
  error: string,           // Error message
  onRetry: function        // Retry handler
}
```

**Usage**:
```javascript
<ErrorMessage error="Failed to load policies" onRetry={handleRetry} />
```

### SuccessMessage

Success message display component.

**Location**: `admin-ui-project/src/components/SuccessMessage.js`

**Props**:
```javascript
{
  message: string,         // Success message
  onClose: function        // Close handler
}
```

**Usage**:
```javascript
<SuccessMessage message="Policy created successfully" onClose={handleClose} />
```

## Form Components

### FormField

Reusable form field component with validation.

**Location**: `admin-ui-project/src/components/FormField.js`

**Props**:
```javascript
{
  label: string,           // Field label
  name: string,            // Field name
  type: string,            // Input type
  value: any,             // Field value
  onChange: function,      // Change handler
  error: string,          // Error message
  required: boolean,       // Required field
  options: array          // Select options
}
```

**Features**:
- Multiple input types
- Validation display
- Required field indicators
- Error styling

**Usage**:
```javascript
<FormField
  label="Policy Name"
  name="name"
  type="text"
  value={formData.name}
  onChange={handleChange}
  error={errors.name}
  required={true}
/>
```

### SelectField

Enhanced select field component.

**Location**: `admin-ui-project/src/components/SelectField.js`

**Props**:
```javascript
{
  label: string,           // Field label
  name: string,            // Field name
  value: string,           // Selected value
  options: array,          // Select options
  onChange: function,      // Change handler
  error: string,          // Error message
  required: boolean,       // Required field
  placeholder: string      // Placeholder text
}
```

**Usage**:
```javascript
<SelectField
  label="Category"
  name="category"
  value={formData.category}
  options={categoryOptions}
  onChange={handleChange}
  error={errors.category}
  required={true}
  placeholder="Select a category..."
/>
```

## Layout Components

### Modal

Reusable modal component.

**Location**: `admin-ui-project/src/components/Modal.js`

**Props**:
```javascript
{
  isOpen: boolean,         // Whether modal is open
  onClose: function,       // Close handler
  title: string,          // Modal title
  children: node,         // Modal content
  size: string           // Modal size (sm, md, lg, xl)
}
```

**Usage**:
```javascript
<Modal
  isOpen={showModal}
  onClose={() => setShowModal(false)}
  title="Create New Policy"
  size="lg"
>
  <PolicyForm {...formProps} />
</Modal>
```

### Header

Application header component.

**Location**: `admin-ui-project/src/components/Header.js`

**Props**:
```javascript
{
  title: string,           // Header title
  onNewPolicy: function,   // New policy handler
  onManageEnums: function, // Manage enums handler
  user: object            // User information
}
```

**Usage**:
```javascript
<Header
  title="Policy Management"
  onNewPolicy={() => setShowModal(true)}
  onManageEnums={() => setShowEnumManagement(true)}
  user={currentUser}
/>
```

## Hook Components

### usePolicyValidation

Custom hook for policy validation.

**Location**: `admin-ui-project/src/hooks/usePolicyValidation.js`

**Usage**:
```javascript
const { validatePolicy, errors, isValid } = usePolicyValidation();

const handleValidation = () => {
  const validationErrors = validatePolicy(formData);
  setErrors(validationErrors);
};
```

**Features**:
- Schema-based validation
- Real-time validation
- Error formatting
- Validation state management

### useDebounce

Custom hook for debouncing function calls.

**Location**: `admin-ui-project/src/hooks/useDebounce.js`

**Usage**:
```javascript
const debouncedValidation = useDebounce(validatePolicy, 500);

useEffect(() => {
  debouncedValidation(formData);
}, [formData, debouncedValidation]);
```

### useApi

Custom hook for API calls.

**Location**: `admin-ui-project/src/hooks/useApi.js`

**Usage**:
```javascript
const { data, loading, error, execute } = useApi();

const handleSave = async () => {
  await execute('/api/v1/policies', {
    method: 'POST',
    body: formData
  });
};
```

## Component Patterns

### Controlled Components

All form components follow the controlled component pattern:

```javascript
const [formData, setFormData] = useState(initialData);

const handleChange = (name, value) => {
  setFormData(prev => ({
    ...prev,
    [name]: value
  }));
};

<FormField
  name="name"
  value={formData.name}
  onChange={(value) => handleChange('name', value)}
/>
```

### Error Boundary

Error boundary for catching component errors:

```javascript
class PolicyErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Policy component error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorMessage error="Something went wrong" />;
    }

    return this.props.children;
  }
}
```

### Loading States

Consistent loading state handling:

```javascript
const [loading, setLoading] = useState(false);

const handleSave = async () => {
  setLoading(true);
  try {
    await savePolicy(formData);
    onSuccess();
  } catch (error) {
    setError(error.message);
  } finally {
    setLoading(false);
  }
};

{loading ? <LoadingSpinner /> : <SaveButton />}
```

## Styling

### Tailwind CSS Classes

Components use Tailwind CSS for styling:

```javascript
// Common button styles
const buttonClasses = {
  primary: "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded",
  secondary: "bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded",
  danger: "bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded"
};

// Form field styles
const fieldClasses = {
  input: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",
  error: "border-red-500 focus:ring-red-500",
  success: "border-green-500 focus:ring-green-500"
};
```

### Responsive Design

Components are responsive:

```javascript
// Responsive grid
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {policies.map(policy => (
    <PolicyCard key={policy.id} policy={policy} />
  ))}
</div>

// Responsive modal
<div className="w-full max-w-4xl mx-auto p-4">
  <Modal size="xl">
    <PolicyForm />
  </Modal>
</div>
```

## Testing

### Component Testing

Example test for PolicyForm:

```javascript
import { render, screen, fireEvent } from '@testing-library/react';
import PolicyForm from '../components/PolicyForm';

test('renders policy form with all fields', () => {
  const mockOnChange = jest.fn();
  const mockOnCategoryChange = jest.fn();
  
  render(
    <PolicyForm
      formData={{
        name: '',
        description: '',
        category: '',
        severity: '',
        definition: '{}'
      }}
      errors={{}}
      onChange={mockOnChange}
      onCategoryChange={mockOnCategoryChange}
      isEditing={false}
      isLoading={false}
    />
  );

  expect(screen.getByLabelText(/policy name/i)).toBeInTheDocument();
  expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
  expect(screen.getByLabelText(/category/i)).toBeInTheDocument();
  expect(screen.getByLabelText(/severity/i)).toBeInTheDocument();
});

test('displays validation errors', () => {
  const errors = {
    name: 'Policy name is required',
    category: 'Category is required'
  };

  render(
    <PolicyForm
      formData={{}}
      errors={errors}
      onChange={jest.fn()}
      onCategoryChange={jest.fn()}
      isEditing={false}
      isLoading={false}
    />
  );

  expect(screen.getByText('Policy name is required')).toBeInTheDocument();
  expect(screen.getByText('Category is required')).toBeInTheDocument();
});
```

### Hook Testing

Example test for usePolicyValidation:

```javascript
import { renderHook, act } from '@testing-library/react-hooks';
import usePolicyValidation from '../hooks/usePolicyValidation';

test('validates policy correctly', () => {
  const { result } = renderHook(() => usePolicyValidation());

  act(() => {
    const errors = result.current.validatePolicy({
      name: '',
      category: '',
      definition: '{}'
    });

    expect(errors.name).toBe('Policy name is required');
    expect(errors.category).toBe('Category is required');
  });
});
```

## Performance Optimization

### Memoization

Use React.memo for expensive components:

```javascript
const PolicyCard = React.memo(({ policy, onEdit, onDelete }) => {
  return (
    <div className="border rounded-lg p-4">
      <h3 className="text-lg font-semibold">{policy.name}</h3>
      <p className="text-gray-600">{policy.description}</p>
      <div className="mt-4 space-x-2">
        <button onClick={() => onEdit(policy)}>Edit</button>
        <button onClick={() => onDelete(policy.id)}>Delete</button>
      </div>
    </div>
  );
});
```

### Lazy Loading

Lazy load heavy components:

```javascript
const EnumManagement = React.lazy(() => import('./components/EnumManagement'));

function App() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <EnumManagement />
    </Suspense>
  );
}
```

### Virtual Scrolling

For large lists, use virtual scrolling:

```javascript
import { FixedSizeList as List } from 'react-window';

const PolicyList = ({ policies }) => {
  const Row = ({ index, style }) => (
    <div style={style}>
      <PolicyCard policy={policies[index]} />
    </div>
  );

  return (
    <List
      height={400}
      itemCount={policies.length}
      itemSize={100}
    >
      {Row}
    </List>
  );
};
```

## Accessibility

### ARIA Labels

All interactive elements have proper ARIA labels:

```javascript
<button
  aria-label="Edit policy"
  onClick={() => onEdit(policy)}
>
  <EditIcon />
</button>

<input
  aria-label="Policy name"
  aria-describedby="name-error"
  aria-invalid={!!errors.name}
  {...props}
/>
```

### Keyboard Navigation

Components support keyboard navigation:

```javascript
const handleKeyDown = (event) => {
  if (event.key === 'Enter') {
    handleSave();
  } else if (event.key === 'Escape') {
    onClose();
  }
};

<div onKeyDown={handleKeyDown} tabIndex={0}>
  {/* Modal content */}
</div>
```

### Screen Reader Support

Proper semantic HTML and ARIA attributes:

```javascript
<div role="dialog" aria-labelledby="modal-title" aria-describedby="modal-description">
  <h2 id="modal-title">Create New Policy</h2>
  <p id="modal-description">Fill in the form below to create a new policy.</p>
  {/* Form content */}
</div>
``` 