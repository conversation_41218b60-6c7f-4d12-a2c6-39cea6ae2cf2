# Contributing to Vitea.ai Policy Management System

## Overview

Thank you for your interest in contributing to the Vitea.ai Policy Management System! This document provides guidelines and best practices for contributing to the project.

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL 17+
- Git
- npm or yarn

### Development Setup

1. **Fork the repository**
   ```bash
   git clone https://github.com/your-username/vitea-app-deployment.git
   cd vitea-app-deployment
   ```

2. **Install dependencies**
   ```bash
   # Install root dependencies
   npm install
   
   # Install API dependencies
   cd enhanced-api-project
   npm install
   
   # Install Admin UI dependencies
   cd ../admin-ui-project
   npm install
   ```

3. **Set up the database**
   ```bash
   # Run database migrations
   psql -h your-db-host -U your-username -d your-database -f configs/enhanced-database-schema.sql
   psql -h your-db-host -U your-username -d your-database -f configs/20250716_06__policy_cloning_enhancements.sql
   psql -h your-db-host -U your-username -d your-database -f configs/20250716_07__enum_management.sql
   psql -h your-db-host -U your-username -d your-database -f configs/policy_templates.sql
   ```

4. **Configure environment variables**
   ```bash
   # Create .env file in enhanced-api-project
   cp enhanced-api-project/.env.example enhanced-api-project/.env
   # Edit the .env file with your database credentials
   ```

5. **Start development servers**
   ```bash
   # Start API server
   cd enhanced-api-project
   npm start
   
   # Start Admin UI (in new terminal)
   cd admin-ui-project
   npm start
   ```

## Development Workflow

### Branch Naming Convention

Use the following branch naming convention:

```
feature/description-of-feature
bugfix/description-of-bug
hotfix/description-of-hotfix
docs/description-of-documentation
```

Examples:
- `feature/enhanced-policy-validation`
- `bugfix/fix-json-editor-autocomplete`
- `hotfix/critical-security-patch`
- `docs/update-api-documentation`

### Commit Message Convention

Use conventional commits format:

```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

Examples:
```
feat(policy): add real-time validation for policy creation

- Add schema-based validation using AJV
- Implement debounced validation with 500ms delay
- Add visual error indicators for form fields

Closes #123
```

```
fix(api): resolve validation error in policy update endpoint

- Fix issue where validation middleware was not properly handling JSON parsing
- Add better error messages for validation failures

Fixes #456
```

### Pull Request Process

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Follow the coding standards
   - Add tests for new functionality
   - Update documentation as needed

3. **Test your changes**
   ```bash
   # Run API tests
   cd enhanced-api-project
   npm test
   
   # Run frontend tests
   cd ../admin-ui-project
   npm test
   
   # Run linting
   npm run lint
   ```

4. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat(policy): add new validation feature"
   ```

5. **Push to your fork**
   ```bash
   git push origin feature/your-feature-name
   ```

6. **Create a pull request**
   - Use the PR template
   - Describe your changes clearly
   - Link any related issues
   - Request reviews from maintainers

## Coding Standards

### JavaScript/TypeScript

#### General Guidelines

- Use ES6+ features
- Prefer `const` and `let` over `var`
- Use arrow functions for callbacks
- Use template literals for string interpolation
- Use destructuring for object/array assignment

#### Code Style

```javascript
// Good
const { name, description } = policy;
const handleSubmit = async (data) => {
  try {
    const result = await api.createPolicy(data);
    onSuccess(result);
  } catch (error) {
    onError(error.message);
  }
};

// Bad
var policyName = policy.name;
var policyDescription = policy.description;
function handleSubmit(data) {
  api.createPolicy(data).then(function(result) {
    onSuccess(result);
  }).catch(function(error) {
    onError(error.message);
  });
}
```

#### React Components

```javascript
// Functional components with hooks
const PolicyForm = ({ formData, errors, onChange, onSubmit }) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Submit error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
    </form>
  );
};

// PropTypes for type checking
PolicyForm.propTypes = {
  formData: PropTypes.object.isRequired,
  errors: PropTypes.object,
  onChange: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired
};
```

#### API Routes

```javascript
// Use async/await
router.post('/policies', validatePolicyMiddleware, async (req, res) => {
  try {
    const { name, description, category, severity, definition } = req.body;
    
    // Validate input
    if (!name || name.trim().length === 0) {
      return res.status(400).json({
        error: 'Policy name is required'
      });
    }

    // Database operation
    const result = await db.query(
      'INSERT INTO policies (name, description, category, severity, definition) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [name, description, category, severity, definition]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Create policy error:', error);
    res.status(500).json({
      error: 'Failed to create policy',
      message: error.message
    });
  }
});
```

### Database

#### SQL Guidelines

- Use prepared statements to prevent SQL injection
- Use meaningful table and column names
- Add appropriate indexes for performance
- Use transactions for multi-step operations
- Add comments for complex queries

```sql
-- Good
CREATE INDEX idx_policies_category ON policies(category);
CREATE INDEX idx_policies_created_at ON policies(created_at);

-- Use prepared statements
const result = await db.query(
  'SELECT * FROM policies WHERE category = $1 AND is_active = $2',
  [category, isActive]
);

-- Bad
const result = await db.query(
  `SELECT * FROM policies WHERE category = '${category}' AND is_active = ${isActive}`
);
```

#### Migration Guidelines

- Use descriptive migration names
- Include both up and down migrations
- Test migrations on development data
- Document breaking changes

```sql
-- Migration: 20240115_01_add_policy_cloning.sql
-- Description: Add policy cloning functionality

-- Up migration
ALTER TABLE policies ADD COLUMN original_policy_id UUID REFERENCES policies(policy_id);
ALTER TABLE policies ADD COLUMN cloned_from_policy_name VARCHAR(255);

-- Down migration
ALTER TABLE policies DROP COLUMN IF EXISTS original_policy_id;
ALTER TABLE policies DROP COLUMN IF EXISTS cloned_from_policy_name;
```

### CSS/Styling

#### Tailwind CSS Guidelines

- Use utility classes for styling
- Create custom components for repeated patterns
- Use responsive design classes
- Follow the design system

```javascript
// Good - using Tailwind utilities
<div className="bg-white shadow-lg rounded-lg p-6 hover:shadow-xl transition-shadow">
  <h2 className="text-xl font-semibold text-gray-900 mb-4">
    Policy Management
  </h2>
  <div className="space-y-4">
    {/* Content */}
  </div>
</div>

// Bad - inline styles
<div style={{ backgroundColor: 'white', boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)', borderRadius: '8px', padding: '24px' }}>
  <h2 style={{ fontSize: '20px', fontWeight: '600', color: '#111827', marginBottom: '16px' }}>
    Policy Management
  </h2>
</div>
```

## Testing

### Test Structure

```
tests/
├── unit/
│   ├── components/
│   ├── utils/
│   └── api/
├── integration/
│   ├── api/
│   └── database/
└── e2e/
    └── policy-management/
```

### Unit Tests

```javascript
// Component test
import { render, screen, fireEvent } from '@testing-library/react';
import PolicyForm from '../components/PolicyForm';

describe('PolicyForm', () => {
  test('renders all form fields', () => {
    const mockOnChange = jest.fn();
    const mockOnSubmit = jest.fn();
    
    render(
      <PolicyForm
        formData={{
          name: '',
          description: '',
          category: '',
          severity: '',
          definition: '{}'
        }}
        errors={{}}
        onChange={mockOnChange}
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.getByLabelText(/policy name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/category/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/severity/i)).toBeInTheDocument();
  });

  test('displays validation errors', () => {
    const errors = {
      name: 'Policy name is required',
      category: 'Category is required'
    };

    render(
      <PolicyForm
        formData={{}}
        errors={errors}
        onChange={jest.fn()}
        onSubmit={jest.fn()}
      />
    );

    expect(screen.getByText('Policy name is required')).toBeInTheDocument();
    expect(screen.getByText('Category is required')).toBeInTheDocument();
  });
});
```

### API Tests

```javascript
// API route test
import request from 'supertest';
import app from '../app.js';

describe('Policy API', () => {
  test('creates policy successfully', async () => {
    const policyData = {
      name: 'Test Policy',
      description: 'Test description',
      category: 'medical_privacy',
      severity: 'high',
      definition: {
        type: 'medical_privacy',
        severity: 'high',
        description: 'Test policy',
        allowed_roles: ['doctor', 'nurse'],
        protected_fields: ['diagnosis', 'medication']
      }
    };

    const response = await request(app)
      .post('/api/v1/policies')
      .set('Authorization', 'Bearer admin-token')
      .send(policyData);

    expect(response.status).toBe(201);
    expect(response.body.name).toBe('Test Policy');
  });

  test('validates policy definition', async () => {
    const invalidPolicy = {
      name: 'Test Policy',
      description: 'Test description',
      category: 'medical_privacy',
      severity: 'high',
      definition: {
        type: 'medical_privacy',
        severity: 'invalid_severity'
      }
    };

    const response = await request(app)
      .post('/api/v1/policies')
      .set('Authorization', 'Bearer admin-token')
      .send(invalidPolicy);

    expect(response.status).toBe(400);
    expect(response.body.error).toBe('Schema validation failed');
  });
});
```

### Database Tests

```javascript
// Database function test
import { db } from '../db.js';

describe('Database Functions', () => {
  test('search_policies returns correct results', async () => {
    const result = await db.query(
      'SELECT * FROM search_policies($1, $2, $3, $4, $5, $6)',
      ['medical', 'medical_privacy', 'high', true, 10, 0]
    );

    expect(result.rows).toBeDefined();
    expect(Array.isArray(result.rows)).toBe(true);
  });

  test('clone_policy creates cloned policy', async () => {
    // First create a source policy
    const sourcePolicy = await db.query(
      'INSERT INTO policies (name, description, category, severity, definition) VALUES ($1, $2, $3, $4, $5) RETURNING policy_id',
      ['Source Policy', 'Source description', 'medical_privacy', 'high', '{}']
    );

    const sourceId = sourcePolicy.rows[0].policy_id;

    // Clone the policy
    const clonedId = await db.query(
      'SELECT clone_policy($1, $2, $3)',
      [sourceId, 'Cloned Policy', 'Cloned description']
    );

    expect(clonedId.rows[0].clone_policy).toBeDefined();
  });
});
```

## Documentation

### Code Documentation

```javascript
/**
 * Validates a policy definition against its schema
 * @param {string} policyType - The type of policy (e.g., 'medical_privacy')
 * @param {object} definition - The policy definition object
 * @returns {object} Validation result with valid boolean and errors array
 */
export const validatePolicyDefinition = (policyType, definition) => {
  const schema = getSchemaForPolicyType(policyType);
  if (!schema) {
    return { valid: false, errors: ['Invalid policy type'] };
  }

  const validate = ajv.compile(schema);
  const valid = validate(definition);

  return {
    valid,
    errors: valid ? [] : formatValidationErrors(validate.errors)
  };
};
```

### API Documentation

```javascript
/**
 * @api {post} /api/v1/policies Create Policy
 * @apiName CreatePolicy
 * @apiGroup Policies
 * @apiVersion 1.0.0
 *
 * @apiHeader {String} Authorization Bearer token for authentication
 *
 * @apiBody {String} name Policy name
 * @apiBody {String} description Policy description
 * @apiBody {String} category Policy category
 * @apiBody {String} severity Policy severity level
 * @apiBody {Object} definition Policy definition JSON
 *
 * @apiSuccess {String} policy_id Unique policy identifier
 * @apiSuccess {String} name Policy name
 * @apiSuccess {String} description Policy description
 * @apiSuccess {String} category Policy category
 * @apiSuccess {String} severity Policy severity
 * @apiSuccess {Object} definition Policy definition
 * @apiSuccess {Date} created_at Creation timestamp
 *
 * @apiError {String} error Error message
 * @apiError {Array} details Validation error details
 */
router.post('/policies', validatePolicyMiddleware, async (req, res) => {
  // Implementation
});
```

## Performance Guidelines

### Frontend Performance

- Use React.memo for expensive components
- Implement proper dependency arrays in useEffect
- Use lazy loading for large components
- Optimize bundle size with code splitting

```javascript
// Good - memoized component
const PolicyCard = React.memo(({ policy, onEdit, onDelete }) => {
  return (
    <div className="border rounded-lg p-4">
      <h3 className="text-lg font-semibold">{policy.name}</h3>
      <p className="text-gray-600">{policy.description}</p>
      <div className="mt-4 space-x-2">
        <button onClick={() => onEdit(policy)}>Edit</button>
        <button onClick={() => onDelete(policy.id)}>Delete</button>
      </div>
    </div>
  );
});

// Good - proper dependency array
useEffect(() => {
  if (category) {
    loadTemplate(category);
  }
}, [category]); // Only re-run when category changes
```

### Backend Performance

- Use connection pooling for database connections
- Implement proper indexing
- Use prepared statements
- Cache frequently accessed data

```javascript
// Good - connection pooling
const pool = new Pool({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// Good - prepared statements
const result = await pool.query(
  'SELECT * FROM policies WHERE category = $1 AND is_active = $2 LIMIT $3 OFFSET $4',
  [category, isActive, limit, offset]
);
```

## Security Guidelines

### Input Validation

- Validate all user inputs
- Use parameterized queries
- Sanitize data before storage
- Implement proper authentication

```javascript
// Good - input validation
const validatePolicyInput = (data) => {
  const errors = {};
  
  if (!data.name || data.name.trim().length === 0) {
    errors.name = 'Policy name is required';
  }
  
  if (data.name && data.name.length > 255) {
    errors.name = 'Policy name must be less than 255 characters';
  }
  
  return errors;
};

// Good - parameterized query
const result = await db.query(
  'INSERT INTO policies (name, description) VALUES ($1, $2)',
  [name, description]
);
```

### Authentication & Authorization

```javascript
// Middleware for admin authentication
const requireAdmin = (req, res, next) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Authorization header required' });
  }
  
  const token = authHeader.substring(7);
  
  if (token !== 'admin-token') {
    return res.status(403).json({ error: 'Admin access required' });
  }
  
  next();
};
```

## Review Process

### Code Review Checklist

- [ ] Code follows the style guide
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No security vulnerabilities
- [ ] Performance considerations addressed
- [ ] Error handling is appropriate
- [ ] Logging is implemented where needed

### Review Guidelines

1. **Be constructive and respectful**
2. **Focus on the code, not the person**
3. **Provide specific feedback**
4. **Suggest improvements**
5. **Ask questions when unclear**

### Review Comments

```javascript
// Good review comment
// Consider using React.memo here since this component re-renders frequently
// and the props don't change often

// Good review comment
// This validation could be moved to a shared utility function
// since it's used in multiple places

// Good review comment
// Missing error handling for the database query
// Should add try-catch block
```

## Release Process

### Versioning

Use semantic versioning (MAJOR.MINOR.PATCH):

- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Checklist

- [ ] All tests passing
- [ ] Documentation updated
- [ ] Changelog updated
- [ ] Version bumped
- [ ] Release notes prepared
- [ ] Deployment tested

### Release Notes Template

```markdown
# Release v2.1.0

## New Features
- Added real-time policy validation
- Implemented policy cloning functionality
- Enhanced JSON editor with autocomplete

## Bug Fixes
- Fixed validation error in policy update endpoint
- Resolved issue with enum value display

## Breaking Changes
- None

## Migration Guide
- No migration required

## Contributors
- @username1 - Added validation system
- @username2 - Fixed enum display issues
```

## Getting Help

### Communication Channels

- **GitHub Issues**: For bug reports and feature requests
- **GitHub Discussions**: For questions and general discussion
- **Pull Requests**: For code contributions

### Issue Templates

Use the provided issue templates:
- Bug Report
- Feature Request
- Documentation Request
- Security Issue

### Asking Questions

When asking questions:
1. Provide context about your environment
2. Include error messages and stack traces
3. Describe what you've tried
4. Be specific about what you need help with

## Recognition

### Contributors

Contributors will be recognized in:
- GitHub contributors list
- Release notes
- Project documentation
- Annual contributor acknowledgments

### Contribution Levels

- **Bronze**: 1-5 contributions
- **Silver**: 6-20 contributions
- **Gold**: 21+ contributions
- **Platinum**: Significant project impact

Thank you for contributing to the Vitea.ai Policy Management System! 🚀 