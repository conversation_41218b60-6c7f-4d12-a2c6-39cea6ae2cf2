# Database Schema Documentation

## Overview

The Vitea.ai Policy Management System uses PostgreSQL as its primary database. The schema has been enhanced to support advanced policy management, audit logging, and enum management features.

## Core Tables

### Policies Table

The main table for storing policy information.

```sql
CREATE TABLE policies (
    policy_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    policy_type VARCHAR(50) NOT NULL,
    definition JSONB NOT NULL,
    applies_to_roles TEXT[],
    original_policy_id UUID REFERENCES policies(policy_id),
    cloned_from_policy_name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### Columns

| Column | Type | Description |
|--------|------|-------------|
| `policy_id` | UUID | Primary key, auto-generated |
| `name` | VARCHAR(255) | Policy name (required) |
| `description` | TEXT | Policy description |
| `category` | VARCHAR(100) | Policy category (required) |
| `severity` | VARCHAR(20) | Severity level (low, medium, high, critical) |
| `is_active` | BOOLEAN | Whether policy is active |
| `policy_type` | VARCHAR(50) | Type of policy (medical_privacy, data_privacy, etc.) |
| `definition` | JSONB | Policy definition JSON |
| `applies_to_roles` | TEXT[] | Array of roles this policy applies to |
| `original_policy_id` | UUID | Reference to original policy (for cloned policies) |
| `cloned_from_policy_name` | VARCHAR(255) | Name of policy this was cloned from |
| `created_at` | TIMESTAMP | Creation timestamp |
| `updated_at` | TIMESTAMP | Last update timestamp |
| `deleted_at` | TIMESTAMP | Soft delete timestamp |

### Policy Templates Table

Stores policy templates for different categories.

```sql
CREATE TABLE policy_templates (
    template_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    template_definition JSONB NOT NULL,
    is_system_template BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Columns

| Column | Type | Description |
|--------|------|-------------|
| `template_id` | UUID | Primary key |
| `name` | VARCHAR(255) | Template name |
| `description` | TEXT | Template description |
| `category` | VARCHAR(100) | Policy category |
| `template_definition` | JSONB | Template JSON definition |
| `is_system_template` | BOOLEAN | Whether this is a system template |
| `created_at` | TIMESTAMP | Creation timestamp |
| `updated_at` | TIMESTAMP | Last update timestamp |

### Audit Log Table

Comprehensive audit logging for HIPAA compliance.

```sql
CREATE TABLE audit_log (
    log_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255),
    username VARCHAR(255),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100) NOT NULL,
    resource_id VARCHAR(255),
    details JSONB,
    session_id VARCHAR(255),
    request_id VARCHAR(255),
    user_role VARCHAR(50),
    resource_name VARCHAR(255),
    access_level VARCHAR(50),
    data_classification VARCHAR(50),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Columns

| Column | Type | Description |
|--------|------|-------------|
| `log_id` | UUID | Primary key |
| `user_id` | VARCHAR(255) | User identifier |
| `username` | VARCHAR(255) | Username |
| `action` | VARCHAR(100) | Action performed (CREATE, UPDATE, DELETE, etc.) |
| `resource_type` | VARCHAR(100) | Type of resource (POLICY, TEMPLATE, etc.) |
| `resource_id` | VARCHAR(255) | Resource identifier |
| `details` | JSONB | Additional details about the action |
| `session_id` | VARCHAR(255) | Session identifier |
| `request_id` | VARCHAR(255) | Request identifier |
| `user_role` | VARCHAR(50) | User's role |
| `resource_name` | VARCHAR(255) | Name of the resource |
| `access_level` | VARCHAR(50) | Access level used |
| `data_classification` | VARCHAR(50) | Data classification level |
| `ip_address` | INET | IP address of the request |
| `user_agent` | TEXT | User agent string |
| `created_at` | TIMESTAMP | Timestamp of the action |

### Enum Categories Table

Categories for organizing enum values.

```sql
CREATE TABLE enum_categories (
    category_id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    policy_type VARCHAR(50) NOT NULL,
    field_path VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Columns

| Column | Type | Description |
|--------|------|-------------|
| `category_id` | SERIAL | Primary key |
| `name` | VARCHAR(255) | Category name |
| `description` | TEXT | Category description |
| `policy_type` | VARCHAR(50) | Associated policy type |
| `field_path` | VARCHAR(255) | JSON path to the field |
| `created_at` | TIMESTAMP | Creation timestamp |
| `updated_at` | TIMESTAMP | Last update timestamp |

### Enum Values Table

Individual enum values with metadata.

```sql
CREATE TABLE enum_values (
    value_id SERIAL PRIMARY KEY,
    category_id INTEGER REFERENCES enum_categories(category_id),
    value VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Columns

| Column | Type | Description |
|--------|------|-------------|
| `value_id` | SERIAL | Primary key |
| `category_id` | INTEGER | Foreign key to enum_categories |
| `value` | VARCHAR(255) | The enum value |
| `description` | TEXT | Description of the value |
| `is_active` | BOOLEAN | Whether the value is active |
| `created_at` | TIMESTAMP | Creation timestamp |
| `updated_at` | TIMESTAMP | Last update timestamp |

## Database Functions

### Search Policies Function

Advanced search function with filtering and pagination.

```sql
CREATE OR REPLACE FUNCTION search_policies(
    p_search_term VARCHAR(255),
    p_category VARCHAR(100),
    p_severity VARCHAR(20),
    p_is_active BOOLEAN,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
) RETURNS TABLE(
    policy_id UUID,
    name VARCHAR(255),
    description TEXT,
    category VARCHAR(100),
    severity VARCHAR(20),
    is_active BOOLEAN,
    policy_type VARCHAR(50),
    definition JSONB,
    applies_to_roles TEXT[],
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    total_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    WITH filtered_policies AS (
        SELECT p.*,
               COUNT(*) OVER() as total_count
        FROM policies p
        WHERE p.deleted_at IS NULL
        AND (p_search_term IS NULL OR 
             p.name ILIKE '%' || p_search_term || '%' OR 
             p.description ILIKE '%' || p_search_term || '%')
        AND (p_category IS NULL OR p.category = p_category)
        AND (p_severity IS NULL OR p.severity = p_severity)
        AND (p_is_active IS NULL OR p.is_active = p_is_active)
    )
    SELECT fp.policy_id, fp.name, fp.description, fp.category, fp.severity,
           fp.is_active, fp.policy_type, fp.definition, fp.applies_to_roles,
           fp.created_at, fp.updated_at, fp.total_count
    FROM filtered_policies fp
    ORDER BY fp.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;
```

### Clone Policy Function

Function to clone an existing policy.

```sql
CREATE OR REPLACE FUNCTION clone_policy(
    p_source_policy_id UUID,
    p_new_name VARCHAR(255),
    p_new_description TEXT
) RETURNS UUID AS $$
DECLARE
    v_new_policy_id UUID;
    v_source_policy RECORD;
BEGIN
    -- Get the source policy
    SELECT * INTO v_source_policy
    FROM policies
    WHERE policy_id = p_source_policy_id AND deleted_at IS NULL;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Source policy not found';
    END IF;
    
    -- Create the cloned policy
    INSERT INTO policies (
        name, description, category, severity, is_active,
        policy_type, definition, applies_to_roles,
        original_policy_id, cloned_from_policy_name
    ) VALUES (
        p_new_name, p_new_description, v_source_policy.category,
        v_source_policy.severity, v_source_policy.is_active,
        v_source_policy.policy_type, v_source_policy.definition,
        v_source_policy.applies_to_roles, p_source_policy_id,
        v_source_policy.name
    ) RETURNING policy_id INTO v_new_policy_id;
    
    RETURN v_new_policy_id;
END;
$$ LANGUAGE plpgsql;
```

### Get Policy Template Function

Function to retrieve policy templates by category.

```sql
CREATE OR REPLACE FUNCTION get_policy_template_by_category(
    p_category VARCHAR(100)
) RETURNS TABLE(
    template_id UUID,
    name VARCHAR(255),
    description TEXT,
    category VARCHAR(100),
    template_definition JSONB,
    is_system_template BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT pt.template_id, pt.name, pt.description, pt.category,
           pt.template_definition, pt.is_system_template,
           pt.created_at, pt.updated_at
    FROM policy_templates pt
    WHERE pt.category = p_category AND pt.is_system_template = true
    ORDER BY pt.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;
```

### Get Enum Values Function

Function to retrieve enum values for a specific policy type and field.

```sql
CREATE OR REPLACE FUNCTION get_enum_values(
    p_policy_type VARCHAR(50),
    p_field_path VARCHAR(255)
) RETURNS TABLE(
    value_id INTEGER,
    value VARCHAR(255),
    description TEXT,
    is_active BOOLEAN,
    category_id INTEGER,
    category_name VARCHAR(255),
    category_description TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT ev.value_id, ev.value, ev.description, ev.is_active,
           ev.category_id, ec.name as category_name, ec.description as category_description
    FROM enum_values ev
    JOIN enum_categories ec ON ev.category_id = ec.category_id
    WHERE ec.policy_type = p_policy_type 
    AND ec.field_path = p_field_path
    AND ev.is_active = true
    ORDER BY ev.value;
END;
$$ LANGUAGE plpgsql;
```

### Get Enum Fields Function

Function to retrieve all enum fields for a policy type.

```sql
CREATE OR REPLACE FUNCTION get_enum_fields_for_policy_type(
    p_policy_type VARCHAR(50)
) RETURNS TABLE(
    field_path VARCHAR(255),
    category_name VARCHAR(255),
    category_description TEXT,
    values JSON
) AS $$
BEGIN
    RETURN QUERY
    SELECT ec.field_path, ec.name as category_name, ec.description as category_description,
           json_agg(
               json_build_object(
                   'value', ev.value,
                   'description', ev.description
               )
           ) as values
    FROM enum_categories ec
    LEFT JOIN enum_values ev ON ec.category_id = ev.category_id AND ev.is_active = true
    WHERE ec.policy_type = p_policy_type
    GROUP BY ec.category_id, ec.field_path, ec.name, ec.description
    ORDER BY ec.field_path;
END;
$$ LANGUAGE plpgsql;
```

### Audit Log Function

Function to log HIPAA-compliant audit events.

```sql
CREATE OR REPLACE FUNCTION log_hipaa_audit_event(
    p_user_id VARCHAR(255),
    p_username VARCHAR(255),
    p_action VARCHAR(100),
    p_resource_type VARCHAR(100),
    p_resource_id VARCHAR(255),
    p_details JSONB,
    p_session_id VARCHAR(255),
    p_request_id VARCHAR(255),
    p_user_role VARCHAR(50),
    p_resource_name VARCHAR(255),
    p_access_level VARCHAR(50),
    p_data_classification VARCHAR(50),
    p_ip_address INET,
    p_user_agent TEXT
) RETURNS UUID AS $$
DECLARE
    v_log_id UUID;
BEGIN
    INSERT INTO audit_log (
        user_id, username, action, resource_type, resource_id,
        details, session_id, request_id, user_role, resource_name,
        access_level, data_classification, ip_address, user_agent
    ) VALUES (
        p_user_id, p_username, p_action, p_resource_type, p_resource_id,
        p_details, p_session_id, p_request_id, p_user_role, p_resource_name,
        p_access_level, p_data_classification, p_ip_address, p_user_agent
    ) RETURNING log_id INTO v_log_id;
    
    RETURN v_log_id;
END;
$$ LANGUAGE plpgsql;
```

## Triggers

### Updated At Triggers

Automatically update the `updated_at` timestamp when records are modified.

```sql
-- Policies table trigger
CREATE OR REPLACE FUNCTION update_policies_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER policies_updated_at
    BEFORE UPDATE ON policies
    FOR EACH ROW
    EXECUTE FUNCTION update_policies_updated_at();

-- Policy templates table trigger
CREATE OR REPLACE FUNCTION update_policy_templates_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER policy_templates_updated_at
    BEFORE UPDATE ON policy_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_policy_templates_updated_at();

-- Enum categories table trigger
CREATE OR REPLACE FUNCTION update_enum_categories_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER enum_categories_updated_at
    BEFORE UPDATE ON enum_categories
    FOR EACH ROW
    EXECUTE FUNCTION update_enum_categories_updated_at();

-- Enum values table trigger
CREATE OR REPLACE FUNCTION update_enum_values_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER enum_values_updated_at
    BEFORE UPDATE ON enum_values
    FOR EACH ROW
    EXECUTE FUNCTION update_enum_values_updated_at();
```

## Indexes

### Performance Indexes

```sql
-- Policies table indexes
CREATE INDEX idx_policies_category ON policies(category);
CREATE INDEX idx_policies_severity ON policies(severity);
CREATE INDEX idx_policies_is_active ON policies(is_active);
CREATE INDEX idx_policies_deleted_at ON policies(deleted_at);
CREATE INDEX idx_policies_created_at ON policies(created_at);
CREATE INDEX idx_policies_name_search ON policies USING gin(to_tsvector('english', name));
CREATE INDEX idx_policies_description_search ON policies USING gin(to_tsvector('english', description));

-- Policy templates table indexes
CREATE INDEX idx_policy_templates_category ON policy_templates(category);
CREATE INDEX idx_policy_templates_is_system_template ON policy_templates(is_system_template);

-- Audit log table indexes
CREATE INDEX idx_audit_log_user_id ON audit_log(user_id);
CREATE INDEX idx_audit_log_action ON audit_log(action);
CREATE INDEX idx_audit_log_resource_type ON audit_log(resource_type);
CREATE INDEX idx_audit_log_created_at ON audit_log(created_at);
CREATE INDEX idx_audit_log_session_id ON audit_log(session_id);

-- Enum categories table indexes
CREATE INDEX idx_enum_categories_policy_type ON enum_categories(policy_type);
CREATE INDEX idx_enum_categories_field_path ON enum_categories(field_path);

-- Enum values table indexes
CREATE INDEX idx_enum_values_category_id ON enum_values(category_id);
CREATE INDEX idx_enum_values_is_active ON enum_values(is_active);
CREATE INDEX idx_enum_values_value ON enum_values(value);
```

## Constraints

### Data Integrity Constraints

```sql
-- Policies table constraints
ALTER TABLE policies ADD CONSTRAINT policies_severity_check 
    CHECK (severity IN ('low', 'medium', 'high', 'critical'));

ALTER TABLE policies ADD CONSTRAINT policies_category_check 
    CHECK (category IN ('medical_privacy', 'data_privacy', 'access_control', 'compliance'));

-- Enum values table constraints
ALTER TABLE enum_values ADD CONSTRAINT enum_values_value_unique 
    UNIQUE (category_id, value);

-- Audit log table constraints
ALTER TABLE audit_log ADD CONSTRAINT audit_log_action_check 
    CHECK (action IN ('CREATE', 'UPDATE', 'DELETE', 'READ', 'CLONE'));
```

## Sample Data

### Initial Policy Templates

```sql
-- Medical Privacy Template
INSERT INTO policy_templates (name, description, category, template_definition, is_system_template) VALUES
(
    'Medical Privacy Template',
    'HIPAA-compliant medical data protection template',
    'medical_privacy',
    '{
        "type": "medical_privacy",
        "severity": "high",
        "description": "HIPAA-compliant medical data protection",
        "enabled": true,
        "allowed_roles": ["doctor", "nurse", "admin"],
        "protected_fields": ["diagnosis", "medication", "lab_orders"],
        "hipaa_compliance": true,
        "audit_requirements": {
            "retention_period": 7,
            "log_access_events": true,
            "encrypt_data": true
        },
        "data_handling": {
            "encryption_required": true,
            "access_logging": true,
            "data_masking": true
        }
    }',
    true
);
```

### Initial Enum Data

```sql
-- Medical Roles Category
INSERT INTO enum_categories (name, description, policy_type, field_path) VALUES
('Medical Roles', 'Medical staff roles', 'medical_privacy', 'allowed_roles');

-- Medical Roles Values
INSERT INTO enum_values (category_id, value, description, is_active) VALUES
(1, 'doctor', 'Medical doctor role', true),
(1, 'nurse', 'Nursing staff role', true),
(1, 'admin', 'Administrative staff role', true),
(1, 'pharmacist', 'Pharmacy staff role', true),
(1, 'lab_tech', 'Laboratory technician role', true),
(1, 'specialist', 'Medical specialist role', true),
(1, 'resident', 'Medical resident role', true);
```

## Migration Scripts

### Policy Cloning Enhancements

File: `configs/20250716_06__policy_cloning_enhancements.sql`

- Adds `original_policy_id` and `cloned_from_policy_name` columns to policies table
- Enhances audit_log table with HIPAA compliance fields
- Creates `log_hipaa_audit_event` function
- Creates `get_policy_template_by_category` function
- Creates `clone_policy` function
- Creates `search_policies` function

### Enum Management

File: `configs/20250716_07__enum_management.sql`

- Creates `enum_categories` table
- Creates `enum_values` table
- Creates `get_enum_values` function
- Creates `get_enum_fields_for_policy_type` function
- Inserts initial enum data

## Backup and Recovery

### Backup Strategy

```bash
# Full database backup
pg_dump -h your-db-host -U your-username -d your-database > backup_$(date +%Y%m%d_%H%M%S).sql

# Schema-only backup
pg_dump -h your-db-host -U your-username -d your-database --schema-only > schema_backup_$(date +%Y%m%d_%H%M%S).sql

# Data-only backup
pg_dump -h your-db-host -U your-username -d your-database --data-only > data_backup_$(date +%Y%m%d_%H%M%S).sql
```

### Recovery Strategy

```bash
# Restore from backup
psql -h your-db-host -U your-username -d your-database < backup_20240115_103000.sql

# Restore schema only
psql -h your-db-host -U your-username -d your-database < schema_backup_20240115_103000.sql

# Restore data only
psql -h your-db-host -U your-username -d your-database < data_backup_20240115_103000.sql
```

## Performance Optimization

### Query Optimization

1. **Use indexes**: All frequently queried columns have indexes
2. **Limit results**: Use LIMIT and OFFSET for pagination
3. **Avoid SELECT ***: Only select needed columns
4. **Use prepared statements**: For repeated queries
5. **Monitor slow queries**: Use PostgreSQL's slow query log

### Maintenance

```sql
-- Analyze tables for query optimization
ANALYZE policies;
ANALYZE policy_templates;
ANALYZE audit_log;
ANALYZE enum_categories;
ANALYZE enum_values;

-- Vacuum tables to reclaim space
VACUUM ANALYZE policies;
VACUUM ANALYZE policy_templates;
VACUUM ANALYZE audit_log;
VACUUM ANALYZE enum_categories;
VACUUM ANALYZE enum_values;
```

## Security Considerations

### Data Protection

1. **Encryption**: All sensitive data should be encrypted at rest
2. **Access Control**: Use role-based access control
3. **Audit Logging**: All data access is logged
4. **Input Validation**: All inputs are validated
5. **SQL Injection Prevention**: Use parameterized queries

### Compliance

1. **HIPAA Compliance**: Audit logging meets HIPAA requirements
2. **Data Retention**: Policies define data retention periods
3. **Access Controls**: Role-based access control implemented
4. **Audit Trail**: Complete audit trail for all operations 