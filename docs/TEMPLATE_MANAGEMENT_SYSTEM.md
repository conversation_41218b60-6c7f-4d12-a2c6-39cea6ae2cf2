# Template Management System Documentation

## Overview

The Vitea.ai Policy Management System has migrated from a static `policy_templates` table approach to a dynamic, schema-driven template management system. Templates are now managed as part of the `policy_schemas` table, providing auto-generation capabilities, manual override support, and seamless integration with external schema management systems.

## Architecture

### Database Structure

```sql
-- policy_schemas table with template support
CREATE TABLE policy_schemas (
    id UUID PRIMARY KEY,
    schema_name VARCHAR(255) NOT NULL UNIQUE,
    schema_content JSONB NOT NULL,          -- JSON Schema definition
    default_template JSONB,                 -- Default template values
    template_source VARCHAR(20),            -- Source of template
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### Template Sources

- **`auto_generated`** - Template automatically generated from JSON Schema
- **`manual_override`** - Administrator manually customized template
- **`external_provided`** - Provided by external system with schema
- **`migrated_legacy`** - Migrated from old policy_templates table

## API Endpoints

### Get Template
```http
GET /api/v1/schemas/:name/template
```

Returns the default template for a schema. Resolution order:
1. Database template (manual or external)
2. Auto-generated from schema
3. Hardcoded fallback (temporary)

**Response:**
```json
{
  "schema_name": "medical_privacy",
  "template": {
    "type": "medical_privacy",
    "severity": "medium",
    "allowed_roles": ["doctor"],
    "hipaa_compliance": true,
    "protected_fields": ["diagnosis"]
  },
  "source": "database"
}
```

### Update Template (Manual Override)
```http
PUT /api/v1/schemas/:name/template
Authorization: admin
Content-Type: application/json

{
  "template": {
    "type": "medical_privacy",
    "severity": "high",
    "custom_field": "custom_value"
  },
  "source": "manual_override"
}
```

### Reset Template to Auto-Generated
```http
DELETE /api/v1/schemas/:name/template
Authorization: admin
```

Removes manual override and regenerates template from schema.

### Regenerate All Templates
```http
POST /api/v1/schemas/regenerate-templates?overrideManual=false
Authorization: admin
```

Batch regeneration of all auto-generated templates. Set `overrideManual=true` to override manual templates.

### Get Template Status
```http
GET /api/v1/schemas/templates/status
Authorization: admin
```

Returns status of all schema templates:
```json
{
  "summary": {
    "total": 4,
    "with_templates": 4,
    "without_templates": 0,
    "by_source": {
      "auto_generated": 3,
      "manual_override": 1
    }
  },
  "schemas": [...]
}
```

## Template Auto-Generation Algorithm

The system automatically generates templates from JSON Schema definitions using the following priority:

1. **Explicit `default` value** - Uses schema-defined default
2. **`const` value** - Uses fixed constant value
3. **First `enum` value** - Uses first enumeration option
4. **Nested objects** - Recursively generates for nested structures
5. **Type-specific defaults** - Falls back to type defaults (empty string, 0, false, [], {})

### Example Schema to Template Generation

**Input Schema:**
```json
{
  "type": "object",
  "properties": {
    "severity": {
      "type": "string",
      "enum": ["low", "medium", "high"],
      "default": "medium"
    },
    "enabled": {
      "type": "boolean",
      "default": true
    },
    "allowed_roles": {
      "type": "array",
      "items": {
        "type": "string",
        "enum": ["admin", "user", "viewer"]
      },
      "minItems": 1
    }
  },
  "required": ["severity", "allowed_roles"]
}
```

**Generated Template:**
```json
{
  "severity": "medium",
  "enabled": true,
  "allowed_roles": ["admin"]
}
```

## Integration with External Systems

### Schema Update Workflow

When an external system updates a schema:

1. **Schema Update API Called**
   ```http
   PUT /api/v1/schemas/:name
   {
     "schema_content": {...},
     "description": "Updated schema"
   }
   ```

2. **Template Generation Hook Triggered**
   - System checks current `template_source`
   - If `manual_override`, template is preserved
   - Otherwise, template is regenerated

3. **Template Stored**
   - New template saved with `template_source = 'auto_generated'`
   - Cache cleared for immediate availability

### External System Providing Templates

External systems can provide templates with schemas:

```http
PUT /api/v1/schemas/:name
{
  "schema_content": {...},
  "default_template": {...},
  "template_source": "external_provided"
}
```

## Migration Guide

### From policy_templates Table

1. **Run Migration Script**
   ```bash
   psql -U dbadmin -d vitea_db -f scripts/migrate-templates-to-schemas.sql
   ```

2. **Verify Migration**
   ```sql
   SELECT schema_name, template_source, 
          default_template IS NOT NULL as has_template
   FROM policy_schemas
   WHERE is_active = true;
   ```

3. **Update Application Code**
   - Replace calls to `/api/v1/policies/templates` with `/api/v1/schemas/:name/template`
   - Update `generateDefaultPolicy()` to use async pattern

4. **Drop Old Table** (after verification)
   ```sql
   DROP TABLE IF EXISTS policy_templates CASCADE;
   ```

### Code Changes Required

**Backend:**
```javascript
// Old
const template = generateDefaultPolicy(policyType);

// New (async)
const template = await generateDefaultPolicy(policyType);
```

**Frontend:**
```javascript
// Old - hardcoded templates
const template = getHardcodedTemplate(policyType);

// New - API call
const template = await fetchSchemaTemplate(policyType);
```

## Template Management Service

### Key Features

- **Auto-generation** from JSON Schemas
- **Manual override** preservation
- **Caching** for performance (5-minute TTL)
- **Fallback chain** for reliability
- **Audit trail** via `template_source`

### Service Methods

```javascript
// Get template with fallback chain
const template = await templateGenerationService.getTemplate(schemaName);

// Update template manually
await templateGenerationService.updateTemplate(schemaName, template, 'manual_override');

// Reset to auto-generated
await templateGenerationService.resetTemplate(schemaName);

// Handle schema update from external system
await templateGenerationService.handleSchemaUpdate(schemaName, schemaContent);

// Regenerate all templates
await templateGenerationService.regenerateAllTemplates(overrideManual);
```

## Database Functions

### Generate Template from Schema (PL/pgSQL)

```sql
SELECT generate_default_template(schema_content) 
FROM policy_schemas 
WHERE schema_name = 'medical_privacy';
```

This function:
- Extracts defaults, const values, and enum first values
- Handles nested objects recursively
- Respects required fields
- Returns JSONB template

## Monitoring and Debugging

### Check Template Status
```sql
SELECT 
    schema_name,
    template_source,
    default_template IS NOT NULL as has_template,
    updated_at
FROM policy_schemas
WHERE is_active = true
ORDER BY schema_name;
```

### View Template Generation Log
```sql
SELECT * FROM audit_log
WHERE action = 'TEMPLATE_GENERATED'
ORDER BY created_at DESC
LIMIT 10;
```

### Debug Template Generation
```javascript
// Enable debug logging
templateGenerationService.debug = true;

// Test generation
const template = templateGenerationService.generateFromSchema(schema);
console.log('Generated:', template);
```

## Best Practices

1. **Preserve Manual Overrides** - System automatically preserves `manual_override` templates during schema updates

2. **Use Appropriate Template Source**
   - `auto_generated` for standard schemas
   - `manual_override` for custom business logic
   - `external_provided` when external system manages templates

3. **Cache Management** - Templates cached for 5 minutes; clear cache after updates

4. **Validation** - Always validate generated templates against schema before use

5. **Audit Trail** - Track template changes via `template_source` and `updated_at`

## Troubleshooting

### Template Not Generated

**Problem:** Schema exists but no template generated

**Solution:**
```sql
-- Check schema exists and is active
SELECT * FROM policy_schemas WHERE schema_name = 'your_schema';

-- Manually trigger generation
UPDATE policy_schemas 
SET default_template = generate_default_template(schema_content)
WHERE schema_name = 'your_schema';
```

### Template Override Not Working

**Problem:** Manual template reverts to auto-generated

**Solution:**
```http
PUT /api/v1/schemas/:name/template
{
  "template": {...},
  "source": "manual_override"  // Must specify source
}
```

### Old Endpoints Still Used

**Problem:** Deprecation warnings in logs

**Solution:** Update to new endpoints:
- `/api/v1/policies/templates` → `/api/v1/schemas/templates/status`
- `/api/v1/policies/templates/:category` → `/api/v1/schemas/:name/template`

## Security Considerations

1. **Admin-Only Template Management** - PUT/DELETE endpoints require admin authorization
2. **Template Validation** - Templates validated against schema before storage
3. **Audit Logging** - All template changes logged with user attribution
4. **No Direct SQL Access** - Templates managed through API only

## Performance Optimization

1. **Caching** - 5-minute in-memory cache for templates
2. **Indexes** - Database indexes on `schema_name` and `template_source`
3. **Lazy Generation** - Templates generated on-demand, not preemptively
4. **Batch Operations** - Bulk regeneration available for maintenance

## Future Enhancements

1. **Template Versioning** - Track template history and allow rollback
2. **Template Inheritance** - Base templates with overrides
3. **Template Variables** - Dynamic values resolved at runtime
4. **Template Validation Rules** - Custom business logic validation
5. **Template UI Editor** - Visual template management interface

## Support

For issues or questions regarding the template management system:

1. Check this documentation
2. Review API endpoint responses for error details
3. Check audit logs for template operations
4. Contact the development team with specific error messages

## Appendix: Deprecated Features

### policy_templates Table (Removed)

The old `policy_templates` table has been removed. All template data has been migrated to `policy_schemas.default_template`.

### Legacy Endpoints (Deprecated)

- `GET /api/v1/policies/templates` - Returns deprecation warning
- `GET /api/v1/policies/templates/:category` - Returns deprecation warning

These endpoints remain functional during transition but will be removed in future versions.