# Validation System Documentation

## Overview

The Vitea.ai Policy Management System implements a comprehensive validation system that ensures data integrity, provides real-time feedback, and maintains compliance with business rules. The system uses JSON schemas for validation and provides both frontend and backend validation.

## Architecture

### Validation Layers

1. **Frontend Validation**: Real-time validation using AJV
2. **Backend Validation**: Server-side validation before database operations
3. **Schema Validation**: JSON schema-based validation
4. **Business Rule Validation**: Custom business logic validation

### Components

- **AJV (Another JSON Schema Validator)**: JavaScript schema validator
- **JSON Schemas**: Policy definition schemas
- **Validation Middleware**: Express.js middleware for API validation
- **Real-time Validation**: Frontend validation with debouncing

## JSON Schemas

### Schema Structure

All policy schemas are defined in `policy_schemas.json`:

```json
{
  "definitions": {
    "basePolicy": {
      "type": "object",
      "properties": {
        "type": { "type": "string" },
        "severity": { 
          "type": "string", 
          "enum": ["low", "medium", "high", "critical"] 
        },
        "description": { "type": "string" },
        "enabled": { "type": "boolean", "default": true }
      },
      "required": ["type", "severity", "description"],
      "additionalProperties": false
    }
  },
  "medical_privacy": {
    "type": "object",
    "title": "Medical Privacy Policy",
    "description": "Template for HIPAA-compliant medical privacy policies",
    "properties": {
      "type": { "type": "string", "const": "medical_privacy" },
      "severity": { "type": "string", "enum": ["low", "medium", "high", "critical"] },
      "description": { "type": "string" },
      "enabled": { "type": "boolean", "default": true },
      "allowed_roles": {
        "type": "array",
        "items": { "type": "string" },
        "minItems": 1
      },
      "protected_fields": {
        "type": "array",
        "items": { "type": "string" },
        "minItems": 1
      },
      "hipaa_compliance": { "type": "boolean", "default": true },
      "audit_requirements": {
        "type": "object",
        "properties": {
          "retention_period": { "type": "integer", "minimum": 1, "maximum": 10 },
          "log_access_events": { "type": "boolean" },
          "encrypt_data": { "type": "boolean" }
        },
        "required": ["retention_period", "log_access_events", "encrypt_data"]
      },
      "data_handling": {
        "type": "object",
        "properties": {
          "encryption_required": { "type": "boolean" },
          "access_logging": { "type": "boolean" },
          "data_masking": { "type": "boolean" }
        },
        "required": ["encryption_required", "access_logging", "data_masking"]
      }
    },
    "required": ["type", "severity", "allowed_roles", "protected_fields"],
    "additionalProperties": false
  }
}
```

### Supported Policy Types

1. **Medical Privacy** (`medical_privacy`)
   - HIPAA compliance requirements
   - Medical role restrictions
   - Protected health information fields

2. **Data Privacy** (`data_privacy`)
   - General data protection
   - Privacy level classifications
   - Consent requirements

3. **Access Control** (`access_control`)
   - Role-based access control
   - Resource type restrictions
   - Time and location restrictions

4. **Compliance** (`compliance`)
   - Regulatory compliance frameworks
   - Audit frequency requirements
   - Reporting requirements

## Frontend Validation

### Schema Utilities

**Location**: `admin-ui-project/src/utils/schemaUtils.js`

```javascript
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import policySchemas from '../policy_schemas.json';

const ajv = new Ajv({ allErrors: true });
addFormats(ajv);

// Get schema for specific policy type
export const getSchemaForPolicyType = (policyType) => {
  return policySchemas[policyType];
};

// Validate policy definition against schema
export const validatePolicyDefinition = (policyType, definition) => {
  const schema = getSchemaForPolicyType(policyType);
  if (!schema) {
    return { valid: false, errors: ['Invalid policy type'] };
  }

  const completeSchema = {
    ...schema,
    definitions: policySchemas.definitions
  };

  const validate = ajv.compile(completeSchema);
  const valid = validate(definition);

  return {
    valid,
    errors: valid ? [] : formatValidationErrors(validate.errors)
  };
};

// Generate default policy based on schema
export const generateDefaultPolicy = (policyType) => {
  switch (policyType) {
    case 'medical_privacy':
      return {
        type: 'medical_privacy',
        severity: 'medium',
        description: 'HIPAA-compliant medical data protection',
        enabled: true,
        allowed_roles: ['doctor', 'nurse', 'admin'],
        protected_fields: ['diagnosis', 'medication', 'lab_orders'],
        hipaa_compliance: true,
        audit_requirements: {
          retention_period: 7,
          log_access_events: true,
          encrypt_data: true
        },
        data_handling: {
          encryption_required: true,
          access_logging: true,
          data_masking: true
        }
      };
    // Add other policy types...
    default:
      return { type: policyType, severity: 'medium', enabled: true };
  }
};
```

### Real-time Validation

**Location**: `admin-ui-project/src/EnhancedPolicyModal.js`

```javascript
import { useDebounce } from '../hooks/useDebounce';
import { validatePolicyDefinition } from '../utils/schemaUtils';

const EnhancedPolicyModal = ({ isOpen, onClose, existingPolicy, onSave }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    severity: '',
    definition: '{\n \n}'
  });

  const [errors, setErrors] = useState({});
  const [isValid, setIsValid] = useState(false);

  // Debounced validation
  const debouncedValidation = useDebounce((data) => {
    const validationErrors = {};

    // Validate required fields
    if (!data.name.trim()) {
      validationErrors.name = 'Policy name is required';
    }

    if (!data.description.trim()) {
      validationErrors.description = 'Description is required';
    }

    if (!data.category) {
      validationErrors.category = 'Category is required';
    }

    if (!data.severity) {
      validationErrors.severity = 'Severity is required';
    }

    // Validate JSON definition
    try {
      const definition = JSON.parse(data.definition);
      const validation = validatePolicyDefinition(data.category, definition);
      
      if (!validation.valid) {
        validationErrors.definition = validation.errors.join(', ');
      }
    } catch (error) {
      validationErrors.definition = 'Invalid JSON format';
    }

    setErrors(validationErrors);
    setIsValid(Object.keys(validationErrors).length === 0);
  }, 500);

  // Trigger validation on form changes
  useEffect(() => {
    debouncedValidation(formData);
  }, [formData, debouncedValidation]);

  const handleChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <form onSubmit={handleSubmit}>
        <FormField
          label="Policy Name"
          name="name"
          value={formData.name}
          onChange={(value) => handleChange('name', value)}
          error={errors.name}
          required
        />
        
        <FormField
          label="Description"
          name="description"
          value={formData.description}
          onChange={(value) => handleChange('description', value)}
          error={errors.description}
          required
        />

        <SelectField
          label="Category"
          name="category"
          value={formData.category}
          options={categoryOptions}
          onChange={(value) => handleChange('category', value)}
          error={errors.category}
          required
        />

        <SelectField
          label="Severity"
          name="severity"
          value={formData.severity}
          options={severityOptions}
          onChange={(value) => handleChange('severity', value)}
          error={errors.severity}
          required
        />

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Policy Definition (JSON)
          </label>
          <JsonEditorWithAutocomplete
            value={formData.definition}
            onChange={(value) => handleChange('definition', value)}
            policyType={formData.category}
            onValidation={(errors) => setErrors(prev => ({ ...prev, definition: errors }))}
          />
          {errors.definition && (
            <p className="text-red-600 text-sm mt-1">{errors.definition}</p>
          )}
        </div>

        <div className="flex justify-end space-x-2">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={!isValid || isLoading}
            className={`px-4 py-2 rounded-md text-white ${
              isValid && !isLoading
                ? 'bg-blue-600 hover:bg-blue-700'
                : 'bg-gray-400 cursor-not-allowed'
            }`}
          >
            {isLoading ? 'Saving...' : 'Save Policy'}
          </button>
        </div>
      </form>
    </Modal>
  );
};
```

## Backend Validation

### Validation Middleware

**Location**: `enhanced-api-project/src/utils/schemaValidator.js`

```javascript
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import fs from 'fs';
import path from 'path';

const ajv = new Ajv({ allErrors: true });
addFormats(ajv);

// Load policy schemas
const policySchemasPath = path.join(__dirname, '../../configs/policy_schemas.json');
const policySchemas = JSON.parse(fs.readFileSync(policySchemasPath, 'utf8'));

// Get schema for specific policy type
export const getSchemaForPolicyType = (policyType) => {
  return policySchemas[policyType];
};

// Validate policy definition
export const validatePolicyDefinition = (policyType, definition) => {
  const schema = getSchemaForPolicyType(policyType);
  if (!schema) {
    return { valid: false, errors: ['Invalid policy type'] };
  }

  const completeSchema = {
    ...schema,
    definitions: policySchemas.definitions
  };

  const validate = ajv.compile(completeSchema);
  const valid = validate(definition);

  return {
    valid,
    errors: valid ? [] : formatValidationErrors(validate.errors)
  };
};

// Format validation errors
export const formatValidationErrors = (errors) => {
  return errors.map(error => {
    const field = error.instancePath.replace('/', '').replace(/\//g, '.');
    return `${field || 'root'}: ${error.message}`;
  });
};

// Express middleware for policy validation
export const validatePolicyMiddleware = (req, res, next) => {
  try {
    const { definition, category } = req.body;
    
    if (!definition) {
      return res.status(400).json({
        error: 'Policy definition is required'
      });
    }

    if (!category) {
      return res.status(400).json({
        error: 'Policy category is required'
      });
    }

    // Parse JSON if it's a string
    let parsedDefinition;
    try {
      parsedDefinition = typeof definition === 'string' 
        ? JSON.parse(definition) 
        : definition;
    } catch (error) {
      return res.status(400).json({
        error: 'Invalid JSON format in policy definition'
      });
    }

    // Validate against schema
    const validation = validatePolicyDefinition(category, parsedDefinition);
    
    if (!validation.valid) {
      return res.status(400).json({
        error: 'Schema validation failed',
        details: validation.errors
      });
    }

    // Update request body with parsed definition
    req.body.definition = parsedDefinition;
    next();
  } catch (error) {
    console.error('Validation middleware error:', error);
    return res.status(500).json({
      error: 'Validation error',
      message: error.message
    });
  }
};
```

### API Route Integration

**Location**: `enhanced-api-project/src/api/policies.js`

```javascript
import { validatePolicyMiddleware } from '../utils/schemaValidator.js';

// Apply validation middleware to create and update routes
router.post('/', validatePolicyMiddleware, async (req, res) => {
  try {
    const { name, description, category, severity, definition, applies_to_roles } = req.body;

    // Additional business rule validation
    if (!name || name.trim().length === 0) {
      return res.status(400).json({
        error: 'Policy name is required'
      });
    }

    if (name.length > 255) {
      return res.status(400).json({
        error: 'Policy name must be less than 255 characters'
      });
    }

    // Check for duplicate policy names
    const existingPolicy = await db.query(
      'SELECT policy_id FROM policies WHERE name = $1 AND deleted_at IS NULL',
      [name]
    );

    if (existingPolicy.rows.length > 0) {
      return res.status(400).json({
        error: 'Policy with this name already exists'
      });
    }

    // Create policy
    const result = await db.query(
      `INSERT INTO policies (name, description, category, severity, policy_type, definition, applies_to_roles)
       VALUES ($1, $2, $3, $4, $5, $6, $7)
       RETURNING *`,
      [name, description, category, severity, category, definition, applies_to_roles]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Create policy error:', error);
    res.status(500).json({
      error: 'Failed to create policy',
      message: error.message
    });
  }
});

router.put('/:id', validatePolicyMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, category, severity, definition, applies_to_roles } = req.body;

    // Check if policy exists
    const existingPolicy = await db.query(
      'SELECT * FROM policies WHERE policy_id = $1 AND deleted_at IS NULL',
      [id]
    );

    if (existingPolicy.rows.length === 0) {
      return res.status(404).json({
        error: 'Policy not found'
      });
    }

    // Check for duplicate names (excluding current policy)
    const duplicatePolicy = await db.query(
      'SELECT policy_id FROM policies WHERE name = $1 AND policy_id != $2 AND deleted_at IS NULL',
      [name, id]
    );

    if (duplicatePolicy.rows.length > 0) {
      return res.status(400).json({
        error: 'Policy with this name already exists'
      });
    }

    // Update policy
    const result = await db.query(
      `UPDATE policies 
       SET name = $1, description = $2, category = $3, severity = $4, 
           policy_type = $5, definition = $6, applies_to_roles = $7, updated_at = NOW()
       WHERE policy_id = $8 AND deleted_at IS NULL
       RETURNING *`,
      [name, description, category, severity, category, definition, applies_to_roles, id]
    );

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Update policy error:', error);
    res.status(500).json({
      error: 'Failed to update policy',
      message: error.message
    });
  }
});
```

## Error Handling

### Validation Error Format

```javascript
// Frontend error format
const errors = {
  name: 'Policy name is required',
  category: 'Category is required',
  definition: 'severity: must be one of: low, medium, high, critical'
};

// Backend error format
{
  error: 'Schema validation failed',
  details: [
    'severity: must be one of: low, medium, high, critical',
    'allowed_roles: must be an array',
    'protected_fields: must have at least 1 items'
  ]
}
```

### Error Display Components

```javascript
// Error message component
const ErrorMessage = ({ error, field }) => {
  if (!error) return null;
  
  return (
    <p className="text-red-600 text-sm mt-1">
      {error}
    </p>
  );
};

// Field with error display
const FormField = ({ label, name, value, onChange, error, required }) => {
  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <input
        type="text"
        name={name}
        value={value}
        onChange={(e) => onChange(name, e.target.value)}
        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
          error 
            ? 'border-red-500 focus:ring-red-500' 
            : 'border-gray-300 focus:ring-blue-500'
        }`}
      />
      <ErrorMessage error={error} />
    </div>
  );
};
```

## Testing

### Frontend Validation Tests

```javascript
import { validatePolicyDefinition } from '../utils/schemaUtils';

describe('Policy Validation', () => {
  test('validates medical privacy policy correctly', () => {
    const validPolicy = {
      type: 'medical_privacy',
      severity: 'high',
      description: 'HIPAA-compliant medical data protection',
      enabled: true,
      allowed_roles: ['doctor', 'nurse', 'admin'],
      protected_fields: ['diagnosis', 'medication', 'lab_orders'],
      hipaa_compliance: true,
      audit_requirements: {
        retention_period: 7,
        log_access_events: true,
        encrypt_data: true
      },
      data_handling: {
        encryption_required: true,
        access_logging: true,
        data_masking: true
      }
    };

    const validation = validatePolicyDefinition('medical_privacy', validPolicy);
    expect(validation.valid).toBe(true);
    expect(validation.errors).toEqual([]);
  });

  test('detects validation errors', () => {
    const invalidPolicy = {
      type: 'medical_privacy',
      severity: 'invalid_severity',
      description: 'Test policy',
      allowed_roles: 'doctor', // Should be array
      protected_fields: [] // Should have at least 1 item
    };

    const validation = validatePolicyDefinition('medical_privacy', invalidPolicy);
    expect(validation.valid).toBe(false);
    expect(validation.errors.length).toBeGreaterThan(0);
  });
});
```

### Backend Validation Tests

```javascript
import request from 'supertest';
import app from '../app.js';

describe('Policy API Validation', () => {
  test('rejects invalid policy definition', async () => {
    const invalidPolicy = {
      name: 'Test Policy',
      description: 'Test description',
      category: 'medical_privacy',
      severity: 'high',
      definition: {
        type: 'medical_privacy',
        severity: 'invalid_severity',
        description: 'Test policy',
        allowed_roles: 'doctor' // Should be array
      }
    };

    const response = await request(app)
      .post('/api/v1/policies')
      .set('Authorization', 'Bearer admin-token')
      .send(invalidPolicy);

    expect(response.status).toBe(400);
    expect(response.body.error).toBe('Schema validation failed');
    expect(response.body.details).toBeDefined();
  });

  test('accepts valid policy definition', async () => {
    const validPolicy = {
      name: 'Valid Policy',
      description: 'Valid description',
      category: 'medical_privacy',
      severity: 'high',
      definition: {
        type: 'medical_privacy',
        severity: 'high',
        description: 'Valid policy',
        enabled: true,
        allowed_roles: ['doctor', 'nurse'],
        protected_fields: ['diagnosis', 'medication'],
        hipaa_compliance: true,
        audit_requirements: {
          retention_period: 7,
          log_access_events: true,
          encrypt_data: true
        },
        data_handling: {
          encryption_required: true,
          access_logging: true,
          data_masking: true
        }
      }
    };

    const response = await request(app)
      .post('/api/v1/policies')
      .set('Authorization', 'Bearer admin-token')
      .send(validPolicy);

    expect(response.status).toBe(201);
    expect(response.body.name).toBe('Valid Policy');
  });
});
```

## Performance Optimization

### Debounced Validation

```javascript
import { useDebounce } from '../hooks/useDebounce';

const usePolicyValidation = (formData) => {
  const [errors, setErrors] = useState({});
  const [isValid, setIsValid] = useState(false);

  const debouncedValidation = useDebounce((data) => {
    const validationErrors = validateForm(data);
    setErrors(validationErrors);
    setIsValid(Object.keys(validationErrors).length === 0);
  }, 500);

  useEffect(() => {
    debouncedValidation(formData);
  }, [formData, debouncedValidation]);

  return { errors, isValid };
};
```

### Schema Caching

```javascript
// Cache compiled schemas for performance
const schemaCache = new Map();

export const getCompiledSchema = (policyType) => {
  if (schemaCache.has(policyType)) {
    return schemaCache.get(policyType);
  }

  const schema = getSchemaForPolicyType(policyType);
  if (!schema) return null;

  const completeSchema = {
    ...schema,
    definitions: policySchemas.definitions
  };

  const compiled = ajv.compile(completeSchema);
  schemaCache.set(policyType, compiled);
  
  return compiled;
};
```

## Monitoring and Logging

### Validation Metrics

```javascript
// Track validation success/failure rates
const validationMetrics = {
  totalValidations: 0,
  successfulValidations: 0,
  failedValidations: 0,
  validationErrors: {}
};

export const trackValidation = (policyType, success, errors = []) => {
  validationMetrics.totalValidations++;
  
  if (success) {
    validationMetrics.successfulValidations++;
  } else {
    validationMetrics.failedValidations++;
    
    // Track error types
    errors.forEach(error => {
      const errorType = error.split(':')[0];
      validationMetrics.validationErrors[errorType] = 
        (validationMetrics.validationErrors[errorType] || 0) + 1;
    });
  }
};
```

### Error Logging

```javascript
import winston from 'winston';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'validation-errors.log' })
  ]
});

export const logValidationError = (policyType, errors, userData) => {
  logger.error('Validation failed', {
    policyType,
    errors,
    userData,
    timestamp: new Date().toISOString()
  });
};
```

## Best Practices

### Schema Design

1. **Use descriptive property names**
2. **Provide meaningful error messages**
3. **Include examples in schema descriptions**
4. **Use consistent data types**
5. **Validate at both frontend and backend**

### Error Handling

1. **Provide specific error messages**
2. **Group related errors**
3. **Show errors near the relevant fields**
4. **Use consistent error formatting**
5. **Log validation errors for debugging**

### Performance

1. **Debounce validation calls**
2. **Cache compiled schemas**
3. **Use efficient validation libraries**
4. **Minimize validation frequency**
5. **Optimize schema structure**

### Security

1. **Validate all inputs**
2. **Sanitize user data**
3. **Use parameterized queries**
4. **Log security-related events**
5. **Implement rate limiting** 