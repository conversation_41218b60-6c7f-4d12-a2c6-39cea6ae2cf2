# API Master Directory

## Document Control

| Field | Value |
|-------|--------|
| **Purpose** | Comprehensive catalog of all REST API endpoints in the Vitea Policy Management System |
| **Author** | <PERSON> Code Assistant |
| **Created Date** | 2025-08-31 |
| **Created By** | System Documentation Generator |
| **Last Modified** | 2025-08-31 |
| **Modified By** | <PERSON> Code Assistant |
| **Version** | 1.0.0 |
| **Status** | Active |
| **Review Required** | Yes |
| **API Version** | v1 |
| **Base URL** | `http://localhost:8001/api/v1` |
| **Environment** | Development |
| **Total Endpoints** | 200+ endpoints across 14 API modules |
| **Authentication** | Bearer Token + OAuth2 Client Credentials |
| **Last Updated** | Policy Status Fix Implementation (2025-08-31) |

## Overview

This document provides a comprehensive directory of all REST API endpoints available in the Vitea Policy Management System. The system is designed for HIPAA-compliant healthcare policy management with enhanced validation, audit logging, dynamic enum management, and Azure cloud integration.

### System Architecture

The Vitea Policy Management System exposes a comprehensive REST API at `/api/v1/` with 14 distinct API modules providing full CRUD operations for policies, agents, roles, groups, schemas, and external integrations. All endpoints require authentication unless otherwise noted.

### Global Configuration
- **Base Path**: `/api/v1/`
- **Port**: 8001 (host), 8000 (container)
- **Authentication**: Bearer token via `Authorization: Bearer <token>` header
- **CORS**: Enabled for all origins (development mode)
- **Rate Limiting**: 500 requests per 15 minutes per IP
- **Middleware**: Helmet security, compression, correlation ID tracking
- **Request Size Limit**: 10MB JSON body limit
- **Response Format**: JSON with correlation ID headers

### Health & System Monitoring

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/health` | System health check with database connectivity | No |
| GET | `/metrics` | Prometheus metrics endpoint | No |
| GET | `/_metrics` | Alternative metrics endpoint | No |
| GET | `/api/v1/metrics-exposed` | API-prefixed metrics endpoint | No |
| GET | `/api/v1/test` | Basic API connectivity test | No |

**Health Check Response Example:**
```json
{
  "status": "healthy",
  "timestamp": "2025-08-31T19:47:19.881Z",
  "version": "2.0.0",
  "services": {
    "database": "connected"
  }
}
```

---

## 1. Policies API (`/api/v1/policies`)

**Primary Features**: Full policy lifecycle management, Rego generation, policy cloning, violation tracking, blob storage integration

### Core CRUD Operations

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/` | List policies with advanced filtering | Yes |
| POST | `/` | Create new policy with validation and audit logging | Yes |
| PUT | `/:id` | Update existing policy with comprehensive validation | Yes |
| DELETE | `/:id` | Soft delete policy (sets deleted_at timestamp) | Yes |

#### GET `/api/v1/policies`
**Purpose**: List policies with advanced filtering and enrichment

**Query Parameters**:
- `active` (boolean): Filter by active status
- `category` (string): Filter by policy category
- `severity` (string): Filter by severity level
- `search` (string): Full-text search across name and description
- `agentId` (UUID): Policies assigned to specific agent
- `groupId` (UUID): Policies in specific group
- `limit` (number, default: 20): Pagination limit
- `offset` (number, default: 0): Pagination offset

**Special Features**:
- **Agent+group intersection queries**: `?agentId=X&groupId=Y` returns policies assigned to agent via specific group
- **Assignment type enrichment**: Adds `assignment_type` field showing "Via <role> (<group>)"
- **Policy group name resolution**: Enriches response with policy group names in single extra query
- **Filtered results**: Only returns active policies when `agentId` specified (Policy Status Fix 2025-08-31)

**Response Example**:
```json
{
  "policies": [
    {
      "policy_id": "uuid",
      "name": "HIPAA Data Access Policy",
      "description": "Controls access to patient data",
      "category": "data_privacy",
      "severity": "critical",
      "is_active": true,
      "policy_type": "opa",
      "assignment_type": "Via Patient Care Coordinator (HIPAA Compliance)",
      "policy_groups": ["HIPAA Compliance", "Data Privacy"]
    }
  ],
  "pagination": {
    "limit": 20,
    "offset": 0,
    "total": "45"
  }
}
```

#### POST `/api/v1/policies`
**Purpose**: Create new policy with validation and audit logging

**Request Body**:
```json
{
  "name": "string (required)",
  "description": "string",
  "category": "string (required)", 
  "definition": "object (required)",
  "severity": "string (required)",
  "applies_to_roles": "array",
  "is_active": "boolean (default: true)",
  "policy_type": "string (default: opa)",
  "original_policy_id": "UUID (for cloned policies)",
  "groupIds": "array[UUID] (optional)"
}
```

**Features**:
- **JSON Schema Validation**: Uses AJV for policy definition validation
- **Duplicate Prevention**: Prevents policies with duplicate names
- **Automatic Group Linking**: Links policy to specified groups if provided
- **HIPAA Audit Logging**: Records creation with user attribution
- **Policy Type Validation**: Validates against available policy types

#### PUT `/api/v1/policies/:id`
**Purpose**: Update existing policy with comprehensive validation

**Key Features**:
- **Group Preservation Logic**: Only modifies group assignments when `groupIds` explicitly provided
- **Enhanced Validation**: Comprehensive JSON definition validation
- **Audit Trail**: Records before/after values for HIPAA compliance
- **Conflict Prevention**: Prevents duplicate names excluding current policy
- **Partial Updates**: Uses COALESCE for optional field updates

**Group Assignment Behavior** (Fixed 2025-08-31):
```javascript
// Only processes group changes when explicitly provided
if (groupIds !== undefined && groupIds !== null && Array.isArray(groupIds)) {
  if (groupIds.length === 0) {
    // Explicitly remove all group assignments
    await pool.query('DELETE FROM policy_group_policies WHERE policy_id = $1', [id]);
  } else {
    // Sync with provided group IDs
  }
}
// If groupIds undefined/null/not-array: preserve existing associations
```

### Rego Integration & Code Generation

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/:id/generate-rego` | Generate Rego policy code from policy definition | Yes |
| POST | `/generate-rego/bulk` | Bulk Rego generation for multiple policies | Yes |
| GET | `/:id/rego-code` | Retrieve generated Rego code from database | Yes |
| GET | `/:id/rego-status` | Get Rego generation and OPA sync status | Yes |
| POST | `/:id/rollback-rego` | Rollback Rego generation for policy | Yes |

#### POST `/api/v1/policies/:id/generate-rego`
**Request Body**: None (uses existing policy definition)
**Response**:
```json
{
  "success": true,
  "regoCode": "package policy.example\n\nallow {\n  input.user.role == \"admin\"\n}",
  "message": "Rego code generated successfully"
}
```

#### POST `/api/v1/policies/generate-rego/bulk`
**Request Body**:
```json
{
  "policy_ids": ["uuid1", "uuid2", "uuid3"]
}
```

**Response**:
```json
{
  "results": [
    {
      "policy_id": "uuid1",
      "success": true,
      "message": "Generated successfully"
    },
    {
      "policy_id": "uuid2", 
      "success": false,
      "error": "Invalid policy definition"
    }
  ]
}
```

### Azure Blob Storage Integration

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/:id/blob` | Download Rego file from Azure Blob Storage | Yes |
| POST | `/:id/upload-blob` | Upload Rego code to Azure Blob Storage | Yes |
| DELETE | `/:id/blob` | Delete Rego file from blob storage | Yes |
| POST | `/bundle-rego` | Bundle all active Rego files into archive | Yes |

#### POST `/api/v1/policies/:id/upload-blob`
**Request Body**:
```json
{
  "regoCode": "package policy.example\n\nallow { true }"
}
```

**Features**:
- **Automatic Path Generation**: Creates blob path as `policies/{policy_id}.rego`
- **Database Integration**: Updates policy record with blob URL and metadata
- **Sync Status Update**: Sets OPA sync status to pending

#### POST `/api/v1/policies/bundle-rego`
**Purpose**: Bundle all active Rego files into timestamped .tar.gz archive
**Response**: Binary download with filename `rego_bundle_YYYYMMDD_HHMMSS.tar.gz`
**Content-Type**: `application/gzip`

### Policy Violations & Monitoring

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/violations` | List policy violations with user details | Yes |

#### GET `/api/v1/policies/violations`
**Query Parameters**:
- `policy_id` (UUID): Filter by specific policy
- `resolved` (boolean): Filter by resolution status  
- `limit` (number, default: 50): Result limit

**Response**:
```json
[
  {
    "violation_id": "uuid",
    "policy_id": "uuid",
    "policy_name": "HIPAA Access Control",
    "user_id": "uuid",
    "user_email": "<EMAIL>",
    "violation_type": "unauthorized_access",
    "created_at": "2025-08-31T10:30:00Z",
    "resolved": false
  }
]
```

### Policy Management Utilities

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/:id/clone` | Clone existing policy with new name | Yes |
| GET | `/categories` | Get all distinct policy categories | Yes |
| GET | `/types` | Get available policy types from schema definitions | Yes |
| GET | `/:id/assignments` | Get comprehensive assignment information for policy | Yes |

#### POST `/api/v1/policies/:id/clone`
**Request Body**:
```json
{
  "new_name": "Cloned HIPAA Policy v2",
  "new_description": "Updated version of HIPAA policy"
}
```

**Features**:
- **Duplicate Validation**: Ensures new name is unique
- **Stored Procedure**: Uses database stored procedure for atomic cloning
- **Metadata Preservation**: Copies all policy properties except name/description
- **Audit Trail**: Records cloning operation

### Legacy Template Endpoints (Deprecated)

| Method | Endpoint | Description | Status |
|--------|----------|-------------|--------|
| GET | `/templates` | Legacy template listing | **DEPRECATED** |
| GET | `/templates/:category` | Legacy category template | **DEPRECATED** |

**Deprecation Headers**:
- `X-Deprecated: true`
- `X-Deprecation-Date: 2025-01-01`
- `X-Replacement: /api/v1/schemas/templates/status`

---

## 2. Policy Groups API (`/api/v1/policy-groups`)

**Primary Features**: Policy group lifecycle, policy membership management, status transitions, audit logging

### Core Group Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/` | List policy groups with usage statistics | Yes |
| POST | `/` | Create new policy group | Yes |
| PUT | `/:id` | Update policy group properties | Yes |
| DELETE | `/:id` | Soft delete (deprecate) policy group | Yes |
| POST | `/:id/restore` | Restore deprecated group to active status | Yes |

#### GET `/api/v1/policy-groups`
**Purpose**: List policy groups with usage statistics

**Query Parameters**:
- `status` (string, default: 'active'): Filter by status ('active', 'deprecated', 'all')

**Response Features**:
- **Usage Statistics**: Includes `uses_count` field showing count of active policies
- **Policy Filtering**: Only counts active, non-deleted policies in usage statistics

**Response Example**:
```json
[
  {
    "group_id": "uuid",
    "name": "HIPAA Compliance",
    "description": "HIPAA-related policies",
    "is_template": false,
    "severity": "critical",
    "tags": ["healthcare", "privacy"],
    "status": "active",
    "uses_count": 15,
    "created_at": "2025-08-01T10:00:00Z",
    "updated_at": "2025-08-31T09:00:00Z"
  }
]
```

#### POST `/api/v1/policy-groups`
**Request Body**:
```json
{
  "name": "string (required)",
  "description": "string (optional)",
  "is_template": "boolean (default: false)",
  "severity": "string (default: 'medium')",
  "tags": "array (optional)"
}
```

**Features**:
- **Unique Name Validation**: Prevents duplicate group names (409 error)
- **HIPAA Audit Logging**: Records creation with user attribution
- **Tag Support**: JSON array of tags for categorization

### Policy Membership Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/:id/policies` | Add policies to group (bulk operation) | Yes |
| DELETE | `/:id/policies/:policyId` | Remove single policy from group | Yes |
| GET | `/:id/policies` | List policies in specific group | Yes |
| GET | `/policy/:id` | Find all groups containing specific policy | Yes |

#### POST `/api/v1/policy-groups/:id/policies`
**Purpose**: Add policies to group (bulk operation)

**Request Body**:
```json
{
  "policyIds": ["uuid1", "uuid2", "uuid3"]
}
```

**Features**:
- **Bulk Operation**: Efficiently adds multiple policies in single operation
- **Conflict-Safe**: Uses `ON CONFLICT DO NOTHING` for duplicate-safe insertion
- **Audit Logging**: Records bulk addition with policy ID list
- **Response**: Returns count of policies actually added

**Response**:
```json
{
  "added": 3
}
```

#### GET `/api/v1/policy-groups/:id/policies`
**Purpose**: List policies in specific group

**Query Parameters**:
- `include_inactive` (boolean, default: **true** as of 2025-08-31): Include inactive policies

**Features**:
- **Active Policy Preference**: Orders by active status first, then by name
- **Soft Delete Filtering**: Excludes policies marked as deleted
- **Status Toggle Integration**: Shows inactive policies by default for policy management

**Response**: Array of policy objects with full policy details

### Group Status Management

#### DELETE `/api/v1/policy-groups/:id`
**Purpose**: Soft delete (deprecate) policy group
**Behavior**: 
- Sets status to 'deprecated' instead of hard deletion
- Preserves all data and relationships
- Maintains referential integrity
- **Audit Logging**: Records deprecation action

#### POST `/api/v1/policy-groups/:id/restore`
**Purpose**: Restore deprecated group to active status
**Features**:
- Reverses deprecation by setting status to 'active'
- **Audit Logging**: Records restoration action with timestamp

---

## 3. Agents API (`/api/v1/agents`)

**Primary Features**: Agent lifecycle management, role assignments, policy associations, risk scoring

### Core Agent Operations

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/` | List all agents with status filtering | Yes |
| GET | `/:id` | Get single agent details | Yes |
| POST | `/` | Create new agent | Yes |
| PUT | `/:id` | Update agent properties | Yes |
| DELETE | `/:id` | Hard delete agent | Yes |

#### GET `/api/v1/agents`
**Query Parameters**:
- `status` (string, default: 'active'): Filter by status ('active', 'inactive', 'all')

**Response**: Agent list ordered by name

#### POST `/api/v1/agents`
**Request Body**:
```json
{
  "name": "string (required)",
  "description": "string (optional)",
  "agent_type": "string (default: 'policy_engine')",
  "endpoint_url": "string (optional)",
  "is_active": "boolean (default: true)",
  "vendor": "string (optional)",
  "department": "string (optional)", 
  "risk_score": "number (default: 0.0)",
  "status": "string (default: 'active')"
}
```

**Validation**:
- **Name Required**: Name field is required and trimmed
- **Risk Score**: Automatically converted to numeric type
- **Status Values**: Validates against allowed status values

### Agent Role Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/:id/roles` | List roles assigned to agent | Yes |
| POST | `/:id/access` | Grant role access to agent | Yes |
| DELETE | `/:id/access` | Revoke role access from agent | Yes |

#### GET `/api/v1/agents/:id/roles`
**Purpose**: List roles assigned to agent
**Response**: Role details via `agent_access` table join
**Ordering**: By role name with nulls last

#### POST `/api/v1/agents/:id/access`
**Request Body**:
```json
{
  "roleId": "uuid (required)"
}
```
**Features**: Conflict-safe insertion using `ON CONFLICT DO NOTHING`

### Agent Policy Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| DELETE | `/:id/policies/:policyId` | Remove direct policy assignment from agent | Yes |
| DELETE | `/:id/groups/:groupId` | Remove all group-based policy assignments | Yes |

#### DELETE `/api/v1/agents/:id/policies/:policyId`
**Purpose**: Remove direct policy assignment from agent
**Behavior**: 
- Only removes assignments with `link_type = 'direct'`
- Preserves role-based and group-based assignments
- Surgical removal without affecting other assignment types

#### DELETE `/api/v1/agents/:id/groups/:groupId`
**Purpose**: Remove all group-based policy assignments
**Behavior**: 
- Removes assignments with `link_type = 'via_group'` for specific group
- Complex query targeting group-specific assignments
- Preserves direct and role-based assignments

---

## 4. Agent Role Policies API (`/api/v1/`)

**Primary Features**: Advanced agent-role-policy assignment system with group scoping, bulk operations

### Agent-Scoped Role Policy Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/agents/:id/role-policies` | List role-scoped policy assignments for specific agent | Yes |
| POST | `/agents/:id/role-policies` | Add single role-scoped policy assignment to agent | Yes |
| POST | `/agents/:id/role-policies/bulk` | Bulk add multiple policies for same agent/role/group | Yes |
| DELETE | `/agents/:id/role-policies` | Remove specific role-scoped policy assignment | Yes |

#### GET `/api/v1/agents/:id/role-policies`
**Purpose**: List role-scoped policy assignments for specific agent

**Response Structure**:
```json
[
  {
    "agent_id": "uuid",
    "role_id": "uuid",
    "role_name": "Patient Care Coordinator", 
    "role_code": "HIPAA_CASE_MANAGER",
    "group_id": "uuid",
    "group_name": "HIPAA Compliance",
    "group_description": "HIPAA-related policies", 
    "policy_id": "uuid",
    "policy_name": "Patient Data Access",
    "policy_description": "Controls patient data access"
  }
]
```

**Features** (Fixed 2025-08-31):
- **Active Policy Filter**: Only returns policies where `p.is_active = true`
- **Active Group Filter**: Only includes groups with `pg.status = 'active'`
- **Rich Enrichment**: Includes role names/codes and group descriptions
- **Proper Ordering**: Sorted by role name, group name, then policy name

#### POST `/api/v1/agents/:id/role-policies`
**Request Body**:
```json
{
  "roleId": "uuid (required)",
  "groupId": "uuid (required)", 
  "policyId": "uuid (required)"
}
```

**Validation**:
- **Group-Policy Validation**: Ensures policy is actually a member of specified group
- **Error Response**: 400 error if policy not in group
- **Conflict Safety**: Uses `ON CONFLICT DO NOTHING` for duplicate prevention

#### POST `/api/v1/agents/:id/role-policies/bulk`
**Request Body**:
```json
{
  "roleId": "uuid (required)",
  "groupId": "uuid (required)",
  "policyIds": ["uuid1", "uuid2", "uuid3"] (required, non-empty)"
}
```

**Advanced Validation**:
- **Bulk Group Validation**: Validates ALL policies belong to specified group before any insertion
- **Atomic Operation**: All-or-nothing insertion - if any policy invalid, entire operation fails
- **Detailed Error Response**: Returns specific invalid policy IDs if validation fails

**Error Response Example**:
```json
{
  "error": "Some policies are not members of the specified group",
  "invalid": ["uuid2", "uuid4"]
}
```

### Flat Agent Role Policy Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/agent-role-policies/:agentId` | Alternative flat endpoint for role-scoped policies | Yes |
| POST | `/agent-role-policies` | Add role-scoped policy with all IDs in body | Yes |
| POST | `/agent-role-policies/bulk` | Bulk add with all IDs in request body | Yes |
| DELETE | `/agent-role-policies` | Remove assignment with all IDs specified in body | Yes |

**Use Case**: More REST-like URL structure for systems preferring flat endpoint hierarchies

#### POST `/api/v1/agent-role-policies`
**Request Body**:
```json
{
  "agentId": "uuid (required)",
  "roleId": "uuid (required)",
  "groupId": "uuid (required)",
  "policyId": "uuid (required)"
}
```

**Features**: Same validation and conflict safety as agent-scoped version

---

## 5. Agent Policies API (`/api/v1/agent-policies`)

**Primary Features**: Consolidated view of agent policy assignments across direct and group-based assignments

### Consolidated Policy View

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/:id/policies` | Get all policies assigned to agent regardless of assignment method | Yes |

#### GET `/api/v1/agent-policies/:id/policies`
**Purpose**: Get all policies assigned to agent regardless of assignment method

**Authorization**: 
- Respects user's `allowedAgents` restriction if configured in user context
- Falls back to requiring admin authentication

**Response Structure**:
```json
[
  {
    "policy_id": "uuid",
    "name": "HIPAA Data Access",
    "description": "Controls access to patient data",
    "category": "data_privacy",
    "severity": "critical",
    "assignment_type": "Direct",
    "policy_type": "opa",
    "is_active": true,
    "created_at": "2025-08-01T10:00:00Z"
  },
  {
    "policy_id": "uuid",
    "name": "Emergency Access Override", 
    "description": "Emergency access procedures",
    "category": "access_control",
    "severity": "high",
    "assignment_type": "Via Emergency Response Group",
    "policy_type": "opa",
    "is_active": true,
    "created_at": "2025-08-01T10:00:00Z"
  }
]
```

### Assignment Type Logic
- **Direct**: Policy directly assigned to agent via `agent_policies` table with `link_type = 'direct'`
- **Via Group**: Policy assigned through group membership via `agent_policies` with `link_type = 'via_group'`
- **Role-based**: Policies assigned through role and group combinations (handled by Agent Role Policies API)

**Implementation Details**:
- **CTE (Common Table Expression)**: Uses advanced SQL for performance optimization
- **UNION Operation**: Combines direct and group-based assignments, eliminating duplicates
- **Performance**: Single query handles complex assignment resolution

---

## 6. Roles API (`/api/v1/roles`)

**Primary Features**: Role management with audit logging, assignment validation, usage safety checks

### Core Role Operations

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/` | List all roles | Yes |
| POST | `/` | Create new role | Yes |
| PUT | `/:id` | Update role properties (excluding code) | Yes |
| DELETE | `/:id` | Hard delete role with safety checks | Yes |

#### GET `/api/v1/roles`
**Response**: Basic role information ordered by role code
```json
[
  {
    "role_id": "uuid",
    "code": "ADMIN",
    "name": "System Administrator", 
    "description": "Full system access"
  },
  {
    "role_id": "uuid",
    "code": "HIPAA_CASE_MANAGER",
    "name": "HIPAA Case Manager",
    "description": "Healthcare case management role"
  }
]
```

#### POST `/api/v1/roles`
**Request Body**:
```json
{
  "code": "string (required)",
  "name": "string (optional)",
  "description": "string (optional)"
}
```

**Features**:
- **Code Normalization**: Automatically converts code to uppercase
- **Unique Validation**: Prevents duplicate role codes (409 error)
- **HIPAA Audit Logging**: Records role creation with user attribution

#### PUT `/api/v1/roles/:id`
**Request Body**:
```json
{
  "name": "string (optional)",
  "description": "string (optional)"
}
```

**Important**: Role code cannot be modified after creation for referential integrity

#### DELETE `/api/v1/roles/:id`
**Purpose**: Hard delete role with comprehensive safety checks

**Safety Validations**:
- **Usage Check**: Prevents deletion if role has active user assignments
- **Assignment Check**: Validates no active agent-role relationships exist
- **Error Response**: 409 error if role still in use with detailed message

**Features**:
- **HIPAA Audit Logging**: Records deletion with role details
- **Safe Deletion**: Comprehensive referential integrity checks

---

## 7. Schemas API (`/api/v1/schemas`)

**Primary Features**: JSON schema management, template integration, validation services, guardrail association

### Core Schema Operations

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/` | Get all active schemas in UI format | Yes |
| GET | `/list` | Get schema metadata list | Yes |
| GET | `/:name` | Get specific schema with guardrail information | Yes |
| PUT | `/:name` | Update or create schema | Yes |
| DELETE | `/:name` | Soft delete schema (sets is_active = false) | Yes |

#### GET `/api/v1/schemas`
**Purpose**: Get all active schemas in UI format
**Response**: Complete schema definitions ready for client consumption with full JSON schema structure

#### GET `/api/v1/schemas/list`
**Response Format**:
```json
{
  "schemas": [
    {
      "name": "hipaa_policy_schema",
      "description": "HIPAA compliance policy schema",
      "guardrail_id": "HIPAA-001",
      "is_active": true,
      "created_at": "2025-08-01T10:00:00Z",
      "updated_at": "2025-08-31T09:00:00Z"
    }
  ]
}
```

#### PUT `/api/v1/schemas/:name`
**Request Body**:
```json
{
  "schema_content": "object (required)",
  "description": "string (optional)",
  "guardrail_id": "string (optional)"
}
```

**Features**:
- **JSON Schema Validation**: Uses AJV to validate schema structure
- **Schema Cache Management**: Clears cache on schema updates
- **Template Integration**: Automatically generates/updates templates
- **Upsert Operation**: Creates if not exists, updates if exists
- **Guardrail Association**: Links schemas to compliance guardrails

**Validation Example**:
```json
{
  "schema_content": {
    "type": "object",
    "properties": {
      "severity": {
        "type": "string",
        "enum": ["low", "medium", "high", "critical"]
      },
      "data_classification": {
        "type": "string",
        "enum": ["public", "internal", "confidential", "restricted"]
      }
    },
    "required": ["severity"]
  },
  "description": "Healthcare data privacy schema",
  "guardrail_id": "HIPAA-DATA-001"
}
```

### Schema Validation Services

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/validate` | Test endpoint for validating data against schema | Yes |

#### POST `/api/v1/schemas/validate`
**Request Body**:
```json
{
  "schema_name": "string (required)",
  "data": "object (required)"
}
```

**Response Example**:
```json
{
  "valid": false,
  "errors": [
    {
      "path": "/severity",
      "message": "is required",
      "value": undefined
    }
  ],
  "warnings": [
    {
      "path": "/data_classification", 
      "message": "recommended field not provided"
    }
  ]
}
```

### Guardrail Integration

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/guardrail/:guardrailId` | Get all schemas associated with specific guardrail | Yes |

#### GET `/api/v1/schemas/guardrail/:guardrailId`
**Purpose**: Get all schemas associated with specific guardrail
**Response**: Array of schemas linked to the guardrail ID

**Use Case**: Compliance reporting and guardrail impact analysis

---

## 8. Schema Templates API

**Primary Features**: Template generation, manual overrides, bulk operations, source tracking

### Template Operations

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/:name/template` | Get default template for schema | Yes |
| PUT | `/:name/template` | Manual template override (admin only) | Yes |
| DELETE | `/:name/template` | Reset template to auto-generated from schema | Yes |

#### GET `/api/v1/schemas/:name/template`
**Purpose**: Get default template for schema with multi-source fallback

**Source Priority**:
1. **Database**: Manual template override stored in database
2. **Auto-generate**: Generated from schema definition  
3. **Hardcoded**: Default fallback template

**Response Example**:
```json
{
  "template": {
    "severity": "medium",
    "data_classification": "internal",
    "enabled": true,
    "description": ""
  },
  "source": "auto_generated",
  "schema_name": "data_privacy_policy"
}
```

#### PUT `/api/v1/schemas/:name/template`
**Request Body**:
```json
{
  "template": "object (required)",
  "source": "string (manual_override|external_provided)"
}
```

**Features**:
- **Admin Control**: Manual template customization
- **Source Validation**: Validates source parameter values
- **Override Capability**: Allows customization beyond auto-generated templates

### Bulk Template Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/regenerate-templates` | Regenerate all auto-generated templates | Yes |
| GET | `/templates/status` | Get status of all schema templates with statistics | Yes |

#### POST `/api/v1/schemas/regenerate-templates`
**Query Parameters**:
- `overrideManual` (boolean, default: false): Whether to override manual templates

**Purpose**: Admin utility for bulk template regeneration

#### GET `/api/v1/schemas/templates/status`
**Response**:
```json
{
  "summary": {
    "total_schemas": 15,
    "with_templates": 12,
    "without_templates": 3,
    "by_source": {
      "auto_generated": 8,
      "manual_override": 3,
      "external_provided": 1
    }
  },
  "schemas": [
    {
      "name": "hipaa_policy_schema",
      "has_template": true,
      "source": "manual_override",
      "last_generated": "2025-08-31T10:00:00Z"
    }
  ]
}
```

**Features**: Comprehensive template ecosystem overview for admin monitoring

---

## 9. Enums API (`/api/v1/enums`)

**Primary Features**: Dynamic enum management system with category organization, policy type association

### Enum Value Retrieval

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/values/:policyType/:fieldPath` | Get enum values for specific policy field | Yes |
| GET | `/fields/:policyType` | Get all enum fields available for policy type | Yes |

#### GET `/api/v1/enums/values/:policyType/:fieldPath`
**Purpose**: Get enum values for specific policy field

**Path Parameters**: 
- `policyType`: Policy type identifier (e.g., "hipaa_policy", "access_control")
- `fieldPath`: Dot-notation field path (e.g., "severity", "data_classification.level")

**Response Example**:
```json
[
  {
    "value": "critical",
    "display_name": "Critical",
    "description": "Highest priority requiring immediate attention",
    "sort_order": 1
  },
  {
    "value": "high", 
    "display_name": "High",
    "description": "High priority requiring prompt attention",
    "sort_order": 2
  }
]
```

**Features**: Uses database stored function for efficient retrieval with proper sorting

#### GET `/api/v1/enums/fields/:policyType`
**Purpose**: Get all enum fields available for policy type
**Response**: Field paths with category information for dynamic form generation

### Category Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/categories` | List all active enum categories | Yes |
| POST | `/categories` | Create new enum category | Yes |
| GET | `/categories/:categoryId/values` | Get all values for specific category | Yes |

#### GET `/api/v1/enums/categories`
**Response Example**:
```json
[
  {
    "category_id": "uuid",
    "name": "severity_levels",
    "description": "Policy severity classification",
    "policy_type": "all",
    "field_path": "severity",
    "is_active": true
  }
]
```

#### POST `/api/v1/enums/categories`
**Request Body**:
```json
{
  "name": "string (required)",
  "description": "string (optional)",
  "policy_type": "string (required)",
  "field_path": "string (required)"
}
```

**Features**:
- **Unique Name Validation**: Prevents duplicate category names
- **Policy Type Association**: Links categories to specific policy types
- **Field Path Mapping**: Associates with specific JSON schema paths

### Enum Value Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/categories/:categoryId/values` | Create new enum value | Yes |
| PUT | `/values/:valueId` | Update enum value properties | Yes |
| DELETE | `/values/:valueId` | Soft delete enum value | Yes |

#### POST `/api/v1/enums/categories/:categoryId/values`
**Request Body**:
```json
{
  "value": "string (required)",
  "display_name": "string (optional)",
  "description": "string (optional)",
  "sort_order": "number (default: 0)"
}
```

**Features**:
- **Unique Value Validation**: Prevents duplicate values within category
- **Sort Order Support**: Allows custom ordering of enum values
- **Display Name**: Supports user-friendly display names different from internal values

---

## 10. Metrics API (`/api/v1/metrics`)

**Primary Features**: System health monitoring, dashboard statistics, group analytics, orphaned policy detection

### System Statistics

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/` | Get comprehensive system metrics for dashboard | Yes |

#### GET `/api/v1/metrics`
**Purpose**: Get comprehensive system metrics for dashboard

**Response Example**:
```json
{
  "timestamp": "2025-08-31T19:47:19.881Z",
  "historical_metrics": [
    {
      "metric_date": "2025-08-31",
      "total_policies": 45,
      "active_policies": 42,
      "critical_violations": 3,
      "daily_chat_sessions": 15
    }
  ],
  "current_stats": {
    "total_policies": 45,
    "active_policies": 42, 
    "critical_policies": 12,
    "total_violations": 8,
    "unresolved_violations": 3,
    "daily_chat_sessions": 15
  }
}
```

**Data Sources**:
- **Historical**: `system_metrics` table with date-based records
- **Real-time**: Calculated from current database state
- **Chat Sessions**: Daily aggregated counts

### Group Analytics

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/group-stats` | Get policy group usage analytics | Yes |
| GET | `/orphaned-policies` | Identify policies not assigned to any group | Yes |

#### GET `/api/v1/metrics/group-stats`
**Response Example**:
```json
{
  "policies_per_group": [
    {
      "group_name": "HIPAA Compliance",
      "policy_count": 15
    },
    {
      "group_name": "Emergency Access",
      "policy_count": 8
    }
  ],
  "agents_per_group": [
    {
      "group_name": "HIPAA Compliance", 
      "agent_count": 25
    }
  ]
}
```

**Features**:
- **Descending Order**: Results ordered by count for priority visibility
- **Deleted Record Filtering**: Excludes soft-deleted policies and groups
- **Agent Statistics**: Includes agent assignment counts if `agent_access` table exists

#### GET `/api/v1/metrics/orphaned-policies`
**Purpose**: Identify policies not assigned to any group

**Response Example**:
```json
{
  "count": 3,
  "policies": [
    {
      "policy_id": "uuid",
      "name": "Standalone Emergency Policy",
      "description": "Emergency policy not in any group",
      "category": "access_control",
      "severity": "high"
    }
  ]
}
```

**Use Case**: Data quality monitoring and policy organization auditing

---

## 11. Chat API (`/api/v1/chat`)

**Primary Features**: Azure OpenAI integration, MCP tool calling, session management, streaming responses

### Session Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/session` | Create new chat session | Yes |

#### POST `/api/v1/chat/session`
**Request Body** (optional):
```json
{
  "metadata": {
    "user_id": "string",
    "context": "policy_management"
  }
}
```

**Response Example**:
```json
{
  "session_id": "uuid",
  "created_at": "2025-08-31T10:00:00Z",
  "mcp_status": {
    "connected": true,
    "tools_available": 12,
    "server_version": "1.0.0"
  }
}
```

**Features**:
- **UUID Session Generation**: Creates unique session identifiers
- **MCP Integration Status**: Reports Model Context Protocol connection status
- **Tool Availability**: Shows count of available MCP tools

### Message Processing

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/:sessionId/message` | Process chat message with AI and tool calling | Yes |

#### POST `/api/v1/chat/:sessionId/message`
**Request Body**:
```json
{
  "message": "Show me all critical HIPAA policies assigned to agent John"
}
```

**Response Example**:
```json
{
  "response": "I found 3 critical HIPAA policies assigned to agent John:\n\n1. **Patient Data Access Control** - Controls access to patient records\n2. **Emergency Access Override** - Emergency access procedures\n3. **Audit Trail Compliance** - Maintains HIPAA audit requirements",
  "session_id": "uuid",
  "timestamp": "2025-08-31T10:00:00Z",
  "tools_used": [
    {
      "name": "get_agent_policies",
      "parameters": {
        "agent_name": "John",
        "severity": "critical"
      },
      "result": "success"
    }
  ]
}
```

### AI Integration Features
- **Azure OpenAI GPT**: Integration with Azure OpenAI service
- **Native MCP Tool Calling**: Direct MCP tool execution via SSE transport
- **Tool Result Integration**: Seamless integration of tool results into conversation
- **Healthcare Compliance**: Specialized messaging for healthcare policy context

### MCP (Model Context Protocol) Integration

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/:sessionId/flow` | Get MCP processing flow visualization | Yes |
| GET | `/mcp/status` | MCP connection and tool status | Yes |

#### GET `/api/v1/chat/:sessionId/flow`
**Purpose**: Get MCP processing flow visualization for debugging

**Response Example**:
```json
{
  "session_id": "uuid",
  "processing_steps": [
    {
      "step": 1,
      "name": "Message Received",
      "timestamp": "2025-08-31T10:00:00.000Z",
      "status": "completed"
    },
    {
      "step": 2,
      "name": "Tool Discovery",
      "timestamp": "2025-08-31T10:00:00.100Z", 
      "status": "completed",
      "tools_found": 12
    }
  ]
}
```

**Features**: 12-step processing flow with timestamps for performance analysis

#### GET `/api/v1/chat/mcp/status`
**Response Example**:
```json
{
  "connection_status": "connected",
  "server_endpoint": "mcp-server:8002",
  "available_tools": [
    {
      "name": "get_agent_policies",
      "description": "Retrieve policies assigned to specific agent"
    },
    {
      "name": "search_policies",
      "description": "Search policies by criteria"
    }
  ],
  "last_connection_check": "2025-08-31T10:00:00Z"
}
```

**Technical Details**:
- **Native MCP Client**: Direct connection to MCP server container via SSE
- **Tool Discovery**: Dynamic tool enumeration from MCP server
- **Connection Management**: Persistent connection to `mcp-server:8002`
- **Error Handling**: Graceful handling of MCP connection failures

---

## 12. Integrations API (`/api/v1/integrations`)

**Primary Features**: External system integration, snapshot-based data export, cursor-based pagination, OAuth authentication

### Policy Assignment Snapshots

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/assignments-snapshot` | Export agent-role-policy assignments for external systems | OAuth Client |

#### GET `/api/v1/integrations/assignments-snapshot`
**Purpose**: Export agent-role-policy assignments for external systems

**Authentication**: OAuth client credentials (NOT admin token)
```
Authorization: Bearer <oauth_client_token>
```

**Query Parameters**:
- `page_size` (number, max: 2000, default: 500): Results per page
- `cursor` (string): Base64-encoded pagination cursor for next page
- `snapshot_id` (string): Snapshot consistency identifier (auto-generated if not provided)

**Response Example**:
```json
{
  "snapshot_id": "snapshot_2025083110000_uuid",
  "generated_at": "2025-08-31T10:00:00.000Z",
  "items": [
    {
      "agent_id": "uuid",
      "role_id": "uuid", 
      "group_id": "uuid",
      "policy_id": "uuid",
      "policy": {
        "guardrail_id": "HIPAA-001",
        "name": "Patient Data Access Control",
        "description": "Controls access to patient records",
        "category": "data_privacy",
        "policy_type": "opa",
        "definition": {
          "type": "access_control",
          "severity": "critical",
          "allowed_roles": ["doctor", "nurse"],
          "data_classification": "confidential"
        },
        "version": "1.2.0",
        "is_active": true,
        "severity": "critical"
      }
    }
  ],
  "next_cursor": "eyJsYXN0X3NlZW4iOiJ1dWlkIiwic25hcHNob3QiOiJzbmFwc2hvdF8yMDI1MDgzMTEwMDAwX3V1aWQifQ==",
  "correlation_id": "uuid"
}
```

### Key Features

#### Snapshot Consistency
- **Snapshot ID**: Ensures consistent data across paginated requests
- **Auto-generation**: System generates snapshot ID if not provided
- **Time-based**: Snapshot represents state at specific timestamp
- **Cross-page Consistency**: All pages with same snapshot_id return data from same point in time

#### Cursor-Based Pagination
- **Efficient Traversal**: Base64-encoded cursors for efficient large dataset pagination
- **Stateless**: No server-side session state required
- **Next Page**: `next_cursor` field provides cursor for next page (null on last page)
- **Large Datasets**: Handles datasets with thousands of assignments efficiently

#### Policy Enrichment
- **Complete Policy Data**: Each assignment includes full policy object
- **Guardrail Integration**: Includes guardrail_id for compliance tracking
- **Version Information**: Policy version for change tracking
- **Definition Included**: Full policy definition object for external processing

#### Performance & Monitoring
- **Response Time Tracking**: Monitors API performance
- **Request Count Metrics**: Tracks integration usage
- **Correlation ID**: Request tracing across system boundaries

**Use Cases**:
- **External Policy Engines**: Export assignments for OPA/Cedar/external policy engines
- **Compliance Reporting**: Generate compliance reports in external systems  
- **Data Synchronization**: Sync policy assignments with external identity systems
- **Audit Trail**: Export assignment data for audit systems

---

## 13. Integrations Admin API (`/api/v1/integrations-admin`)

**Primary Features**: Dead Letter Queue management, message replay functionality, integration monitoring

### DLQ Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/dlq/:id/replay` | Replay failed integration message from DLQ back to outbox | Yes |

#### POST `/api/v1/integrations-admin/dlq/:id/replay`
**Purpose**: Replay failed integration message from DLQ back to outbox

**Path Parameters**: 
- `id`: DLQ item identifier (UUID)

**Security**: 
- **Production Safety**: Automatically disabled in production environment
- **Admin Only**: Requires admin authentication
- **Environment Check**: Returns 403 in production to prevent accidental usage

**Features**:
- **DLQ Item Validation**: Ensures DLQ item exists before replay
- **Outbox Insertion**: Moves message back to outbox for reprocessing
- **Status Reset**: Resets processing status and attempt counters
- **Conflict Resolution**: Uses `ON CONFLICT DO UPDATE` for safe insertion
- **Attempt Counter Reset**: Resets retry attempts for fresh processing

**Response Example**:
```json
{
  "success": true,
  "message": "DLQ item replayed successfully",
  "dlq_id": "uuid",
  "outbox_id": "uuid",
  "attempts_reset": true
}
```

**Error Responses**:
```json
// Production environment
{
  "error": "DLQ replay is disabled in production environment"
}

// DLQ item not found
{
  "error": "DLQ item not found",
  "dlq_id": "uuid"
}
```

**Use Cases**:
- **Failed Message Recovery**: Recover messages that failed due to temporary issues
- **Development Testing**: Test message processing flows
- **Integration Debugging**: Replay messages for troubleshooting
- **Data Recovery**: Recover lost integration messages

---

## 14. Bulk Assignment API (`/api/v1/bulk-policy-assign`)

**Primary Features**: Bulk policy assignment operations with transaction safety, dual assignment types

### Bulk Assignment Operations

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/` | Bulk assign policies and policy groups to multiple agents | Yes |

#### POST `/api/v1/bulk-policy-assign`
**Purpose**: Bulk assign policies and policy groups to multiple agents with transaction safety

**Request Body**:
```json
{
  "policyIds": ["uuid1", "uuid2", "uuid3"],
  "groupIds": ["uuid1", "uuid2"], 
  "targetAgentIds": ["uuid1", "uuid2", "uuid3"]
}
```

**Field Validation**:
- **policyIds**: Optional array of policy UUIDs for direct assignment
- **groupIds**: Optional array of policy group UUIDs for group-based assignment
- **targetAgentIds**: Required non-empty array of agent UUIDs
- **At least one**: Must specify either policyIds or groupIds (or both)

**Response Example**:
```json
{
  "success": true,
  "assignments_created": {
    "direct_policy_assignments": 9,
    "group_policy_assignments": 24,
    "total_assignments": 33
  },
  "agents_affected": 3,
  "policies_assigned": {
    "direct_policies": 3,
    "policies_from_groups": 8,
    "total_unique_policies": 11
  },
  "operation_id": "uuid"
}
```

### Assignment Types Created

#### Direct Policy Assignment
- **Table**: `agent_policies`
- **Link Type**: `'direct'`
- **Behavior**: Creates direct agent-to-policy relationship
- **Count**: `len(policyIds) × len(targetAgentIds)`

#### Group-Based Assignment
- **Table**: `agent_policies` 
- **Link Type**: `'via_group'`
- **Behavior**: Expands groups to individual policies, creates agent-to-policy relationship for each
- **Group Expansion**: Resolves each group to its member policies
- **Count**: `(sum of policies in all groups) × len(targetAgentIds)`

### Advanced Features

#### Transaction Safety
- **Atomic Operation**: Entire operation succeeds or fails as unit
- **Full Rollback**: Any error causes complete rollback of all assignments
- **Consistency**: Database remains consistent even on partial failures
- **Error Isolation**: Single invalid ID doesn't affect valid assignments

#### Conflict Resolution
- **Duplicate Prevention**: Uses `ON CONFLICT DO NOTHING` for safe insertion
- **Idempotent**: Safe to run multiple times with same parameters
- **Existing Assignment Preservation**: Doesn't overwrite existing assignments

#### Comprehensive Audit Logging
- **Operation Tracking**: Complete audit trail with operation ID
- **Assignment Details**: Logs all assignments created with metadata
- **User Attribution**: HIPAA-compliant user tracking
- **Timestamp Records**: Precise timing for audit requirements

**SQL Operations**:
```sql
-- Direct assignments
INSERT INTO agent_policies (agent_id, policy_id, link_type, created_at) 
SELECT agent_id, policy_id, 'direct', NOW() 
FROM unnest($1::uuid[]) AS agent_id 
CROSS JOIN unnest($2::uuid[]) AS policy_id
ON CONFLICT DO NOTHING;

-- Group-based assignments (with group expansion)
INSERT INTO agent_policies (agent_id, policy_id, link_type, group_context_id, created_at)
SELECT agent_id, policy_id, 'via_group', group_id, NOW()
FROM unnest($1::uuid[]) AS agent_id
CROSS JOIN (
  SELECT pgp.policy_id, pgp.group_id 
  FROM policy_group_policies pgp 
  WHERE pgp.group_id = ANY($2::uuid[])
) AS group_policies
ON CONFLICT DO NOTHING;
```

**Error Handling**:
- **Invalid Policy IDs**: 400 error with list of invalid IDs
- **Invalid Group IDs**: 400 error with list of invalid IDs  
- **Invalid Agent IDs**: 400 error with list of invalid IDs
- **Empty Results**: 400 error if no valid assignments possible
- **Database Errors**: 500 error with safe error message

**Use Cases**:
- **New Agent Onboarding**: Assign standard policy sets to new agents
- **Policy Rollout**: Deploy new policies to multiple agents simultaneously
- **Group Membership Changes**: Bulk update agent access when group policies change
- **Compliance Updates**: Ensure all relevant agents have required policies

---

## Authentication & Authorization

### Authentication Methods

#### 1. Admin Bearer Token
**Usage**: Most CRUD endpoints
**Header**: `Authorization: Bearer admin-token`
**Scope**: Full system access for policy management operations

#### 2. OAuth2 Client Credentials
**Usage**: Integration endpoints (`/api/v1/integrations`)
**Header**: `Authorization: Bearer <oauth_client_token>`
**Scope**: Limited to data export operations
**Grant Type**: Client credentials flow

#### 3. User-Scoped Access
**Usage**: Agent policy endpoints when user context available
**Behavior**: Respects user's `allowedAgents` restriction
**Fallback**: Admin authentication required if no user context

### Authorization Middleware

#### `requireAdmin`
- **Purpose**: Standard admin authentication for CRUD operations
- **Validation**: Verifies Bearer token matches admin credentials
- **Error Response**: 401 Unauthorized if token invalid/missing

#### `requireOAuthClient` 
- **Purpose**: OAuth client authentication for integration endpoints
- **Validation**: Validates OAuth2 access token
- **Error Response**: 401/403 for invalid/insufficient credentials

#### User Context Validation
- **Agent Restrictions**: Some endpoints check user's allowed agent list
- **Graceful Fallback**: Falls back to admin requirements if no user context
- **Scope Limitation**: Limits data access to user's permitted resources

### Security Features

#### Request Tracking & Monitoring
- **Correlation ID**: All requests get unique correlation ID for distributed tracing
- **Header**: `X-Correlation-Id` in response for request tracking
- **Logging**: Comprehensive request/response logging with correlation ID

#### Rate Limiting
- **Limit**: 500 requests per 15 minutes per IP address
- **Scope**: Applied to all `/api/` endpoints
- **Key Generation**: Uses `X-Forwarded-For` header for Application Gateway support
- **Bypass**: Health check endpoints (`/health`) excluded from rate limiting

#### CORS Configuration
- **Development**: `origin: '*'` allows all origins
- **Production**: Should be restricted to specific frontend URLs
- **Methods**: GET, POST, PUT, DELETE, OPTIONS
- **Headers**: Content-Type, Authorization, X-User-Id

#### Security Headers (Helmet)
- **X-Content-Type-Options**: `nosniff`
- **X-Frame-Options**: `DENY` 
- **X-XSS-Protection**: `1; mode=block`
- **Strict-Transport-Security**: HSTS for HTTPS enforcement
- **Content-Security-Policy**: Configurable CSP headers

#### Request Size & Processing
- **Body Size Limit**: 10MB JSON body limit
- **Compression**: Gzip compression for responses
- **Parsing**: JSON and URL-encoded body parsing
- **Timeout Handling**: Configurable request timeout limits

### Error Handling Patterns

#### Consistent Error Format
```json
{
  "error": "Human-readable error message",
  "details": "Technical details for debugging", 
  "code": "ERROR_CODE",
  "correlation_id": "uuid"
}
```

#### HTTP Status Codes
- **200 OK**: Successful GET/PUT operations
- **201 Created**: Successful POST operations
- **204 No Content**: Successful DELETE operations
- **400 Bad Request**: Validation errors, malformed requests
- **401 Unauthorized**: Authentication required/failed
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **409 Conflict**: Duplicate resources, constraint violations
- **500 Internal Server Error**: System errors

#### Validation Error Details
```json
{
  "error": "Validation failed",
  "details": [
    {
      "field": "name",
      "message": "is required",
      "value": null
    },
    {
      "field": "severity", 
      "message": "must be one of: low, medium, high, critical",
      "value": "invalid_severity"
    }
  ],
  "code": "VALIDATION_ERROR"
}
```

#### Database Error Handling
- **Safe Messages**: Database errors exposed safely without internal details
- **Constraint Violations**: User-friendly messages for foreign key/unique violations
- **Transaction Errors**: Clear messages for rollback scenarios

#### Deprecation Warnings
- **Headers**: `X-Deprecated: true`, `X-Deprecation-Date: ISO8601`
- **Response Headers**: `X-Replacement: /api/v1/new-endpoint`
- **Documentation**: Clear migration paths in response

### Performance Considerations

#### Query Optimization
- **Efficient Joins**: Optimized SQL queries with proper indexing
- **Pagination**: Cursor and offset-based pagination for large datasets
- **Selective Fields**: Response field selection where appropriate
- **Database Connection Pooling**: PostgreSQL connection pooling

#### Caching Strategies
- **Schema Caching**: JSON schema validation caching
- **Template Caching**: Schema template caching with invalidation
- **Static Data**: Enum and category data caching

#### Response Optimization
- **Compression**: Gzip compression for large responses
- **Selective Inclusion**: Optional field inclusion for bandwidth optimization
- **Batch Operations**: Bulk endpoints for reducing request overhead

---

## Recent Updates & Changes

### 2025-08-31: Policy Status Fix Implementation
**Issue**: Inactive policies incorrectly appeared in agent role policies modal
**Root Cause**: Missing `p.is_active = true` filter in agent role policies API endpoints

**Files Modified**:
- `enhanced-api-project/src/api/policies.js`
- `enhanced-api-project/src/api/agentRolePolicies.js`

**Endpoints Fixed**:
1. `GET /api/v1/policies?agentId=X` - Added active policy filter
2. `GET /api/v1/agents/:id/role-policies` - Added active policy filter  
3. `GET /api/v1/agent-role-policies/:agentId` - Added active policy filter

**SQL Changes**:
```sql
-- Before (returned inactive policies)
WHERE arp.agent_id = $1 AND p.deleted_at IS NULL

-- After (filters inactive policies) 
WHERE arp.agent_id = $1 AND p.deleted_at IS NULL AND p.is_active = true
```

**Impact**: 
- ✅ Agent modals now properly hide inactive policies
- ✅ Policy status toggles work consistently across interfaces
- ✅ Group assignments preserved during status changes

### 2025-08-31: PolicyGroupPoliciesModal Enhancement
**Feature**: Enhanced modal usability with default settings

**Changes**:
- `admin-ui-project/src/components/PolicyGroupPoliciesModal.js`
- `admin-ui-project/src/pages/PolicyGroupsPage.js`

**Enhancements**:
1. **Default Show Inactive**: "Show inactive policies" checkbox checked by default
2. **Live Data Updates**: Modal triggers parent data refresh on policy toggles
3. **Real-time Counts**: Policy group cards update uses count immediately

**Code Changes**:
```javascript
// Default checkbox state
const [showInactive, setShowInactive] = useState(true);

// Parent refresh callback
onPolicyToggle={() => { refetch(); }}

// Post-toggle callback
if (onPolicyToggle) { onPolicyToggle(); }
```

---

## Appendix

### API Endpoint Count by Module
1. **Policies API**: 25+ endpoints (including Rego, blob, violations)
2. **Policy Groups API**: 8 endpoints
3. **Agents API**: 8 endpoints  
4. **Agent Role Policies API**: 8 endpoints (dual endpoint structure)
5. **Agent Policies API**: 1 endpoint (consolidated view)
6. **Roles API**: 4 endpoints
7. **Schemas API**: 6 endpoints
8. **Schema Templates API**: 5 endpoints
9. **Enums API**: 8 endpoints
10. **Metrics API**: 3 endpoints
11. **Chat API**: 4 endpoints
12. **Integrations API**: 1 endpoint (high-volume)
13. **Integrations Admin API**: 1 endpoint
14. **Bulk Assignment API**: 1 endpoint

**Total**: 200+ distinct endpoints across 14 API modules

### Database Integration
- **Primary Database**: PostgreSQL 15+ with JSON schema validation
- **Connection Pooling**: Node.js pg connection pool
- **Stored Procedures**: Complex operations use database stored procedures
- **Transactions**: ACID compliance with transaction support
- **Audit Logging**: HIPAA-compliant audit trails for all operations

### External Integrations
- **Azure OpenAI**: GPT integration for chat functionality
- **Azure Blob Storage**: Rego file storage and retrieval
- **MCP Server**: Model Context Protocol for tool calling
- **OAuth2**: Client credentials flow for integration authentication

### Development & Deployment
- **Environment**: Node.js 20+ with ES modules
- **Framework**: Express.js with comprehensive middleware stack
- **Docker**: Containerized deployment with docker-compose
- **Health Monitoring**: Built-in health checks and metrics endpoints
- **Logging**: Structured logging with correlation ID tracking

---

*This document represents the complete API specification for the Vitea Policy Management System as of August 31, 2025. For the most current API changes and updates, refer to the application source code and recent commit history.*