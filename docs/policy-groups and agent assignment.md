PHASE 0 Kick-off & Logistics (½ day)
0.1 Create tracking issue/epic in GitHub and link the draft PR (#feat/policy-groups-agent-assignment).
0.2 Add checklist of phases below; assign owners; set due dates.
0.3 Spin up a short-lived dev database (or schema branch) for migration tests.
PHASE 1 Database Layer (1–2 days)
1.1 Migration scripts
 • configs/20250805_01__policy_groups.sql – policy_groups, policy_group_policies
 • configs/20250805_02__agent_access.sql – roles, user_roles, agent_access, agent_policies (link_type ENUM)
 • Add FK + ON DELETE CASCADE; add compound indexes listed in design.
1.2 Unit tests (jest + pg-mem) verifying FK & cascade behaviour.
1.3 CI step: run migrations on clean DB container.
Merge ⇒ schema now available for backend dev.
PHASE 2 Backend Auth & Models (1 day)
2.1 Extend enhanced-api-project/src/db.js to expose new tables via knex or query helpers.
2.2 Augment middleware/auth.js
 • After token validation → fetch user’s role_ids → derive allowedAgentIds with single SQL JOIN.
 • Attach req.user.roles, req.user.allowedAgents.
2.3 Add tests: user with/without roles; ensure Agent filtering works.
Merge ⇒ role-based access wired; older routes untouched.
PHASE 3 Core CRUD APIs (1–2 days)
3.1 routes/roles.js – CRUD for roles & user-role mapping.
3.2 routes/policyGroups.js – CRUD for policy groups + group→policy link endpoints.
3.3 routes/agents.js additions
 • GET /agents/:id/policies returns consolidated list w/ assignmentType.
 • POST /agents/:id/access grant/revoke role access.
3.4 routes/bulkAssignments.js – bulk assign policies ↔ agents/groups.
3.5 Swagger / API-doc updates.
Merge ⇒ API complete + Postman collection updated.
PHASE 4 Audit Logging & Metrics (½ day)
4.1 Update log_hipaa_audit_event() to capture new actions.
4.2 Hook calls in all new API controllers.
4.3 Unit tests: verify rows inserted.
PHASE 5 Frontend – State Infrastructure (1 day)
5.1 Create Redux (or Context) slices: roles, policyGroups, agents, assignments.
5.2 API service wrappers for new endpoints; add to Axios base.
Merge ⇒ No UI yet, but network layer ready & passing unit tests.
PHASE 6 UI – Policy List Enhancements (1–2 days)
6.1 Add “Policy Groups” & “Agents” filters (multi-select chips).
6.2 Add “Assignment Type” column with badges (“Direct”, “Via Cardiology Group”).
6.3 Dark-theme styling: reuse card/table CSS classes from Admin UI.
6.4 E2E Cypress tests: filter combinations, duplicate-precedence rule.
PHASE 7 UI – New Pages & Modals (2–3 days)
7.1 Policy Groups page (grid cards, metrics tiles).
7.2 “Agent Registry” additions: access-controlled action buttons.
7.3 “Policy Assignments” dashboard: dual-pane matrix.
7.4 Enhance Create/Edit Policy modal → multi-select group assignment.
7.5 Bulk-assign modal from Policy Table toolbar.
PHASE 8 Analytics Dashboards (1 day)
8.1 Repurpose existing Analytics components to show:
 • Policies per Group, Agents per Group, Orphaned Policies.
 • Compliance coverage bar-charts.
8.2 Wire to new metrics endpoints (can be materialized-view queries).
PHASE 9 QA, Regression & Security (1–2 days)
9.1 Full regression of policy CRUD, clone, soft-delete.
9.2 Role matrix testing (read vs manage).
9.3 Pen-test basic ACL bypass cases.
9.4 Performance: simulate 10 k assignments, verify <150 ms list query.
PHASE 10 Docs & Deployment (½ day)
10.1 Update README.md, API_DOCUMENTATION.md, and ER diagram.
10.2 Create migration run-book; add feature flag toggle (env var).
10.3 Bump version to 1.1.0; draft release notes.
Success / Acceptance Criteria
✓ Admin can create/edit/delete Policy Groups.
✓ Admin can assign/unassign policies to groups (UI + API).
✓ Admin can grant role-based Agent access; users see only allowed agents.
✓ Policy table filters by Agent and/or Group; shows Assignment Type.
✓ Audit log records ROLE_GRANT, GROUP_CHANGE, BULK_ASSIGN.
✓ All existing policy CRUD flows remain intact.
✓ Unit + integration tests ≥ 90 % pass; new E2E tests green.