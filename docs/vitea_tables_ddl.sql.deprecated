--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13
-- Dumped by pg_dump version 16.9 (Ubuntu 16.9-0ubuntu0.24.04.1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: agent_access; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.agent_access (
    agent_id uuid NOT NULL,
    role_id uuid NOT NULL,
    access_level public.access_level_enum DEFAULT 'view'::public.access_level_enum
);


--
-- Name: agent_policies; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.agent_policies (
    agent_id uuid NOT NULL,
    policy_id uuid NOT NULL,
    link_type public.link_type_enum NOT NULL
);


--
-- Name: agents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.agents (
    agent_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    agent_type character varying(100) DEFAULT 'policy_engine'::character varying,
    endpoint_url character varying(500),
    is_active boolean DEFAULT true,
    configuration jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    vendor character varying(255),
    department character varying(255),
    risk_score numeric(4,2) DEFAULT 0.0,
    status public.agent_status_enum DEFAULT 'active'::public.agent_status_enum
);


--
-- Name: policies; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.policies (
    policy_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    category character varying(100) NOT NULL,
    policy_type character varying(50) DEFAULT 'opa'::character varying,
    definition jsonb NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    is_active boolean DEFAULT false,
    severity character varying(20) DEFAULT 'medium'::character varying,
    applies_to_roles text[],
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    updated_by uuid,
    deleted_at timestamp with time zone,
    original_policy_id uuid,
    cloned_from_policy_name character varying(255),
    rego_code text,
    blob_container character varying(255) DEFAULT 'rego-policies'::character varying,
    blob_path character varying(500),
    blob_url character varying(1000),
    rego_template_id character varying(100),
    opa_sync_status character varying(50) DEFAULT 'pending'::character varying,
    last_rego_generation timestamp with time zone,
    rego_generation_error text,
    rego_version integer DEFAULT 1
);


--
-- Name: policy_executions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.policy_executions (
    execution_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    session_id uuid,
    step_id uuid,
    policy_id uuid,
    execution_status character varying(20),
    input_data jsonb,
    output_data jsonb,
    execution_time_ms integer,
    error_message text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: policy_group_policies; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.policy_group_policies (
    group_id uuid NOT NULL,
    policy_id uuid NOT NULL
);


--
-- Name: policy_groups; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.policy_groups (
    group_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    is_template boolean DEFAULT false,
    severity public.severity_level_enum DEFAULT 'medium'::public.severity_level_enum,
    status public.lifecycle_status_enum DEFAULT 'active'::public.lifecycle_status_enum,
    version character varying(20) DEFAULT 'v1.0.0'::character varying,
    tags text[] DEFAULT '{}'::text[],
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    deleted_at timestamp with time zone
);


--
-- Name: policy_templates; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.policy_templates (
    template_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    category character varying(100) NOT NULL,
    template_definition jsonb NOT NULL,
    is_system_template boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid
);


--
-- Name: policy_violations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.policy_violations (
    violation_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    policy_id uuid,
    user_id uuid,
    violation_type character varying(100) NOT NULL,
    details jsonb,
    severity character varying(20),
    resolved boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    resolved_at timestamp with time zone,
    resolved_by uuid
);


--
-- Name: roles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.roles (
    role_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    code character varying(100) NOT NULL,
    name character varying(255),
    description text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: user_roles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_roles (
    user_id uuid NOT NULL,
    role_id uuid NOT NULL
);


--
-- Name: users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.users (
    user_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    azure_ad_id character varying(255) NOT NULL,
    email character varying(255) NOT NULL,
    first_name character varying(100),
    last_name character varying(100),
    role character varying(50) DEFAULT 'user'::character varying NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    updated_by uuid,
    department character varying(255),
    status public.user_status_enum DEFAULT 'active'::public.user_status_enum,
    risk_score numeric(4,2) DEFAULT 0.0,
    last_login timestamp with time zone,
    two_factor_enabled boolean DEFAULT false
);


--
-- Name: agent_access agent_access_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.agent_access
    ADD CONSTRAINT agent_access_pkey PRIMARY KEY (agent_id, role_id);


--
-- Name: agent_policies agent_policies_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.agent_policies
    ADD CONSTRAINT agent_policies_pkey PRIMARY KEY (agent_id, policy_id, link_type);


--
-- Name: agents agents_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.agents
    ADD CONSTRAINT agents_name_key UNIQUE (name);


--
-- Name: agents agents_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.agents
    ADD CONSTRAINT agents_pkey PRIMARY KEY (agent_id);


--
-- Name: policies policies_name_version_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_name_version_key UNIQUE (name, version);


--
-- Name: policies policies_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_pkey PRIMARY KEY (policy_id);


--
-- Name: policy_executions policy_executions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policy_executions
    ADD CONSTRAINT policy_executions_pkey PRIMARY KEY (execution_id);


--
-- Name: policy_group_policies policy_group_policies_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policy_group_policies
    ADD CONSTRAINT policy_group_policies_pkey PRIMARY KEY (group_id, policy_id);


--
-- Name: policy_groups policy_groups_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policy_groups
    ADD CONSTRAINT policy_groups_name_key UNIQUE (name);


--
-- Name: policy_groups policy_groups_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policy_groups
    ADD CONSTRAINT policy_groups_pkey PRIMARY KEY (group_id);


--
-- Name: policy_templates policy_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policy_templates
    ADD CONSTRAINT policy_templates_pkey PRIMARY KEY (template_id);


--
-- Name: policy_violations policy_violations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policy_violations
    ADD CONSTRAINT policy_violations_pkey PRIMARY KEY (violation_id);


--
-- Name: roles roles_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_code_key UNIQUE (code);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (role_id);


--
-- Name: user_roles user_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_pkey PRIMARY KEY (user_id, role_id);


--
-- Name: users users_azure_ad_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_azure_ad_id_key UNIQUE (azure_ad_id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (user_id);


--
-- Name: idx_agent_access_role; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_agent_access_role ON public.agent_access USING btree (role_id);


--
-- Name: idx_agent_policies_direct; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_agent_policies_direct ON public.agent_policies USING btree (agent_id, policy_id) WHERE (link_type = 'direct'::public.link_type_enum);


--
-- Name: idx_agents_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_agents_active ON public.agents USING btree (is_active);


--
-- Name: idx_agents_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_agents_type ON public.agents USING btree (agent_type);


--
-- Name: idx_pg_policies_policy; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_pg_policies_policy ON public.policy_group_policies USING btree (policy_id);


--
-- Name: idx_policies_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_policies_active ON public.policies USING btree (is_active);


--
-- Name: idx_policies_blob_path; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_policies_blob_path ON public.policies USING btree (blob_path);


--
-- Name: idx_policies_category; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_policies_category ON public.policies USING btree (category);


--
-- Name: idx_policies_last_generation; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_policies_last_generation ON public.policies USING btree (last_rego_generation);


--
-- Name: idx_policies_original_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_policies_original_id ON public.policies USING btree (original_policy_id);


--
-- Name: idx_policies_rego_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_policies_rego_status ON public.policies USING btree (opa_sync_status);


--
-- Name: idx_policies_roles; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_policies_roles ON public.policies USING gin (applies_to_roles);


--
-- Name: idx_policies_severity; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_policies_severity ON public.policies USING btree (severity);


--
-- Name: idx_policy_executions_policy_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_policy_executions_policy_id ON public.policy_executions USING btree (policy_id);


--
-- Name: idx_policy_executions_session_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_policy_executions_session_id ON public.policy_executions USING btree (session_id);


--
-- Name: idx_policy_executions_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_policy_executions_status ON public.policy_executions USING btree (execution_status);


--
-- Name: idx_policy_templates_category; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_policy_templates_category ON public.policy_templates USING btree (category);


--
-- Name: idx_users_azure_ad_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_users_azure_ad_id ON public.users USING btree (azure_ad_id);


--
-- Name: idx_users_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_users_email ON public.users USING btree (email);


--
-- Name: idx_users_role; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_users_role ON public.users USING btree (role);


--
-- Name: idx_violations_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_violations_created_at ON public.policy_violations USING btree (created_at);


--
-- Name: idx_violations_policy_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_violations_policy_id ON public.policy_violations USING btree (policy_id);


--
-- Name: idx_violations_resolved; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_violations_resolved ON public.policy_violations USING btree (resolved);


--
-- Name: idx_violations_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_violations_user_id ON public.policy_violations USING btree (user_id);


--
-- Name: policies update_policies_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_policies_updated_at BEFORE UPDATE ON public.policies FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: policy_groups update_policy_groups_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_policy_groups_updated_at BEFORE UPDATE ON public.policy_groups FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: users update_users_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: agent_access agent_access_agent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.agent_access
    ADD CONSTRAINT agent_access_agent_id_fkey FOREIGN KEY (agent_id) REFERENCES public.agents(agent_id) ON DELETE CASCADE;


--
-- Name: agent_access agent_access_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.agent_access
    ADD CONSTRAINT agent_access_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(role_id) ON DELETE CASCADE;


--
-- Name: agent_policies agent_policies_agent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.agent_policies
    ADD CONSTRAINT agent_policies_agent_id_fkey FOREIGN KEY (agent_id) REFERENCES public.agents(agent_id) ON DELETE CASCADE;


--
-- Name: agent_policies agent_policies_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.agent_policies
    ADD CONSTRAINT agent_policies_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id) ON DELETE CASCADE;


--
-- Name: agents agents_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.agents
    ADD CONSTRAINT agents_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: policies policies_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: policies policies_original_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_original_policy_id_fkey FOREIGN KEY (original_policy_id) REFERENCES public.policies(policy_id);


--
-- Name: policies policies_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: policy_executions policy_executions_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policy_executions
    ADD CONSTRAINT policy_executions_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id);


--
-- Name: policy_executions policy_executions_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policy_executions
    ADD CONSTRAINT policy_executions_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.mcp_chat_sessions(session_id);


--
-- Name: policy_executions policy_executions_step_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policy_executions
    ADD CONSTRAINT policy_executions_step_id_fkey FOREIGN KEY (step_id) REFERENCES public.mcp_flow_steps(step_id);


--
-- Name: policy_group_policies policy_group_policies_group_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policy_group_policies
    ADD CONSTRAINT policy_group_policies_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.policy_groups(group_id) ON DELETE CASCADE;


--
-- Name: policy_group_policies policy_group_policies_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policy_group_policies
    ADD CONSTRAINT policy_group_policies_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id) ON DELETE CASCADE;


--
-- Name: policy_groups policy_groups_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policy_groups
    ADD CONSTRAINT policy_groups_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: policy_templates policy_templates_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policy_templates
    ADD CONSTRAINT policy_templates_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: policy_violations policy_violations_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policy_violations
    ADD CONSTRAINT policy_violations_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id);


--
-- Name: policy_violations policy_violations_resolved_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policy_violations
    ADD CONSTRAINT policy_violations_resolved_by_fkey FOREIGN KEY (resolved_by) REFERENCES public.users(user_id);


--
-- Name: policy_violations policy_violations_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.policy_violations
    ADD CONSTRAINT policy_violations_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id);


--
-- Name: user_roles user_roles_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(role_id) ON DELETE CASCADE;


--
-- Name: user_roles user_roles_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE;


--
-- Name: users users_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: users users_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- PostgreSQL database dump complete
--

