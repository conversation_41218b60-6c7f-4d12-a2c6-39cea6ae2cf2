const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const { Pool } = require('pg');
const axios = require('axios');
require('dotenv').config();
const { DefaultAzureCredential } = require("@azure/identity");
const { SecretClient } = require("@azure/keyvault-secrets");

console.log('[Startup] Enhanced API server booting...');

const app = express();

// Security middleware
app.use(helmet());
app.use(cors());
//	{
//  origin: process.env.FRONTEND_URL || '*',
//  credentials: true
//}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 60 * 60 * 1000,
  max: 1000,
  message: 'Too many requests from this IP'
});
app.use('/api/', limiter);

// Body parsing
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Logging
app.use(morgan('combined'));

// Admin authentication middleware (simplified)
const requireAdmin = (req, res, next) => {
  // In production, validate Azure AD JWT token and check roles
  // For now, simple check for admin header
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.includes('admin')) {
    return res.status(401).json({ error: 'Admin access required' });
  }
  req.user = { id: 'admin-user-id', role: 'admin' };
  next();
};

let pool;

async function initializeApp() {
  try {
    console.log(`[Startup] Initializing App in NODE_ENV: ${process.env.NODE_ENV}`);
    let dbPassword = process.env.DB_PASSWORD;

    if (process.env.NODE_ENV === 'production') {
      console.log('[Startup] Production environment detected, fetching secrets from Azure Key Vault...');
      
      const vaultName = process.env.KEY_VAULT_NAME;
      console.log(`[Debug] Using Key Vault Name: ${vaultName}`);
      if (!vaultName) {
        throw new Error("[FATAL] KEY_VAULT_NAME environment variable not set.");
      }
      
      const url = `https://${vaultName}.vault.azure.net`;
      console.log(`[Debug] Key Vault URL: ${url}`);
      
      const credential = new DefaultAzureCredential();
      const client = new SecretClient(url, credential);
      
      console.log('[Startup] Fetching database password from Key Vault...');
      const secret = await client.getSecret("db-admin-password");
      dbPassword = secret.value;
      console.log('[Startup] Successfully fetched database password.');
    }

    console.log(`[Debug] Attempting to connect with DB_HOST: ${process.env.DB_HOST}`);
    const dbUser = 'dbadmin';
    console.log(`[Debug] Using user: ${dbUser}`);
    console.log(`[Debug] Password length: ${dbPassword ? dbPassword.length : 'undefined'}`);

    // Database connection
    pool = new Pool({
      host: process.env.DB_HOST,
      port: 5432,
      database: 'vitea_db',
      user: dbUser,
      password: dbPassword,
      ssl: { rejectUnauthorized: false }
    });

    // Verify connection
    await pool.query('SELECT 1');
    console.log('[Startup] Database pool connected successfully.');

  } catch (err) {
    console.error('[FATAL] App initialization failed:', err.message);
    if (err.stack) {
        console.error('[FATAL] Error Stack:', err.stack);
    }
    if(err.details) {
        console.error('[FATAL] Error Details:', err.details);
    }
    process.exit(1);
  }
}

// Health check endpoint
//app.get('/health', (req, res) => {
//res.status(200).json({ status: 'alive', timestamp: new Date().toISOString()});
//});		

app.get('/health', async (req, res) => {
  try {
    // Test database connection
    console.log('[Health] Starting check...');
    const dbTest = await pool.query('SELECT 1');
    console.log('[Health] DB check passed:', dbTest);
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: '2.0.0',
      services: {
        database: dbTest.rows ? 'connected' : 'disconnected',
        azureOpenAI: process.env.AZURE_OPENAI_ENDPOINT ? 'configured' : 'not_configured'
      }
    });
  } catch (err) {
    console.error('Health check error:', err);
    res.status(500).json({ error: 'Health check failed' });
  }
});

// Original test endpoint
app.get('/api/v1/test', (req, res) => {
  res.json({ 
    message: 'Enhanced Vitea API is running', 
    version: '2.0.0',
    features: ['policy_management', 'mcp_flow', 'azure_openai'],
    timestamp: new Date().toISOString()
  });
});

// ===== POLICY MANAGEMENT ENDPOINTS =====

// Get all policies with filtering
app.get('/api/v1/policies', requireAdmin, async (req, res) => {
  try {
    const { active, category, severity } = req.query;
    
    let query = 'SELECT * FROM policies WHERE 1=1';
    const params = [];
    
    if (active !== undefined) {
      query += ' AND is_active = $' + (params.length + 1);
      params.push(active === 'true');
    }
    
    if (category) {
      query += ' AND category = $' + (params.length + 1);
      params.push(category);
    }
    
    if (severity) {
      query += ' AND severity = $' + (params.length + 1);
      params.push(severity);
    }
    
    query += ' ORDER BY created_at DESC';
    
    const result = await pool.query(query, params);
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching policies:', err);
    res.status(500).json({ error: 'Failed to fetch policies' });
  }
});

// Create new policy
app.post('/api/v1/policies', requireAdmin, async (req, res) => {
  try {
    const { name, description, category, definition, severity, applies_to_roles } = req.body;
    
    const result = await pool.query(
      `INSERT INTO policies (name, description, category, definition, severity, applies_to_roles, created_by) 
       VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`,
      [name, description, category, definition, severity, applies_to_roles, req.user.id]
    );
    
    res.status(201).json(result.rows[0]);
  } catch (err) {
    console.error('Error creating policy:', err);
    res.status(500).json({ error: 'Failed to create policy' });
  }
});

// Update policy
app.put('/api/v1/policies/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, category, definition, is_active, severity, applies_to_roles } = req.body;
    
    const result = await pool.query(
      `UPDATE policies SET name = $1, description = $2, category = $3, definition = $4, 
       is_active = $5, severity = $6, applies_to_roles = $7, updated_by = $8, updated_at = CURRENT_TIMESTAMP
       WHERE policy_id = $9 RETURNING *`,
      [name, description, category, definition, is_active, severity, applies_to_roles, req.user.id, id]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Policy not found' });
    }
    
    res.json(result.rows[0]);
  } catch (err) {
    console.error('Error updating policy:', err);
    res.status(500).json({ error: 'Failed to update policy' });
  }
});

// Delete policy
app.delete('/api/v1/policies/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await pool.query(
      'DELETE FROM policies WHERE policy_id = $1 RETURNING *',
      [id]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Policy not found' });
    }
    
    res.json({ message: 'Policy deleted successfully' });
  } catch (err) {
    console.error('Error deleting policy:', err);
    res.status(500).json({ error: 'Failed to delete policy' });
  }
});

// Get policy violations
app.get('/api/v1/violations', requireAdmin, async (req, res) => {
  try {
    const { policy_id, resolved, limit = 50 } = req.query;
    
    let query = `
      SELECT pv.*, p.name as policy_name, u.first_name, u.last_name, u.email
      FROM policy_violations pv
      LEFT JOIN policies p ON pv.policy_id = p.policy_id
      LEFT JOIN users u ON pv.user_id = u.user_id
      WHERE 1=1
    `;
    const params = [];
    
    if (policy_id) {
      query += ' AND pv.policy_id = $' + (params.length + 1);
      params.push(policy_id);
    }
    
    if (resolved !== undefined) {
      query += ' AND pv.resolved = $' + (params.length + 1);
      params.push(resolved === 'true');
    }
    
    query += ' ORDER BY pv.created_at DESC LIMIT $' + (params.length + 1);
    params.push(parseInt(limit));
    
    const result = await pool.query(query, params);
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching violations:', err);
    res.status(500).json({ error: 'Failed to fetch violations' });
  }
});

// Get policy templates
app.get('/api/v1/policy-templates', requireAdmin, async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT * FROM policy_templates ORDER BY is_system_template DESC, created_at DESC'
    );
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching policy templates:', err);
    res.status(500).json({ error: 'Failed to fetch policy templates' });
  }
});

// Get system metrics for dashboard
app.get('/api/v1/metrics', requireAdmin, async (req, res) => {
  try {
    // Get current metrics
    const metricsResult = await pool.query(
      'SELECT * FROM system_metrics ORDER BY recorded_at DESC'
    );
    
    // Calculate real-time stats
    const statsQueries = await Promise.all([
      pool.query('SELECT COUNT(*) as total_policies FROM policies'),
      pool.query('SELECT COUNT(*) as active_policies FROM policies WHERE is_active = true'),
      pool.query('SELECT COUNT(*) as critical_policies FROM policies WHERE severity = \'critical\''),
      pool.query('SELECT COUNT(*) as total_violations FROM policy_violations'),
      pool.query('SELECT COUNT(*) as unresolved_violations FROM policy_violations WHERE resolved = false'),
      pool.query('SELECT COUNT(*) as chat_sessions_today FROM mcp_chat_sessions WHERE created_at >= CURRENT_DATE'),
    ]);
    
    const stats = {
      total_policies: parseInt(statsQueries[0].rows[0].total_policies),
      active_policies: parseInt(statsQueries[1].rows[0].active_policies),
      critical_policies: parseInt(statsQueries[2].rows[0].critical_policies),
      total_violations: parseInt(statsQueries[3].rows[0].total_violations),
      unresolved_violations: parseInt(statsQueries[4].rows[0].unresolved_violations),
      chat_sessions_today: parseInt(statsQueries[5].rows[0].chat_sessions_today)
    };
    
    res.json({
      metrics: metricsResult.rows,
      current_stats: stats,
      last_updated: new Date().toISOString()
    });
  } catch (err) {
    console.error('Error fetching metrics:', err);
    res.status(500).json({ error: 'Failed to fetch metrics' });
  }
});

// ===== MCP CHAT FLOW ENDPOINTS =====

// Start new chat session
app.post('/api/v1/chat/session', async (req, res) => {
  try {
    // In production, get user from JWT token
    const userId = req.headers['x-user-id'] || 'demo-user-id';
    
    const result = await pool.query(
      'INSERT INTO mcp_chat_sessions (user_id, metadata) VALUES ($1, $2) RETURNING *',
      [userId, req.body.metadata || {}]
    );
    
    res.status(201).json(result.rows[0]);
  } catch (err) {
    console.error('Error creating chat session:', err);
    res.status(500).json({ error: 'Failed to create chat session' });
  }
});

// Process chat message through MCP flow
app.post('/api/v1/chat/:sessionId/message', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { message } = req.body;
    
    // Step 1: User submits query
    await createFlowStep(sessionId, 1, 'User submits query', { message });
    
    // Step 2: Get active policies
    const activePolicies = await pool.query(
      'SELECT * FROM policies WHERE is_active = true'
    );
    await createFlowStep(sessionId, 2, 'Get active policies', { 
      policies_count: activePolicies.rows.length 
    });
    
    // Step 3: Policy rules retrieved
    await createFlowStep(sessionId, 3, 'Policy rules retrieved', { 
      policies: activePolicies.rows.map(p => ({ id: p.policy_id, name: p.name }))
    });
    
    // Step 4: Pre-process request (basic sanitization)
    const sanitizedMessage = message.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    await createFlowStep(sessionId, 4, 'Pre-process request', { 
      original_length: message.length,
      sanitized_length: sanitizedMessage.length 
    });
    
    // Step 5: Sanitized request to AI backend (mock Azure OpenAI call)
    const aiResponse = await mockAzureOpenAICall(sanitizedMessage);
    await createFlowStep(sessionId, 5, 'Sanitized request to chatbot', { 
      prompt_tokens: aiResponse.usage?.prompt_tokens || 0,
      completion_tokens: aiResponse.usage?.completion_tokens || 0
    });
    
    // Step 6: Route through gateway (mock)
    await createFlowStep(sessionId, 6, 'Route through gateway', { routed: true });
    
    // Step 7: MCP invokes APIs (mock)
    await createFlowStep(sessionId, 7, 'MCP invokes APIs', { apis_called: ['policy_engine', 'content_filter'] });
    
    // Step 8: Database queries (mock)
    await createFlowStep(sessionId, 8, 'Database queries', { queries_executed: 3 });
    
    // Step 9: Raw data returned (mock)
    const rawResponse = aiResponse.choices?.[0]?.message?.content || "This is a mock AI response for testing.";
    await createFlowStep(sessionId, 9, 'Raw data returned', { response_length: rawResponse.length });
    
    // Step 10: Policy filtering
    const filteredResponse = await applyPolicyFiltering(rawResponse, activePolicies.rows);
    await createFlowStep(sessionId, 10, 'Policy filtering', { 
      policies_applied: filteredResponse.policies_applied.length,
      content_modified: filteredResponse.modified
    });
    
    // Step 11: Filtered response
    await createFlowStep(sessionId, 11, 'Filtered response', { 
      final_response: filteredResponse.content 
    });
    
    // Step 12: UI rendering
    await createFlowStep(sessionId, 12, 'UI rendering', { rendered: true });
    
    // Store the chat message
    await pool.query(
      `INSERT INTO chat_messages (session_id, role, content, original_content, policies_applied, is_filtered) 
       VALUES ($1, $2, $3, $4, $5, $6)`,
      [sessionId, 'user', message, message, [], false]
    );
    
    await pool.query(
      `INSERT INTO chat_messages (session_id, role, content, original_content, policies_applied, is_filtered) 
       VALUES ($1, $2, $3, $4, $5, $6)`,
      [sessionId, 'assistant', filteredResponse.content, rawResponse, filteredResponse.policies_applied, filteredResponse.modified]
    );
    
    res.json({
      message: filteredResponse.content,
      metadata: {
        policies_applied: filteredResponse.policies_applied.length,
        content_filtered: filteredResponse.modified,
        processing_steps: 12
      }
    });
    
  } catch (err) {
    console.error('Error processing chat message:', err);
    res.status(500).json({ error: 'Failed to process message' });
  }
});

// Get chat session history
app.get('/api/v1/chat/:sessionId/history', async (req, res) => {
  try {
    const { sessionId } = req.params;
    
    const result = await pool.query(
      'SELECT * FROM chat_messages WHERE session_id = $1 ORDER BY created_at ASC',
      [sessionId]
    );
    
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching chat history:', err);
    res.status(500).json({ error: 'Failed to fetch chat history' });
  }
});

// Get MCP flow steps for a session
app.get('/api/v1/chat/:sessionId/flow', requireAdmin, async (req, res) => {
  try {
    const { sessionId } = req.params;
    
    const result = await pool.query(
      'SELECT * FROM mcp_flow_steps WHERE session_id = $1 ORDER BY step_number ASC',
      [sessionId]
    );
    
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching MCP flow:', err);
    res.status(500).json({ error: 'Failed to fetch MCP flow' });
  }
});

// ===== HELPER FUNCTIONS =====

async function createFlowStep(sessionId, stepNumber, stepName, inputData) {
  const start = Date.now();
  
  try {
    const result = await pool.query(
      `INSERT INTO mcp_flow_steps (session_id, step_number, step_name, status, input_data, processing_time_ms, completed_at) 
       VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP) RETURNING *`,
      [sessionId, stepNumber, stepName, 'completed', inputData, Date.now() - start]
    );
    
    return result.rows[0];
  } catch (err) {
    console.error('Error creating flow step:', err);
    throw err;
  }
}

async function mockAzureOpenAICall(message) {
  // Mock Azure OpenAI response
  // In production, replace with actual Azure OpenAI API call
  return {
    choices: [{
      message: {
        content: `This is a mock response to: "${message}". In a real implementation, this would be generated by Azure OpenAI based on the user's query and would include relevant healthcare information while respecting privacy policies.`
      }
    }],
    usage: {
      prompt_tokens: message.length / 4, // rough estimate
      completion_tokens: 50,
      total_tokens: (message.length / 4) + 50
    }
  };
}

async function applyPolicyFiltering(content, policies) {
  let filteredContent = content;
  let modified = false;
  const appliedPolicies = [];
  
  for (const policy of policies) {
    try {
      const definition = policy.definition;
      
      // Apply different policy types
      if (definition.type === 'data_masking' && definition.regex_patterns) {
        for (const pattern of definition.regex_patterns) {
          const regex = new RegExp(pattern, 'gi');
          if (regex.test(filteredContent)) {
            filteredContent = filteredContent.replace(regex, '[REDACTED]');
            modified = true;
            appliedPolicies.push(policy.policy_id);
          }
        }
      }
      
      if (definition.type === 'content_filtering' && definition.blocked_categories) {
        // Simple keyword-based filtering (in production, use ML models)
        const harmfulKeywords = ['violence', 'hate', 'harm'];
        for (const keyword of harmfulKeywords) {
          if (filteredContent.toLowerCase().includes(keyword)) {
            filteredContent = filteredContent.replace(new RegExp(keyword, 'gi'), '[FILTERED]');
            modified = true;
            appliedPolicies.push(policy.policy_id);
          }
        }
      }
      
    } catch (err) {
      console.error('Error applying policy:', policy.name, err);
    }
  }
  
  return {
    content: filteredContent,
    modified,
    policies_applied: appliedPolicies
  };
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({ 
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

try {
  const PORT = process.env.PORT || 8000;
  initializeApp().then(() => {
    app.listen(PORT, () => {
      console.log(`Enhanced Vitea API server running on port ${PORT}`);
      console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`Health check: http://localhost:${PORT}/health`);
      console.log(`Features: Policy Management, MCP Flow, Azure OpenAI Integration`);
    });
  });
} catch (e) {
	console.error('[Fatal] App failed to start:', e);
}	
//module.exports = app;
