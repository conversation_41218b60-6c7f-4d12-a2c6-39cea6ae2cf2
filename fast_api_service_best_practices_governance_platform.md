# FastAPI Service Best Practices (Governance Platform)

> **Audience:** Engineers building FastAPI-based microservices for our governance platform (Python + Postgres).  
> **Goals:** Consistent project structure, predictable migrations, clear separation of concerns, secure defaults, and smooth migrations from Node.js/Express.

---

## 1) Architecture & Principles

- **Separation of concerns:**
  - **DB models & persistence:** SQLAlchemy 2.0
  - **Schema validation & I/O:** Pydantic v2
  - **Migrations:** Alembic
  - **API surface:** FastAPI routers
- **12‑Factor app:** config via env vars; stateless services; externalize state (Postgres/Redis).
- **Security-first defaults:** input validation, authz/authn at the edge, least privilege DB roles, secrets via vault.
- **Async when needed:** prefer sync SQLAlchemy for simplicity unless concurrency/IO justifies async; be consistent per service.
- **One DB per service** (logical). Keep migration histories isolated unless a shared DB is explicitly required.

---

## 2) Tech Stack & Versions (baseline)

- Python ≥ 3.11
- FastAPI ≥ 0.111
- SQLAlchemy 2.x
- Alembic ≥ 1.13
- Pydantic v2
- Postgres ≥ 14
- Uvicorn (or Gunicorn+Uvicorn workers) for production
- Optional: Redis (caching/rate limiting), HTTPX (service calls), Loguru/structlog, pytest + httpx[cli]

**Install (sync example):**
```bash
pip install fastapi uvicorn "sqlalchemy>=2" psycopg alembic "pydantic>=2" python-dotenv httpx loguru pytest
```

**Install (async variant):**
```bash
pip install fastapi uvicorn "sqlalchemy[asyncio]>=2" asyncpg alembic "pydantic>=2"
```

---

## 3) Project Layout (per service)

```
service-x/
  app/
    api/
      v1/
        __init__.py
        routers/
          __init__.py
          policies.py
      deps.py           # FastAPI dependencies (db session, auth, rate limiters)
      errors.py         # exception handlers
    core/
      config.py         # settings via pydantic Settings
      logging.py        # structured logging config
      security.py       # auth helpers (JWT/OAuth2), scopes
    db/
      models.py         # SQLAlchemy models (Base)
      session.py        # engine + session
      seed.py           # optional seed scripts (idempotent)
    schemas/
      policy.py         # Pydantic schemas for I/O
    services/
      policy_svc.py     # business logic services
    main.py             # FastAPI app factory
  migrations/
    env.py
    versions/
  alembic.ini
  tests/
    api/
    services/
  pyproject.toml or requirements.txt
  Dockerfile
  README.md
```

**Why this layout?**
- Clear split: `db` (persistence), `schemas` (I/O), `services` (domain logic), `api` (transport).
- Allows thin routers and testable business logic.

---

## 4) ORM vs Pydantic: When to Use What

| Use Case | Choose | Rationale |
|---|---|---|
| Table definitions, relationships, constraints, indexes | **SQLAlchemy models** | Source of truth for DB + Alembic autogeneration |
| CRUD & transactions | **SQLAlchemy Session** | Proper unit-of-work, transaction mgmt |
| Request validation (body/query/path), response shaping | **Pydantic models** | Strong typing, security (don’t leak internal fields) |
| Migrations (DDL changes) | **Alembic** | Versioned, reviewable scripts |
| Data migrations/backfills | **Alembic revision (Python/SQL)** | Repeatable, auditable |

**Rule:** Do not use Pydantic models to represent DB tables. Keep them separate. If a unified model is desired, consider **SQLModel** (trade-offs apply).

---

## 5) Database & Migrations

### 5.1 Modeling
```python
# app/db/models.py
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
from sqlalchemy import String, Integer, ForeignKey, text
from datetime import datetime

class Base(DeclarativeBase):
    pass

class Policy(Base):
    __tablename__ = "policies"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    name: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    created_at: Mapped[datetime] = mapped_column(server_default=text("now()"))

class Rule(Base):
    __tablename__ = "rules"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    policy_id: Mapped[int] = mapped_column(ForeignKey("policies.id", ondelete="CASCADE"))
    expr: Mapped[str] = mapped_column(String(255), nullable=False)
    policy = relationship(Policy, backref="rules")
```

### 5.2 Sessions (sync example)
```python
# app/db/session.py
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings

engine = create_engine(settings.DATABASE_URL, pool_pre_ping=True)
SessionLocal = sessionmaker(bind=engine, autoflush=False, autocommit=False)
```

### 5.3 Pydantic Schemas (I/O)
```python
# app/schemas/policy.py
from pydantic import BaseModel, Field

class RuleIn(BaseModel):
    expr: str = Field(min_length=1, max_length=255)

class PolicyCreate(BaseModel):
    name: str
    rules: list[RuleIn] = []

class RuleOut(BaseModel):
    id: int
    expr: str

class PolicyOut(BaseModel):
    id: int
    name: str
    rules: list[RuleOut]
```

### 5.4 Alembic Setup & Commands

- Init: `alembic init migrations`
- Wire `migrations/env.py` to `Base.metadata`
- Set `sqlalchemy.url` in `alembic.ini`
- Create migration: `alembic revision --autogenerate -m "add rules"`
- Apply: `alembic upgrade head`
- Roll back one: `alembic downgrade -1`

**Policies:**
- A schema change **must** ship with a migration in the same PR.
- Review `--autogenerate` output; never commit unreviewed diffs.
- Prefer **expand/contract** for breaking changes:
  1) Expand: add nullable/new columns, backfill
  2) Deploy code using new schema
  3) Contract: drop old columns in later release
- In production, create large indexes **concurrently** or off-peak.

---

## 6) FastAPI Patterns

### 6.1 App Factory & Dependencies
```python
# app/main.py
from fastapi import FastAPI
from app.api.v1.routers import policies
from app.api.errors import register_error_handlers

app = FastAPI(title="Governance Service")
app.include_router(policies.router, prefix="/api/v1")
register_error_handlers(app)
```

```python
# app/api/deps.py
from contextlib import contextmanager
from fastapi import Depends
from app.db.session import SessionLocal

@contextmanager
def get_db():
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()
```

### 6.2 Routers (thin) & Services (thick)
```python
# app/api/v1/routers/policies.py
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.api.deps import get_db
from app.schemas.policy import PolicyCreate, PolicyOut
from app.services.policy_svc import create_policy, get_policy

router = APIRouter(tags=["policies"])

@router.post("/policies", response_model=PolicyOut, status_code=201)
def create(policy: PolicyCreate, db: Session = Depends(get_db)):
    return create_policy(db, policy)

@router.get("/policies/{policy_id}", response_model=PolicyOut)
def read(policy_id: int, db: Session = Depends(get_db)):
    obj = get_policy(db, policy_id)
    if not obj:
        raise HTTPException(404, "Not found")
    return obj
```

```python
# app/services/policy_svc.py
from sqlalchemy.orm import Session
from app.db import models
from app.schemas.policy import PolicyCreate

def create_policy(db: Session, payload: PolicyCreate):
    obj = models.Policy(name=payload.name)
    for r in payload.rules:
        obj.rules.append(models.Rule(expr=r.expr))
    db.add(obj)
    db.flush()  # assign IDs
    db.refresh(obj)
    return obj

def get_policy(db: Session, policy_id: int):
    return db.query(models.Policy).filter(models.Policy.id == policy_id).first()
```

### 6.3 Error Handling & Validation
- Centralize exception handlers (`app/api/errors.py`).
- Use Pydantic constraints for inputs; never trust client-provided IDs without server-side checks.

---

## 7) Configuration & Secrets

- Use `pydantic-settings` or Pydantic v2 `BaseSettings` in `core/config.py`.
- All secrets (DB password, tokens) via environment/Vault. Example:
```python
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    DATABASE_URL: str
    ENV: str = "dev"

    class Config:
        env_file = ".env"

settings = Settings()
```

---

## 8) Testing Strategy

- **pytest** as the test runner
- **Factories/fixtures** for db session (transactional tests), HTTP client (FastAPI TestClient/HTTPX)
- Use a **throwaway Postgres** (Testcontainers) for integration tests
- Seed data via `db/seed.py` (idempotent)

---

## 9) Logging, Metrics, Tracing

- **Structured logs** (JSON) with request ID and user ID when available.
- Export metrics via **Prometheus** (Starlette middleware or custom endpoint).
- **OpenTelemetry** for tracing; propagate trace IDs through service calls.

---

## 10) Performance & Reliability

- Prefer **bulk operations** for batch writes (`session.execute(many)`).
- Tune **connection pool** sizes; enable `pool_pre_ping`.
- Add **timeouts** and **retries** for outbound HTTP calls.
- Use **read-only DB roles** for GET-heavy services.

---

## 11) CI/CD & Runtime

- Lint/format: ruff + black; type-check: mypy/pyright.
- Build once; promote artifact across envs.
- Run DB migrations as a **separate job** (K8s Job/InitContainer) before app rollout:
```bash
alembic upgrade head
```
- Health probes: `/healthz` (liveness) and `/readyz` (readiness, checks DB).

---

## 12) Security Checklist (API & Data)

- Enforce auth (OIDC/JWT) and scopes on routers; avoid per‑handler ad‑hoc checks.
- Validate all inputs (Pydantic); never trust IDs, enforce ownership/tenancy.
- Output schemas must **omit internal fields** (e.g., secrets, system notes, deleted flags).
- Use parameterized queries only (SQLAlchemy default).
- Encrypt in transit (TLS) and at rest (cloud-managed KMS).
- Principle of least privilege for DB users; separate migration role from app role.

---

## 13) Node.js/Express → FastAPI Migration Guide

1. **Inventory & carve out**: list routes, middlewares, models, data flows.
2. **Define schemas**: translate Swagger/TS interfaces → Pydantic models.
3. **DB layer**: map Sequelize/knex models → SQLAlchemy models; port migrations → Alembic.
4. **Router parity**: recreate route paths/verbs; aim to preserve external contracts.
5. **Auth**: map Express middlewares → FastAPI dependencies (JWT/OAuth2 scopes).
6. **Business logic**: move to `services/` and write unit tests.
7. **Observability**: replicate logs/metrics; add traces.
8. **Cutover plan**: shadow traffic, canary, rollback path. Data migrations tested on prod snapshot.

**Common pitfalls:** implicit JSON shape changes, date/time parsing differences, 1‑based vs 0‑based pagination, error code drift.

---

## 14) Style & Code Quality

- Use **type hints everywhere**; `from __future__ import annotations` for forward refs.
- Keep routers thin; logic lives in `services/`.
- One transaction per request by default; long workflows use sagas or jobs.
- Avoid global state; prefer dependency injection and context‑managed sessions.

---

## 15) Templates & Snippets

**App startup (Uvicorn):**
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8080
```

**Dockerfile (slim example):**
```dockerfile
FROM python:3.12-slim
WORKDIR /app
COPY pyproject.toml poetry.lock* requirements.txt* ./
RUN pip install --no-cache-dir -r requirements.txt || true \
 && pip install --no-cache-dir poetry || true \
 && (poetry install --no-root || true)
COPY . .
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080"]
```

**K8s migration Job (conceptual):**
```yaml
apiVersion: batch/v1
kind: Job
metadata: { name: service-x-migrate }
spec:
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: alembic
        image: our-registry/service-x:SHA
        command: ["alembic", "upgrade", "head"]
        envFrom:
        - secretRef: { name: service-x-secrets }
```

---

## 16) Governance-Specific Notes

- Sensitive data (PHI/PII) fields must be **explicitly identified** in DB models; avoid returning them in any response schema.
- Add **audit tables** (append-only) for key entities; use triggers or app-level inserts.
- Consider **soft-deletes** (deleted_at) with filtered unique indexes.
- Policy/evaluation services should emit structured events to the observability pipeline.

---

## 17) Glossary & Quick Commands

- Create migration: `alembic revision --autogenerate -m "msg"`
- Apply migrations: `alembic upgrade head`
- Downgrade last: `alembic downgrade -1`
- New app route: add to `api/v1/routers/*.py` and include in `main.py`
- Generate OpenAPI: FastAPI serves `/openapi.json`; add `/docs` & `/redoc` for dev

---

## 18) FAQs

- **Can I reuse Pydantic models for DB?** No—use SQLAlchemy for DB, Pydantic for I/O. If you want a unified approach, evaluate SQLModel.
- **Async or sync?** Default to sync unless you have IO-bound workloads that justify async. Avoid mixing within the same service.
- **Shared DB across services?** Avoid. Prefer service-owned schemas. If you must share, implement change control and versioned contracts.

---

**Last updated:** <fill date>

