#!/bin/bash

# E2E Test Runner for Vitea
# Ensures dependencies are installed and runs the distributed e2e test

set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[E2E]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "🧪 Vitea E2E Test Runner"
echo "========================"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    error "Python 3 is required but not installed"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    warn ".env file not found. Creating from .env.example..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        log "Created .env from .env.example"
        log "You may want to edit .env with your specific configuration"
    else
        error ".env.example not found. Cannot create default configuration."
        exit 1
    fi
fi

# Install Python dependencies if needed
log "Checking Python dependencies..."
if ! python3 -c "import requests, dotenv" &> /dev/null; then
    log "Installing Python dependencies..."
    if command -v pip3 &> /dev/null; then
        pip3 install -r requirements-e2e.txt
    elif command -v pip &> /dev/null; then
        pip install -r requirements-e2e.txt
    else
        error "pip not found. Please install the dependencies manually:"
        error "pip install requests python-dotenv"
        exit 1
    fi
    log "Dependencies installed successfully"
else
    log "Dependencies already satisfied"
fi

# Run the test
log "Starting E2E test..."
echo ""

# Pass through any command line arguments (like --auto)
python3 e2e-test-distributed.py "$@"