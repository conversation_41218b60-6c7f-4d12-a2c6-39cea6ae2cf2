#!/bin/bash
# CRITICAL: Complete System Backup Script
# RUN THIS BEFORE ANY DEPLOYMENT!

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}ℹ️  INFO: $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  WARNING: $1${NC}"
}

log_error() {
    echo -e "${RED}❌ ERROR: $1${NC}"
}

log_step() {
    echo ""
    echo -e "${BLUE}📋 STEP: $1${NC}"
    echo "----------------------------------------"
}

echo "🛡️ VITEA SYSTEM BACKUP - CRITICAL SAFETY PROCEDURE"
echo "=================================================="
echo "This will create a complete backup of your existing system"
echo "so you can safely revert if the enhancement fails."
echo ""

# Check if we're in the right directory
if [ ! -f "../configs/environment.conf" ]; then
    log_error "environment.conf not found. Please run from the scripts directory."
    exit 1
fi

# Load environment
source ../configs/environment.conf

# Create backup directory with timestamp
BACKUP_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="../backups/system_backup_$BACKUP_TIMESTAMP"
mkdir -p "$BACKUP_DIR"

log_info "Backup directory: $BACKUP_DIR"

log_step "1. Backing up Database"
export PGPASSWORD="$DB_PASSWORD"

# Full database backup
log_info "Creating complete database backup..."
pg_dump -h "${DB_HOST}" \
        -p 5432 \
        -d vitea_db \
        -U dbadmin \
        --verbose \
        --no-password \
        --format=custom \
        --file="$BACKUP_DIR/vitea_database_complete.backup"

# Also create SQL backup for easy inspection
pg_dump -h "${DB_HOST}" \
        -p 5432 \
        -d vitea_db \
        -U dbadmin \
        --no-password \
        --format=plain \
        --file="$BACKUP_DIR/vitea_database_complete.sql"

log_success "Database backup completed"

log_step "2. Backing up Existing API Source Code"
if [ -d "../api-project" ]; then
    cp -r ../api-project "$BACKUP_DIR/api-project-original"
    log_success "API source code backed up"
else
    log_warning "API project directory not found"
fi

log_step "3. Backing up Existing Frontend Source Code"
if [ -d "../frontend-project" ]; then
    cp -r ../frontend-project "$BACKUP_DIR/frontend-project-original"
    log_success "Frontend source code backed up"
else
    log_warning "Frontend project directory not found"
fi

log_step "4. Backing up Configuration Files"
if [ -d "../configs" ]; then
    cp -r ../configs "$BACKUP_DIR/configs-original"
    log_success "Configuration files backed up"
else
    log_warning "Configs directory not found"
fi

log_step "5. Backing up Azure App Service Configuration"
log_info "Backing up Azure App Service settings..."

# Backup API app settings
az webapp config appsettings list \
    --resource-group "$RESOURCE_GROUP" \
    --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
    --output json > "$BACKUP_DIR/api-app-settings.json"

# Backup API app configuration
az webapp config show \
    --resource-group "$RESOURCE_GROUP" \
    --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
    --output json > "$BACKUP_DIR/api-app-config.json"

# Backup frontend app settings if it exists
if az webapp show --resource-group "$RESOURCE_GROUP" --name "${COMPANY_NAME}-${ENVIRONMENT}-frontend" > /dev/null 2>&1; then
    az staticwebapp show \
        --name "${COMPANY_NAME}-${ENVIRONMENT}-frontend" \
        --resource-group "$RESOURCE_GROUP" \
        --output json > "$BACKUP_DIR/frontend-app-config.json"
fi

log_success "Azure configuration backed up"

log_step "6. Backing up Database Schema Definition"
log_info "Extracting current database schema..."

# Backup current schema structure only
pg_dump -h "${DB_HOST}" \
        -p 5432 \
        -d vitea_db \
        -U dbadmin \
        --no-password \
        --schema-only \
        --file="$BACKUP_DIR/current_schema_only.sql"

# Backup current data only (for rollback)
pg_dump -h "${DB_HOST}" \
        -p 5432 \
        -d vitea_db \
        -U dbadmin \
        --no-password \
        --data-only \
        --file="$BACKUP_DIR/current_data_only.sql"

log_success "Database schema and data backed up separately"

log_step "7. Creating Backup Manifest"
cat > "$BACKUP_DIR/backup_manifest.txt" << EOF
VITEA SYSTEM BACKUP MANIFEST
============================
Backup Date: $(date)
Backup Directory: $BACKUP_DIR
Environment: $ENVIRONMENT
Resource Group: $RESOURCE_GROUP
Database Host: $DB_HOST

FILES INCLUDED:
- vitea_database_complete.backup (PostgreSQL custom format)
- vitea_database_complete.sql (SQL format)
- current_schema_only.sql (Schema structure)
- current_data_only.sql (Data only)
- api-project-original/ (Original API source)
- frontend-project-original/ (Original frontend source)
- configs-original/ (Original configuration)
- api-app-settings.json (Azure API app settings)
- api-app-config.json (Azure API app configuration)
- frontend-app-config.json (Azure frontend configuration)

ROLLBACK INSTRUCTIONS:
1. Run: ../scripts/00-rollback-system.sh $BACKUP_TIMESTAMP
2. Or manually restore from this backup directory

CURRENT SYSTEM STATE:
Database Tables: $(PGPASSWORD="$DB_PASSWORD" psql -h "${DB_HOST}" -p 5432 -d vitea_db -U dbadmin -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" --set sslmode=require | xargs)
API Version: $(curl -s "https://${COMPANY_NAME}-${ENVIRONMENT}-api.azurewebsites.net/api/v1/test" | grep -o '"version":"[^"]*"' | cut -d'"' -f4 2>/dev/null || echo "Unable to determine")
EOF

log_success "Backup manifest created"

log_step "8. Validating Backup Integrity"
# Verify database backup
if [ -f "$BACKUP_DIR/vitea_database_complete.backup" ]; then
    BACKUP_SIZE=$(stat -f%z "$BACKUP_DIR/vitea_database_complete.backup" 2>/dev/null || stat -c%s "$BACKUP_DIR/vitea_database_complete.backup")
    if [ "$BACKUP_SIZE" -gt 1000 ]; then
        log_success "Database backup file size: $BACKUP_SIZE bytes"
    else
        log_error "Database backup seems too small: $BACKUP_SIZE bytes"
        exit 1
    fi
else
    log_error "Database backup file not found!"
    exit 1
fi

# Verify source code backups
if [ -d "$BACKUP_DIR/api-project-original" ] && [ -d "$BACKUP_DIR/frontend-project-original" ]; then
    log_success "Source code backups verified"
else
    log_warning "Some source code backups may be missing"
fi

log_step "9. Creating Quick Restore Script"
cat > "$BACKUP_DIR/quick_restore.sh" << EOF
#!/bin/bash
# Quick restore script for backup: $BACKUP_TIMESTAMP
set -e

echo "🔄 RESTORING VITEA SYSTEM FROM BACKUP: $BACKUP_TIMESTAMP"
echo "======================================================"

# Navigate to project root
cd \$(dirname \$0)/../../

# Restore database
echo "Restoring database..."
export PGPASSWORD="$DB_PASSWORD"
pg_restore -h "${DB_HOST}" -p 5432 -d vitea_db -U dbadmin --clean --if-exists --verbose backups/system_backup_$BACKUP_TIMESTAMP/vitea_database_complete.backup

# Restore source code
echo "Restoring source code..."
if [ -d "api-project" ]; then
    rm -rf api-project-backup-\$(date +%s)
    mv api-project api-project-backup-\$(date +%s)
fi
cp -r backups/system_backup_$BACKUP_TIMESTAMP/api-project-original api-project

if [ -d "frontend-project" ]; then
    rm -rf frontend-project-backup-\$(date +%s)
    mv frontend-project frontend-project-backup-\$(date +%s)
fi
cp -r backups/system_backup_$BACKUP_TIMESTAMP/frontend-project-original frontend-project

# Restore configurations
echo "Restoring configurations..."
cp -r backups/system_backup_$BACKUP_TIMESTAMP/configs-original/* configs/

echo "✅ SYSTEM RESTORED TO PRE-ENHANCEMENT STATE"
echo "You may need to redeploy the original API and frontend."
EOF

chmod +x "$BACKUP_DIR/quick_restore.sh"
log_success "Quick restore script created"

# Update environment with backup location
echo "export LATEST_BACKUP_DIR=\"$BACKUP_DIR\"" >> ../configs/environment.conf
echo "export LATEST_BACKUP_TIMESTAMP=\"$BACKUP_TIMESTAMP\"" >> ../configs/environment.conf

echo ""
echo "🎉 SYSTEM BACKUP COMPLETED SUCCESSFULLY!"
echo "========================================"
echo "📁 Backup Location: $BACKUP_DIR"
echo "🕐 Backup Timestamp: $BACKUP_TIMESTAMP"
echo ""
echo "📋 BACKUP CONTENTS:"
echo "   🗄️ Complete Database Backup (Custom & SQL formats)"
echo "   📂 Original API Source Code"
echo "   📂 Original Frontend Source Code"
echo "   ⚙️ Original Configuration Files"
echo "   ☁️ Azure App Service Settings"
echo "   📄 Backup Manifest & Restore Instructions"
echo ""
echo "🔄 TO RESTORE: Run ./00-rollback-system.sh $BACKUP_TIMESTAMP"
echo "📖 MANIFEST: View $BACKUP_DIR/backup_manifest.txt"
echo ""
log_success "✅ SAFE TO PROCEED WITH ENHANCEMENT DEPLOYMENT"
echo ""