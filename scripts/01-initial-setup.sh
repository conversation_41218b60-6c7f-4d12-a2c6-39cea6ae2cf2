#!/bin/bash
set -e

# Color codes
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log_step() {
    echo ""
    echo -e "${BLUE}📋 STEP: $1${NC}"
    echo "----------------------------------------"
}

check_success() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ SUCCESS: $1${NC}"
    else
        echo -e "${RED}❌ FAILED: $1${NC}"
        exit 1
    fi
}

echo "🚀 Starting Vitea Application Azure Setup..."
echo "================================================"

# Load environment variables
source ../configs/environment.conf

log_step "Checking Azure CLI authentication"
az account show > /dev/null 2>&1
check_success "Azure CLI is authenticated"

log_step "Installing required Azure CLI extensions"
az extension add --name application-insights --only-show-errors
az extension add --name staticwebapp --only-show-errors
check_success "Extensions installed"

log_step "Registering Azure resource providers"
az provider register --namespace Microsoft.ContainerInstance --only-show-errors
az provider register --namespace Microsoft.DBforPostgreSQL --only-show-errors
az provider register --namespace Microsoft.Web --only-show-errors
az provider register --namespace Microsoft.KeyVault --only-show-errors
check_success "Resource providers registered"

log_step "Creating resource group: $RESOURCE_GROUP"
az group create \
  --name $RESOURCE_GROUP \
  --location $LOCATION \
  --tags Environment=$ENVIRONMENT Project="Vitea-AI-App" \
  --only-show-errors
check_success "Resource group created"

log_step "Creating Key Vault"
az keyvault create \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-kv" \
  --resource-group $RESOURCE_GROUP \
  --location $LOCATION \
  --enabled-for-template-deployment true \
  --sku standard \
  --only-show-errors
check_success "Key Vault created"

log_step "Creating Log Analytics workspace"
az monitor log-analytics workspace create \
  --resource-group $RESOURCE_GROUP \
  --workspace-name "${COMPANY_NAME}-${ENVIRONMENT}-logs" \
  --location $LOCATION \
  --only-show-errors
check_success "Log Analytics workspace created"

echo ""
echo -e "${GREEN}🎉 Initial setup completed successfully!${NC}"
echo "Next: Run ./02-database-setup.sh"
