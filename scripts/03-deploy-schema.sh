#!/bin/bash
set -e

echo "📊 Deploying Database Schema..."
echo "==============================="

source ../configs/environment.conf

log_step() {
    echo ""
    echo "📋 STEP: $1"
    echo "----------------------------------------"
}

check_success() {
    if [ $? -eq 0 ]; then
        echo "✅ SUCCESS: $1"
    else
        echo "❌ FAILED: $1"
        exit 1
    fi
}

log_step "PostgreSQL client ready (Cloud Shell has psql built-in)"

log_step "Connecting to database and deploying schema"
echo "Database: ${COMPANY_NAME}-${ENVIRONMENT}-postgres.postgres.database.azure.com"

# Code to allow-list "uuid-ossp" extension 
az postgres flexible-server parameter set \
    --resource-group $RESOURCE_GROUP \
    --server-name ${COMPANY_NAME}-${ENVIRONMENT}-postgres \
    --name azure.extensions \
    --value "uuid-ossp"

export PGPASSWORD="$DB_PASSWORD"
psql -h "${COMPANY_NAME}-${ENVIRONMENT}-postgres.postgres.database.azure.com" \
     -p 5432 \
     -d vitea_db \
     -U dbadmin \
     -f ../configs/database-schema.sql \
     --set sslmode=require

check_success "Schema deployed"

log_step "Verifying database deployment"
TABLES_COUNT=$(PGPASSWORD="$DB_PASSWORD" psql -h "${COMPANY_NAME}-${ENVIRONMENT}-postgres.postgres.database.azure.com" \
    -p 5432 -d vitea_db -U dbadmin \
    -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" \
    --set sslmode=require | xargs)

echo "Tables created: $TABLES_COUNT"

if [ "$TABLES_COUNT" -ge 5 ]; then
    echo "✅ Database verification successful"
else
    echo "❌ Database verification failed"
    exit 1
fi

echo ""
echo "🎉 Database schema deployed successfully!"
echo "Tables: users, policies, policy_violations, documents, audit_log"
echo "Next: Run script 4 (Azure AD setup)"
