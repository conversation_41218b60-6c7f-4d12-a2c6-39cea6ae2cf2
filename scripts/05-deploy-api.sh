#!/bin/bash
set -e

echo "🚀 Deploying Vitea API..."
echo "=============================="

source ../configs/environment.conf

log_step() {
    echo ""
    echo "📋 STEP: $1"
    echo "----------------------------------------"
}

check_success() {
    if [ $? -eq 0 ]; then
        echo "✅ SUCCESS: $1"
    else
        echo "❌ FAILED: $1"
        exit 1
    fi
}

log_step "Creating App Service Plan"
az appservice plan create \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-plan" \
  --resource-group $RESOURCE_GROUP \
  --location "centralus" \
  --sku B1 \
  --only-show-errors
check_success "App Service Plan created"

log_step "Creating App Service for API"
az webapp create \
  --resource-group $RESOURCE_GROUP \
  --plan "${COMPANY_NAME}-${ENVIRONMENT}-plan" \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
  --runtime "NODE:20LTS"
#  --startup-file "src/app.js"
#  --only-show-errors
az webapp config set \
  --resource-group $RESOURCE_GROUP \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
  --startup-file "src/app.js"

check_success "App Service created"

log_step "Configuring App Service settings"
az webapp config appsettings set \
  --resource-group $RESOURCE_GROUP \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
  --settings \
    NODE_ENV=production \
    PORT=8000 \
    FRONTEND_URL="https://temp-frontend-url" \
  --only-show-errors
check_success "App settings configured"

log_step "Enabling managed identity"
az webapp identity assign \
  --resource-group $RESOURCE_GROUP \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
  --only-show-errors

PRINCIPAL_ID=$(az webapp identity show \
  --resource-group $RESOURCE_GROUP \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
  --query principalId \
  --output tsv)

# Block commented and replaced by {az role assignment create} to resolve error "Cannot set policies to a vault with '--enable-rbac-authorization' specified"
#az keyvault set-policy \
#  --name "${COMPANY_NAME}-${ENVIRONMENT}-kv" \
#  --object-id $PRINCIPAL_ID \
#  --secret-permissions get list \
#  --only-show-errors

az role assignment create \
  --assignee $PRINCIPAL_ID \
  --role "Key Vault Secrets User" \
  --scope "/subscriptions/7d91d61f-241a-4803-b43c-ea2730d6ec6f"

check_success "Managed identity configured"

log_step "Preparing deployment package"
cd ../api-project
zip -r api-deployment.zip . -x "node_modules/*"
check_success "Deployment package created"

log_step "Deploying API code"
az webapp deployment source config-zip \
  --resource-group $RESOURCE_GROUP \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
  --src api-deployment.zip \
  --only-show-errors
check_success "API code deployed"

log_step "Restarting App Service"
az webapp restart \
  --resource-group $RESOURCE_GROUP \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
  --only-show-errors
check_success "App Service restarted"

echo "⏱️ Waiting for deployment to complete (30 seconds)..."
sleep 30

log_step "Testing API deployment"
API_URL="https://${COMPANY_NAME}-${ENVIRONMENT}-api.azurewebsites.net"
echo "Testing: $API_URL/health"

HEALTH_RESPONSE=$(curl  -o /dev/null -w "%{http_code}" "$API_URL/health" || echo "000")

if [ "$HEALTH_RESPONSE" = "200" ]; then
    echo "✅ API health check passed (HTTP 200)"
    API_TEST_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/api/v1/test" || echo "000")
    if [ "$API_TEST_RESPONSE" = "200" ]; then
        echo "✅ API test endpoint passed (HTTP 200)"
    else
        echo "⚠️ API test endpoint returned HTTP $API_TEST_RESPONSE"
    fi
else
    echo "❌ API health check failed (HTTP $HEALTH_RESPONSE)"
    exit 1
fi

cd ../scripts
echo "export API_URL=\"$API_URL\"" >> ../configs/environment.conf

echo ""
echo "🎉 API deployment completed successfully!"
echo "API URL: $API_URL"
echo "Health Check: $API_URL/health"
echo "Next: Run script 6 (Deploy frontend)"
