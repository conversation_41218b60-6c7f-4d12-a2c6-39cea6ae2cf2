#!/bin/bash
set -e

echo "🌐 Deploying React Frontend..."
echo "=============================="

source ../configs/environment.conf

log_step() {
    echo ""
    echo "📋 STEP: $1"
    echo "----------------------------------------"
}

check_success() {
    if [ $? -eq 0 ]; then
        echo "✅ SUCCESS: $1"
    else
        echo "❌ FAILED: $1"
        exit 1
    fi
}

echo "Node.js already available in Cloud Shell: $(node --version)"

log_step "Installing frontend dependencies"
cd ../frontend-project
npm install #--silent
check_success "Dependencies installed"

log_step "Creating production environment file"
cat > .env.production << EOF
REACT_APP_AZURE_CLIENT_ID=${APP_ID}
REACT_APP_AZURE_TENANT_ID=${TENANT_ID}
REACT_APP_API_BASE_URL=https://${COMPANY_NAME}-${ENVIRONMENT}-api.azurewebsites.net
EOF
check_success "Environment file created"

log_step "Building React application"
REACT_APP_AZURE_CLIENT_ID=$APP_ID \
REACT_APP_AZURE_TENANT_ID=$TENANT_ID \
REACT_APP_API_BASE_URL=https://${COMPANY_NAME}-${ENVIRONMENT}-api.azurewebsites.net \
npm run build #--silent
check_success "React build completed"

cd ../scripts

log_step "Creating Static Web App"
SWA_NAME="${COMPANY_NAME}-${ENVIRONMENT}-frontend"

az staticwebapp create \
  --name $SWA_NAME \
  --resource-group $RESOURCE_GROUP \
  --location "West US 2" \
  --only-show-errors
check_success "Static Web App created"

SWA_URL=$(az staticwebapp show \
  --name $SWA_NAME \
  --resource-group $RESOURCE_GROUP \
  --query "defaultHostname" \
  --output tsv)

echo "Static Web App URL: https://$SWA_URL"

log_step "Getting deployment token"
DEPLOYMENT_TOKEN=$(az staticwebapp secrets list \
  --name $SWA_NAME \
  --resource-group $RESOURCE_GROUP \
  --query "properties.apiKey" \
  --output tsv)
check_success "Deployment token retrieved"

log_step "Installing and deploying with SWA CLI"
# Added "sudo" to command npm install -g @azure/statis-web-app-cli@latest to avoid permission error
sudo npm install -g @azure/static-web-apps-cli@latest #--silent

cd ../frontend-project
# Commented {--output-location "build"} to avoid simultaneous argument error
# Added "sudo" to command {swa deploy ...} to avoid permission erro
sudo swa deploy ./build \
  --deployment-token $DEPLOYMENT_TOKEN \
  --app-location "/"
#  --output-location "build"
check_success "Frontend deployed"

log_step "Updating Azure AD redirect URI"
az ad app update \
  --id $APP_ID \
  --web-redirect-uris "https://$SWA_URL" "http://localhost:3000" \
  --only-show-errors
check_success "Azure AD redirect URI updated"

cd ../scripts

echo "⏱️ Waiting for deployment to propagate (60 seconds)..."
sleep 60

log_step "Testing frontend deployment"
FRONTEND_URL="https://$SWA_URL"
FRONTEND_RESPONSE=$(curl -o /dev/null -w "%{http_code}" "$FRONTEND_URL" || echo "000")

if [ "$FRONTEND_RESPONSE" = "200" ]; then
    echo "✅ Frontend accessible (HTTP 200)"
else
    echo "⚠️ Frontend returned HTTP $FRONTEND_RESPONSE (may still be deploying)"
fi

echo "export FRONTEND_URL=\"https://$SWA_URL\"" >> ../configs/environment.conf

echo ""
echo "🎉 Frontend deployment completed successfully!"
echo "Frontend URL: https://$SWA_URL"
echo ""
echo "🔧 TEST YOUR APPLICATION:"
echo "1. Go to: https://$SWA_URL"
echo "2. Click 'Sign In with Azure AD'"
echo "3. Complete authentication"
echo "4. Verify dashboard loads"
echo ""
echo "Next: Run script 7 (Final testing)"
