#!/bin/bash
set -e

echo "🚀 Deploying Enhanced API..."
echo "============================"

source ../configs/environment.conf

log_step() {
    echo ""
    echo "📋 STEP: $1"
    echo "----------------------------------------"
}

check_success() {
    if [ $? -eq 0 ]; then
        echo "✅ SUCCESS: $1"
    else
        echo "❌ FAILED: $1"
        exit 1
    fi
}

log_step "Preparing enhanced API project"
cd ../enhanced-api-project

# Install dependencies
npm install --production
echo "✅ Dependencies installed"

log_step "Setting up environment variables"
cat > .env << EOL
NODE_ENV=production
PORT=8000
DB_HOST=${DB_HOST}
FRONTEND_URL=${FRONTEND_URL}
AZURE_STORAGE_CONNECTION_STRING=${AZURE_STORAGE_CONNECTION_STRING}
AZURE_STORAGE_CONTAINER=${AZURE_STORAGE_CONTAINER}
EOL
check_success "Environment variables configured"

echo "DB_HOST= $DB_HOST"
echo "FRONTEND_URL = $FRONTEND_URL"

#log_step "Deleting current API service"
#az webapp delete \
#  --resource-group $RESOURCE_GROUP \
#  --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
#  --only-show-errors
#check_success "API service deleted"

log_step "Creating App Service Plan"
az appservice plan create \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-plan" \
  --resource-group $RESOURCE_GROUP \
  --location "centralus" \
  --sku B1 \
  --is-linux
check_success "App Service Plan created"

log_step "Creating App Service for API"
az webapp create \
  --resource-group $RESOURCE_GROUP \
  --plan "${COMPANY_NAME}-${ENVIRONMENT}-plan" \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
  --runtime "NODE:20-lts"

log_step "Updating API configuration"
az webapp config set \
  --resource-group $RESOURCE_GROUP \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
  --startup-file "src/app.js" 

az webapp config appsettings set \
  --resource-group $RESOURCE_GROUP \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
  --settings \
    NODE_ENV=production \
    PORT=8000 \
    DB_HOST="${DB_HOST}" \
    FRONTEND_URL="${FRONTEND_URL}" \
    KEY_VAULT_NAME="${COMPANY_NAME}-${ENVIRONMENT}-kv" \
    AZURE_OPENAI_ENDPOINT="https://nniga-mcyos879-eastus2.services.ai.azure.com/models" \
    AZURE_OPENAI_KEY="EDL9Q79qG1KMTm1oVmBLHwDrOe54reUWt1QSvwgTJc6p7PpgZvZYJQQJ99BGACHYHv6XJ3w3AAAAACOGyZaC" \
    AZURE_OPENAI_DEPLOYMENT_NAME="gpt-4.1-nano-dev"
#  --only-show-errors
check_success "API configuration updated"

log_step "Enabling managed identity"
az webapp identity assign \
  --resource-group $RESOURCE_GROUP \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
  --only-show-errors

PRINCIPAL_ID=$(az webapp identity show \
  --resource-group $RESOURCE_GROUP \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
  --query principalId \
  --output tsv)

# Block commented and replaced by {az role assignment create} to resolve error "Cannot set policies to a vault with '--enable-rbac-authorization' specified"
#az keyvault set-policy \
#  --name "${COMPANY_NAME}-${ENVIRONMENT}-kv" \
#  --object-id $PRINCIPAL_ID \
#  --secret-permissions get list \
#  --only-show-errors
# Wait for Azure to create the service principal
echo "⏳ Waiting for service principal to be available..."
for i in {1..10}; do
  az ad sp show --id "$PRINCIPAL_ID" &> /dev/null && break
  echo "Waiting for service principal... ($i)"
  sleep 10
done

az role assignment create \
  --assignee $PRINCIPAL_ID \
  --role "Key Vault Secrets User" \
  --scope "/subscriptions/7d91d61f-241a-4803-b43c-ea2730d6ec6f/resourceGroups/${COMPANY_NAME}-${ENVIRONMENT}-rg/providers/Microsoft.KeyVault/vaults/${COMPANY_NAME}-${ENVIRONMENT}-kv"

check_success "Managed identity configured"

echo "⏱️ Waiting for App Service to apply changes (30 seconds)..."
sleep 30

log_step "Creating deployment package"
rm -rf node_modules
npm install --production
zip -r enhanced-api-deployment.zip . -x "*.git*" "*.DS_Store"
#  -x "*.git*" "*.DS_Store" \
#  -x "test/*" \
#  -x "scripts/*" \
#  -x "logs/*" \
#  -x "node_modules/*"
check_success "Deployment package created"

log_step "Deploying enhanced API code"
az webapp deployment source config-zip \
  --resource-group $RESOURCE_GROUP \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
  --src enhanced-api-deployment.zip 
#  --only-show-errors
check_success "Enhanced API code deployed"

log_step "Starting enhanced API service"
az webapp restart \
  --resource-group $RESOURCE_GROUP \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-api" 
#  --only-show-errors
check_success "Enhanced API service started"

echo "⏱️ Waiting for service to fully start (30 seconds)..."
sleep 30

log_step "Testing enhanced API"
API_URL="https://${COMPANY_NAME}-${ENVIRONMENT}-api.azurewebsites.net"
echo "API_URL= $API_URL"
HEALTH_RESPONSE=$(curl -o /dev/null -w "%{http_code}" "$API_URL/health" || echo "000")

if [ "$HEALTH_RESPONSE" = "200" ]; then
    echo "✅ Enhanced API health check passed"
    
    # Test new endpoints
    POLICIES_RESPONSE=$(curl -o /dev/null -w "%{http_code}" "$API_URL/api/v1/policies" -H "Authorization: Bearer admin-token" || echo "000")
    if [ "$POLICIES_RESPONSE" = "200" ]; then
        echo "✅ Policy management endpoints working"
    else
        echo "⚠️ Policy endpoints returned HTTP $POLICIES_RESPONSE"
    fi
else
    echo "❌ Enhanced API health check failed (HTTP $HEALTH_RESPONSE)"
    exit 1
fi

cd ../scripts

echo ""
echo "🎉 Enhanced API deployment completed!"
echo "API URL: $API_URL"
echo "New features: Policy Management, MCP Flow, Enhanced Logging"
