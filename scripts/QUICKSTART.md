# 🚀 Quick Start Guide - Pilot Admin Platform

This guide will help you get the complete Pilot Admin Platform and Testing/Observability Platform up and running quickly with all sample data.

## Prerequisites

- Docker Desktop installed and running
- Docker Compose v2.0+
- Node.js 18+ (for local development)
- 8GB+ RAM available for Docker
- Ports available:
  - **3001**: Pilot Admin UI (Docker)
  - **5432**: Pilot PostgreSQL database
  - **5433**: Testing Platform PostgreSQL database (if separate)
  - **8000**: Testing & Observability API
  - **8001**: Pilot Admin API
  - **3004**: Testing Platform UI (optional)

## 🐳 Docker Setup - Complete Platform

### Step 1: Create Docker Network
First, create a shared network for all services to communicate:

```bash
docker network create vitea-shared-network
```

### Step 2: Start All Services

#### Option A: Using Docker Compose (Recommended)
```bash
# Start Pilot Admin Platform (Database, API, Frontend)
cd /Users/<USER>/Vitea/pilot
docker-compose --env-file .env.docker up -d

# Start Testing & Observability Platform
cd /Users/<USER>/Vitea/testing-observability-platform
docker-compose --env-file .env.docker up -d
```

**Note:** Both platforms will use the shared network `vitea-shared-network` for inter-service communication.

#### Option B: Manual Container Setup
```bash
# 1. PostgreSQL for Pilot (port 5432)
docker run -d \
  --name pilot-postgres \
  --network vitea-shared-network \
  -e POSTGRES_DB=vitea_db \
  -e POSTGRES_USER=dbadmin \
  -e POSTGRES_PASSWORD=vitea123 \
  -p 5432:5432 \
  postgres:15-alpine

# 2. PostgreSQL for Testing Platform (port 5433)
docker run -d \
  --name eval-postgres \
  --network vitea-shared-network \
  -e POSTGRES_DB=evaluation_db \
  -e POSTGRES_USER=dbadmin \
  -e POSTGRES_PASSWORD=postgres123 \
  -p 5433:5432 \
  postgres:15-alpine

# Wait for databases to be ready
sleep 10

# 3. Build and run Pilot API (port 8001)
cd /Users/<USER>/Vitea/pilot
docker build -t pilot-api ./enhanced-api-project
docker run -d \
  --name pilot-api \
  --network vitea-shared-network \
  -e DB_HOST=pilot-postgres \
  -e DB_NAME=vitea_db \
  -e DB_USER=dbadmin \
  -e DB_PASSWORD=vitea123 \
  -p 8001:8000 \
  pilot-api

# 4. Build and run Pilot Frontend (port 3001)
docker build -t pilot-frontend -f Dockerfile.frontend.dynamic .
docker run -d \
  --name pilot-frontend \
  --network vitea-shared-network \
  -e API_URL=http://pilot-api:8000 \
  -p 3001:80 \
  pilot-frontend

# 5. Build and run Testing API (port 8000)
cd /Users/<USER>/Vitea/testing-observability-platform
docker build -t eval-api ./api
docker run -d \
  --name eval-api \
  --network vitea-shared-network \
  -e DB_HOST=eval-postgres \
  -e DB_NAME=evaluation_db \
  -e DB_USER=dbadmin \
  -e DB_PASSWORD=postgres123 \
  -p 8000:8000 \
  eval-api
```

### Step 3: Initialize Databases (REQUIRED)

After containers are running, you MUST initialize the database schemas:

```bash
# Initialize Pilot Admin database with schema
cd /Users/<USER>/Vitea/pilot
#create the schema
#./scripts/setup-pilot-admin.sh -- DO NOT run this, the load-demo-data script below will also create the tables for pilot schema.
#load test data for demo
./scripts/load-demo-data-v2.sh


# Initialize Testing & Observability database with schema
./scripts/setup-testing-observability.sh

# Optional: Load sample data for testing
./scripts/setup-testing-observability.sh --sample-data
```

**Important:** The database schemas include all required columns for CSV imports and full functionality.

### Step 4: Verify All Containers Are Running

```bash
# Check container status
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
```

You should see:
```
NAMES               STATUS              PORTS
pilot-postgres      Up X minutes        0.0.0.0:5432->5432/tcp
pilot-api           Up X minutes        0.0.0.0:8001->8000/tcp
pilot-frontend      Up X minutes        0.0.0.0:3001->80/tcp
eval-postgres       Up X minutes        0.0.0.0:5433->5432/tcp
eval-api            Up X minutes        0.0.0.0:8000->8000/tcp
```

## 🎯 One-Command Database Setup

### Quick Setup - Both Platforms
For the fastest setup with everything included:

```bash
# Setup Pilot Admin Platform with all data
cd /Users/<USER>/Vitea/pilot
./scripts/setup-pilot-admin.shload-demo-data-v2.sh

# Setup Testing & Observability Platform
./scripts/setup-testing-observability.sh --sample-data
```

This will:
1. Create/recreate fresh databases
2. Set up all required tables (30+ for Pilot, 7+ for Testing)
3. Load HIPAA compliance sample data
4. Load testing platform sample data with experiments
5. Configure all stored procedures and functions

## 📋 Setup Options

### Full Setup (Recommended for New Users)
```bash
# Complete setup with all sample data
./scripts/setup-pilot-admin.sh
```

### Clean Install
```bash
# Drop everything and start fresh
./scripts/setup-pilot-admin.sh --clean
```

### Schema Only (No Sample Data)
```bash
# Just create the database structure
./scripts/setup-pilot-admin.sh --schema-only
```

### Custom Container
```bash
# Use a different PostgreSQL container
./scripts/setup-pilot-admin.sh --container my-postgres --database my_db
```

## 🗂️ What Gets Installed

### Database Tables (30 total)
- **Core Tables**: users, roles, policies, agents
- **Policy Management**: policy_groups, policy_templates
- **Compliance**: audit_log, policy_violations
- **Testing Platform**: datasets, experiments, evaluations
- **Chat/MCP**: chat sessions, flow steps

### Sample Data
- **5 Healthcare Users** (doctors, nurses, admins)
- **8 HIPAA Policies** (PHI access, data sharing, breach notification)
- **4 Security Roles** (admin, physician, nurse, auditor)
- **4 Test Datasets** (Q&A, tool usage, RAG retrieval)
- **4 Sample Experiments** (with results)

### Sample Credentials
All sample users have password: `password123`
- `<EMAIL>` - Physician
- `<EMAIL>` - Nurse Practitioner
- `<EMAIL>` - System Admin

## 🏃 Running the Applications

After database setup, the services should already be running if you used Docker Compose. If not:

### 1. Pilot Admin Platform
```bash
# If using Docker Compose (already running)
cd /Users/<USER>/Vitea/pilot
docker-compose ps  # Check status

# For local development (instead of Docker)
cd enhanced-api-project
npm install  # First time only
npm start    # API at http://localhost:8001

cd ../admin-ui-project
npm install  # First time only
npm start    # UI at http://localhost:3000
```

### 2. Testing & Observability Platform
```bash
# If using Docker Compose (already running)
cd /Users/<USER>/Vitea/testing-observability-platform
docker-compose ps  # Check status

# API is available at http://localhost:8000
# Frontend at http://localhost:3004 (if configured)
```

### Service URLs
- **Pilot Admin UI**: http://localhost:3001 (Docker) or http://localhost:3000 (local)
- **Pilot API**: http://localhost:8001
- **Testing API**: http://localhost:8000
- **Testing UI**: http://localhost:3004

## 🔍 Verify Installation

Check that everything is working:

```bash
# Check all running containers
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Verify Pilot database tables
docker exec pilot-postgres psql -U dbadmin -d vitea_db -c "\dt" | head -20

# Check Pilot sample data
docker exec pilot-postgres psql -U dbadmin -d vitea_db -c "SELECT COUNT(*) as policies FROM policies;"
docker exec pilot-postgres psql -U dbadmin -d vitea_db -c "SELECT COUNT(*) as users FROM users;"

# Check Testing Platform tables (if using same database)
docker exec pilot-postgres psql -U dbadmin -d vitea_db -c "SELECT COUNT(*) as datasets FROM datasets;"
docker exec pilot-postgres psql -U dbadmin -d vitea_db -c "SELECT COUNT(*) as experiments FROM experiments;"

# Test API endpoints
curl http://localhost:8001/health  # Pilot API
curl http://localhost:8000/health  # Testing API
```

## 🛠️ Troubleshooting

### Docker Network Issues
```bash
# Ensure the shared network exists
docker network ls | grep vitea-shared-network

# If not, create it
docker network create vitea-shared-network

# Connect existing containers to the network
docker network connect vitea-shared-network pilot-postgres
docker network connect vitea-shared-network pilot-api
docker network connect vitea-shared-network pilot-frontend
```

### Container Not Found
```bash
# For Pilot PostgreSQL
docker run --name pilot-postgres \
  --network vitea-shared-network \
  -e POSTGRES_USER=dbadmin \
  -e POSTGRES_PASSWORD=vitea123 \
  -p 5432:5432 \
  -d postgres:15

# For Testing PostgreSQL (if separate)
docker run --name eval-postgres \
  --network vitea-shared-network \
  -e POSTGRES_USER=dbadmin \
  -e POSTGRES_PASSWORD=postgres123 \
  -p 5433:5432 \
  -d postgres:15
```

### Database Connection Failed
```bash
# Check containers are running
docker ps | grep postgres

# Restart containers if needed
docker restart pilot-postgres
docker restart eval-postgres  # if using separate DB

# Check logs for errors
docker logs pilot-postgres --tail 50
```

### Port Conflicts
```bash
# Check what's using the ports
lsof -i :3001  # Pilot UI
lsof -i :8000  # Testing API
lsof -i :8001  # Pilot API
lsof -i :5432  # Pilot DB
lsof -i :5433  # Testing DB

# Stop conflicting services or use different ports in docker-compose.yml
```

### Clean Start
```bash
# Remove everything and start fresh
docker-compose down -v  # Remove volumes too
./scripts/setup-pilot-admin.sh --clean
./scripts/setup-testing-observability.sh --clean --sample-data
```

## 📚 Additional Scripts

### HIPAA Sample Data Only
```bash
cat scripts/hipaa-sample-data-safe.sql | docker exec -i pilot-postgres psql -U dbadmin -d vitea_db
```

### Testing Platform Data Only
```bash
cat scripts/testing-observability-sample-data.sql | docker exec -i pilot-postgres psql -U dbadmin -d vitea_db
```

### Create Schema Only
```bash
cat scripts/create-complete-schema.sql | docker exec -i pilot-postgres psql -U dbadmin -d vitea_db
```

## 🎨 Features to Explore

Once setup is complete, you can:

1. **Policy Management**
   - View 8 pre-configured HIPAA policies
   - Toggle policies on/off
   - Edit policy configurations
   - View policy groups

2. **User Management**
   - 5 sample healthcare professionals
   - Role-based access control
   - Department assignments

3. **Testing Platform**
   - Run experiments on sample datasets
   - View evaluation metrics
   - Compare model performance

4. **Audit & Compliance**
   - View audit logs
   - Track policy violations
   - Monitor compliance status

## 🆘 Getting Help

- Check the logs: `docker logs pilot-postgres`
- View script help: `./scripts/setup-pilot-admin.sh --help`
- Database console: `docker exec -it pilot-postgres psql -U dbadmin -d vitea_db`

## 🎯 Next Steps

1. Run the setup script
2. Start the API and Admin UI
3. Login with a sample user account
4. Explore the HIPAA compliance features
5. Try creating new policies and experiments

---

**Happy coding!** 🚀 If you encounter any issues, the setup script includes detailed logging to help troubleshoot.