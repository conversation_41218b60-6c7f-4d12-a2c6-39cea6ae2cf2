# Database Scripts Directory

## 🚀 Quick Start

For all database deployments, use:
```bash
psql -h localhost -U dbadmin -d vitea_db -f 00-create-complete-postgres-schema-consolidated-v2.sql
```

## 📁 File Structure

### ✅ Active Files

| File | Purpose | Usage |
|------|---------|-------|
| `00-create-complete-postgres-schema-consolidated-v2.sql` | **MAIN SCHEMA FILE** | Complete database schema with template management |
| `deploy-template-management-docker.sh` | Deployment automation | Run to deploy template system |
| `validate-template-deployment.sh` | Validation script | Verify deployment success |
| `rollback-template-deployment.sh` | Emergency rollback | Restore previous state if needed |
| `test-template-management.sh` | Test suite | Run template system tests |
| `load-hipaa-demo-data.sh` | Demo data loader | Load HIPAA-compliant test data |

### ❌ Deprecated Files

All files with `.deprecated` extension are no longer used:
- `00-create-complete-postgres-schema-consolidated.sql.deprecated` - Old V1 schema
- `migrate-templates-to-schemas*.sql.deprecated` - One-time migrations (already applied)
- `*-sample-data*.sql.deprecated` - Old data loading scripts
- Others - See SQL_FILES_DEPRECATION_NOTICE.md for full list

## 🗄️ Schema V2 Features

The consolidated V2 schema includes:
- **34 tables** with full relationships
- **17+ functions** including template auto-generation
- **14+ triggers** for automation
- **40+ indexes** for performance
- **Template Management** fully integrated
- **HIPAA compliance** built-in
- **Audit logging** comprehensive

## 🔧 Common Tasks

### Deploy Fresh Database
```bash
# Create database (if needed)
createdb -h localhost -U dbadmin vitea_db

# Deploy schema
psql -h localhost -U dbadmin -d vitea_db -f 00-create-complete-postgres-schema-consolidated-v2.sql

# Load demo data (optional)
./load-hipaa-demo-data.sh
```

### Deploy Template Management (Existing DB)
```bash
# For existing databases that need template management
./deploy-template-management-docker.sh

# Validate deployment
./validate-template-deployment.sh
```

### Emergency Rollback
```bash
# If something goes wrong
./rollback-template-deployment.sh
```

### Run Tests
```bash
# Test template management system
./test-template-management.sh
```

## 📋 Template Management

Templates are now integrated into `policy_schemas` table:
- Auto-generated from JSON schemas
- Manual override support via API
- No more `policy_templates` table

API Endpoints:
- `GET /api/v1/schemas/:name/template` - Get template
- `PUT /api/v1/schemas/:name/template` - Update template
- `DELETE /api/v1/schemas/:name/template` - Reset to auto-generated
- `POST /api/v1/schemas/regenerate-templates` - Bulk regeneration

## ⚠️ Important Notes

1. **DO NOT** use any `.deprecated` files
2. **DO NOT** create new migration files
3. **DO** make all changes in V2 schema file
4. **DO** test changes locally first

## 📚 Documentation

- `SQL_FILES_DEPRECATION_NOTICE.md` - Deprecation details
- `SCHEMA_CONSOLIDATION_SUMMARY.md` - Consolidation summary
- `TEMPLATE_MANAGEMENT_README.md` - Template system docs
- `TEMPLATE_DEPLOYMENT_GUIDE.md` - Deployment guide

## 🆘 Troubleshooting

### Schema Already Exists Error
```bash
# Drop and recreate (CAUTION: Destroys all data)
psql -h localhost -U dbadmin -d vitea_db -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
psql -h localhost -U dbadmin -d vitea_db -f 00-create-complete-postgres-schema-consolidated-v2.sql
```

### Template Not Generating
```sql
-- Manually trigger template generation
UPDATE policy_schemas 
SET default_template = generate_default_template(schema_content)
WHERE schema_name = 'your_schema';
```

### Check Template Status
```sql
SELECT schema_name, template_source, 
       default_template IS NOT NULL as has_template
FROM policy_schemas WHERE is_active = true;
```

---

**Last Updated:** August 29, 2025  
**Schema Version:** 2.0.0  
**Status:** Production Ready