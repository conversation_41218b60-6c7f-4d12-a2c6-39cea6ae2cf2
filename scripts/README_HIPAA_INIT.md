# HIPAA Sample Data Initialization

## Overview

The HIPAA sample data initialization script provides a complete, realistic dataset for demonstrating HIPAA compliance capabilities in the Vitea.ai system. This script is designed for **development and staging environments only**.

## Files

- **`init-hipaa-sample-data.sh`** - Main initialization script with environment safety checks
- **`hipaa-sample-data-safe.sql`** - ✅ **RECOMMENDED** - Complete SQL dataset (handles existing data)
- **`hipaa-sample-data.sql`** - ⚠️ Original version (only for empty databases)

## Quick Start

### **🎯 RECOMMENDED: Use Safe SQL File**
```bash
# ✅ RECOMMENDED - Works with existing data
PGPASSWORD=vitea123 psql -h localhost -p 5432 -d vitea_db -U dbadmin -f scripts/hipaa-sample-data-safe.sql

# Alternative: Use wrapper script (requires environment configuration)
./scripts/init-hipaa-sample-data.sh --env staging
```

### **⚠️ AVOID: Original SQL File**
```bash
# ❌ ONLY use with empty database (will fail on existing data)
PGPASSWORD=vitea123 psql -h localhost -p 5432 -d vitea_db -U dbadmin -f scripts/hipaa-sample-data.sql
```

### **Script Options**
```bash
# Initialize staging environment  
./scripts/init-hipaa-sample-data.sh --env staging

# Remove sample data only
./scripts/init-hipaa-sample-data.sh --clean

# Validate data relationships without changes
./scripts/init-hipaa-sample-data.sh --validate-only
```

## What Gets Created

### **Users (5 healthcare staff)**
- **System Administrator** (<EMAIL>)
- **Dr. Sarah Martinez** - Medical Director (<EMAIL>)
- **Jennifer Chen** - Compliance Officer (<EMAIL>)  
- **Michael Rodriguez** - Clinical Reviewer (<EMAIL>)
- **Lisa Thompson** - Case Manager (<EMAIL>)

### **Roles (5 HIPAA-focused)**
- `COMPLIANCE_OFFICER` - HIPAA privacy and compliance oversight
- `CLINICAL_REVIEWER` - Healthcare staff with PHI access
- `MEDICAL_DIRECTOR` - Senior physician with full access
- `CASE_MANAGER` - Patient care coordination
- `ADMIN` - System administration

### **Agent (1 HIPAA compliance agent)**
- **Anthem HIPAA Compliance Agent** - AI agent specialized in PHI protection and redaction

### **Policies (8 comprehensive HIPAA policies)**
1. **PHI Access Control Policy** - Controls access to Protected Health Information
2. **Medical Record Sharing with Redaction** - Secure sharing with automatic PHI redaction
3. **Patient Consent Management Policy** - Manages patient consent and authorization
4. **PHI Breach Notification Protocol** - Immediate response for security incidents
5. **Audit Trail Monitoring Policy** - Continuous monitoring of PHI access
6. **Third-Party Data Sharing Rules** - External entity sharing guidelines
7. **Minimum Necessary with Auto-Redaction** - Role-based PHI visibility
8. **Patient Right to Access Policy** - Patient access to their medical records

### **Policy Group**
- **HIPAA Compliance Policy Suite** - Contains all 8 HIPAA policies

### **Sample Data Features**
- ✅ **PHI/PII Redaction** - Realistic redaction rules for SSN, phone, address, email
- ✅ **Role-based Access** - Different visibility levels per healthcare role
- ✅ **Audit Trail** - 10 sample audit log entries with realistic activities
- ✅ **Emergency Override** - Bypass redaction for medical emergencies
- ✅ **Compliance Monitoring** - Policy execution tracking and violation detection

## Environment Safety

The script includes multiple safety checks:
- **Production Protection** - Cannot be run against production
- **Environment Validation** - Validates target environment
- **Backup Creation** - Creates backup before changes (staging only)
- **Transaction Support** - Rollback on failure
- **Relationship Validation** - Checks foreign key constraints

## Sample Redaction Examples

### Clinical Reviewer sees:
```
Patient: John D***
DOB: 03/15/****
Phone: (555) ***-****
Address: Main St, Chicago, IL 60601
SSN: ***-**-1234
```

### Medical Director sees:
```
Patient: John Doe
DOB: 03/15/1985
Phone: (*************
Address: 123 Main St, Chicago, IL 60601
SSN: ***********
```

## Prerequisites

1. **Environment Configuration** - `configs/environment.conf` must exist with database credentials
2. **Database Access** - PostgreSQL with proper permissions
3. **Schema Applied** - Current database schema must be in place

## Usage Scenarios

### Development Workflow
```bash
# Reset database with fresh HIPAA data
./scripts/init-hipaa-sample-data.sh

# Clean data for testing
./scripts/init-hipaa-sample-data.sh --clean

# Reinitialize 
./scripts/init-hipaa-sample-data.sh
```

### Demo Preparation
```bash
# Prepare staging environment for demo
./scripts/init-hipaa-sample-data.sh --env staging

# Validate everything looks correct
./scripts/init-hipaa-sample-data.sh --validate-only
```

## Troubleshooting

### Common Issues

**Database Connection Failed**
- Check `configs/environment.conf` exists
- Verify database credentials
- Ensure database is accessible

**Foreign Key Violations**
- Ensure current schema is applied
- Check for existing conflicting data
- Use `--clean` to remove sample data first

**Permission Errors**
- Verify database user has CREATE/INSERT permissions
- Check that script has execute permissions (`chmod +x`)

### Getting Help

```bash
./scripts/init-hipaa-sample-data.sh --help
```

## Important Notes

⚠️ **This script is for development/staging only**
⚠️ **Do not run against production databases**
⚠️ **Sample data includes realistic but fictional PHI**
⚠️ **Always backup before running in staging**

---

*For questions or issues, refer to the main documentation or contact the development team.*