# Template Management System - Migration Checklist

## Pre-Migration Steps

### 1. Backup Database
```bash
# Create a backup before migration
docker exec pilot-postgres pg_dump -U dbadmin -d vitea_db > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. Verify Services Running
```bash
# Check all services are up
docker-compose ps

# Verify API health
curl http://localhost:8001/health
```

## Migration Steps

### 3. Run Database Migration
```bash
# Apply the migration script
docker exec pilot-postgres psql -U dbadmin -d vitea_db -f /scripts/migrate-templates-to-schemas.sql

# Or from host:
psql -h localhost -p 5432 -U dbadmin -d vitea_db -f scripts/migrate-templates-to-schemas.sql
```

### 4. Verify Migration Success
```sql
-- Connect to database
docker exec -it pilot-postgres psql -U dbadmin -d vitea_db

-- Check new columns exist
\d policy_schemas

-- Verify templates were migrated/generated
SELECT 
    schema_name,
    template_source,
    default_template IS NOT NULL as has_template,
    updated_at
FROM policy_schemas
WHERE is_active = true
ORDER BY schema_name;

-- Check migration audit log
SELECT * FROM audit_log 
WHERE action = 'MIGRATE' 
AND resource_type = 'policy_templates'
ORDER BY created_at DESC;
```

### 5. Test Template Generation
```bash
# Run the test script
./scripts/test-template-management.sh

# Or test manually:
# Get a template
curl -X GET http://localhost:8001/api/v1/schemas/medical_privacy/template \
  -H "Authorization: Bearer admin-token"

# Check template status
curl -X GET http://localhost:8001/api/v1/schemas/templates/status \
  -H "Authorization: Bearer admin-token"
```

## Post-Migration Verification

### 6. Test Policy Creation
```bash
# Test that policies can still be created with templates
curl -X POST http://localhost:8001/api/v1/policies \
  -H "Authorization: Bearer admin-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Policy After Migration",
    "description": "Testing template system",
    "policy_type": "medical_privacy",
    "category": "HIPAA Compliance",
    "definition": {}
  }'
```

### 7. Verify Frontend Functionality
- [ ] Open Admin UI at http://localhost:3000
- [ ] Navigate to Policy Configuration page
- [ ] Click "+ New Policy"
- [ ] Select a policy type from dropdown
- [ ] Verify template loads correctly
- [ ] Create a test policy
- [ ] Verify policy saves successfully

### 8. Check Deprecated Endpoints
```bash
# Verify deprecation warnings appear
curl -I -X GET http://localhost:8001/api/v1/policies/templates \
  -H "Authorization: Bearer admin-token"

# Should see: X-Deprecated: true header
```

## Monitoring Phase (1-2 weeks)

### 9. Monitor System Logs
```bash
# Check for template-related errors
docker logs pilot-api 2>&1 | grep -i template

# Check for deprecation warnings
docker logs pilot-api 2>&1 | grep -i deprecated
```

### 10. Performance Monitoring
- Monitor API response times for template endpoints
- Check database query performance for template operations
- Verify cache hit rates

## Final Cleanup (After Stabilization)

### 11. Remove Hardcoded Templates
After 1-2 weeks of stable operation:

1. Remove hardcoded templates from:
   - `enhanced-api-project/src/services/templateGenerationService.js` (getHardcodedTemplate method)
   - `enhanced-api-project/src/utils/schemaValidator.js` (hardcoded templates)
   - `admin-ui-project/src/utils/schemaUtils.js` (hardcoded templates)

2. Update code to rely solely on database templates

### 12. Drop Legacy Table
```sql
-- Final cleanup - only after full verification
DROP TABLE IF EXISTS policy_templates CASCADE;

-- Remove any remaining references
SELECT * FROM information_schema.columns 
WHERE column_name LIKE '%template%' 
AND table_schema = 'public';
```

### 13. Remove Deprecated Endpoints
Remove from `enhanced-api-project/src/api/policies.js`:
- `GET /api/v1/policies/templates`
- `GET /api/v1/policies/templates/:category`

## Rollback Plan

If issues arise, rollback steps:

### 1. Restore Database
```bash
# Restore from backup
docker exec -i pilot-postgres psql -U dbadmin -d vitea_db < backup_TIMESTAMP.sql
```

### 2. Revert Code Changes
```bash
# Restore from git backup branch
git checkout backup/pre-template-migration
```

### 3. Restart Services
```bash
docker-compose down
docker-compose up -d
```

## Success Criteria

- [ ] All existing policies continue to work
- [ ] New policies can be created with templates
- [ ] Template auto-generation works for new schemas
- [ ] Manual template overrides are preserved
- [ ] No errors in production logs
- [ ] Performance metrics remain stable
- [ ] External systems can update schemas without breaking templates

## Sign-off

- [ ] Development Team
- [ ] QA Team  
- [ ] DevOps Team
- [ ] Product Owner

---

**Migration Date:** ________________

**Migrated By:** ________________

**Notes:** ________________