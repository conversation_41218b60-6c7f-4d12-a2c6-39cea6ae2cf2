-- ================================================================
-- Complete Database Schema for Pilot Application
-- ================================================================
-- This creates ALL tables with ALL columns needed for the application
-- including HIPAA compliance features, soft delete, and all relationships
-- ================================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing tables if doing a fresh install (uncomment if needed)
-- DROP SCHEMA public CASCADE;
-- CREATE SCHEMA public;

-- ================================================================
-- CORE TABLES
-- ================================================================

-- Users table with all columns needed for HIPAA
CREATE TABLE IF NOT EXISTS users (
    user_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    azure_ad_id VARCHAR(255) UNIQUE,
    email VARCHAR(255) UNIQUE NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role VARCHAR(50) DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    department VARCHAR(100),
    status VARCHAR(50) DEFAULT 'active',
    risk_score DECIMAL(3,2) DEFAULT 0,
    two_factor_enabled BOOLEAN DEFAULT false,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Roles table
CREATE TABLE IF NOT EXISTS roles (
    role_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    code VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    permissions JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- User-Role associations
CREATE TABLE IF NOT EXISTS user_roles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(role_id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    assigned_by UUID REFERENCES users(user_id),
    UNIQUE(user_id, role_id)
);

-- Policies table with all columns
CREATE TABLE IF NOT EXISTS policies (
    policy_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    policy_type VARCHAR(50) DEFAULT 'opa',
    definition JSONB NOT NULL DEFAULT '{}'::jsonb,
    version INTEGER DEFAULT 1 NOT NULL,
    is_active BOOLEAN DEFAULT false,
    severity VARCHAR(20) DEFAULT 'medium',
    applies_to_roles TEXT[],
    guardrail_id UUID,
    original_policy_id UUID REFERENCES policies(policy_id),
    rego_content TEXT,
    rego_code TEXT,
    compiled_rego BYTEA,
    blob_container VARCHAR(255) DEFAULT 'rego-policies',
    blob_path VARCHAR(500),
    blob_url VARCHAR(1000),
    rego_template_id VARCHAR(100),
    opa_sync_status VARCHAR(50) DEFAULT 'pending',
    last_rego_generation TIMESTAMP WITH TIME ZONE,
    rego_generation_error TEXT,
    rego_version INTEGER DEFAULT 1,
    cloned_from_policy_name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    updated_by UUID REFERENCES users(user_id),
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(name, version)
);

-- Policy Groups
CREATE TABLE IF NOT EXISTS policy_groups (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    group_id UUID DEFAULT uuid_generate_v4() UNIQUE,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    category VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    status VARCHAR(50) DEFAULT 'active',
    priority INTEGER DEFAULT 100,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Policy Group associations
CREATE TABLE IF NOT EXISTS policy_group_policies (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    group_id UUID NOT NULL,
    policy_id UUID NOT NULL REFERENCES policies(policy_id) ON DELETE CASCADE,
    execution_order INTEGER DEFAULT 0,
    is_mandatory BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(group_id, policy_id)
);

-- Agents table with all columns
CREATE TABLE IF NOT EXISTS agents (
    agent_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    type VARCHAR(50) DEFAULT 'standard',
    agent_type VARCHAR(50) DEFAULT 'standard',
    configuration JSONB DEFAULT '{}'::jsonb,
    vendor VARCHAR(100),
    department VARCHAR(100),
    risk_score DECIMAL(3,2) DEFAULT 0,
    status VARCHAR(50) DEFAULT 'active',
    is_active BOOLEAN DEFAULT true,
    last_health_check TIMESTAMP WITH TIME ZONE,
    health_status VARCHAR(50) DEFAULT 'unknown',
    metadata JSONB DEFAULT '{}'::jsonb,
    rego_content TEXT,
    compiled_rego BYTEA,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Agent-Policy associations
CREATE TABLE IF NOT EXISTS agent_policies (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    agent_id UUID NOT NULL REFERENCES agents(agent_id) ON DELETE CASCADE,
    policy_id UUID NOT NULL REFERENCES policies(policy_id) ON DELETE CASCADE,
    link_type VARCHAR(50) DEFAULT 'enforced',
    priority INTEGER DEFAULT 100,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(agent_id, policy_id, link_type)
);

-- Agent Access Control
CREATE TABLE IF NOT EXISTS agent_access (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    agent_id UUID NOT NULL REFERENCES agents(agent_id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(role_id) ON DELETE CASCADE,
    access_level VARCHAR(50) DEFAULT 'read',
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    granted_by UUID REFERENCES users(user_id),
    UNIQUE(agent_id, role_id)
);

-- Policy Templates
CREATE TABLE IF NOT EXISTS policy_templates (
    template_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    category VARCHAR(100) NOT NULL,
    template_name VARCHAR(255) NOT NULL,
    description TEXT,
    template_schema JSONB NOT NULL,
    sample_policy JSONB,
    tags TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id)
);

-- Audit Log
CREATE TABLE IF NOT EXISTS audit_log (
    audit_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(user_id),
    action_type VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50),
    resource_id UUID,
    changes JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    status VARCHAR(20) DEFAULT 'success',
    error_message TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Policy Violations
CREATE TABLE IF NOT EXISTS policy_violations (
    violation_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    policy_id UUID REFERENCES policies(policy_id),
    agent_id UUID REFERENCES agents(agent_id),
    user_id UUID REFERENCES users(user_id),
    severity VARCHAR(20) NOT NULL,
    violation_type VARCHAR(100),
    description TEXT,
    context JSONB,
    resolved BOOLEAN DEFAULT false,
    resolved_by UUID REFERENCES users(user_id),
    resolved_at TIMESTAMP WITH TIME ZONE,
    detected_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Policy Executions
CREATE TABLE IF NOT EXISTS policy_executions (
    execution_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    policy_id UUID REFERENCES policies(policy_id),
    agent_id UUID REFERENCES agents(agent_id),
    session_id UUID,
    input_data JSONB,
    output_data JSONB,
    decision VARCHAR(50),
    execution_time_ms INTEGER,
    status VARCHAR(20),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Documents table
CREATE TABLE IF NOT EXISTS documents (
    document_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    document_type VARCHAR(50),
    category VARCHAR(100),
    tags TEXT[],
    metadata JSONB DEFAULT '{}'::jsonb,
    version INTEGER DEFAULT 1,
    is_published BOOLEAN DEFAULT false,
    created_by UUID REFERENCES users(user_id),
    updated_by UUID REFERENCES users(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Rego Templates
CREATE TABLE IF NOT EXISTS rego_templates (
    template_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    template_content TEXT NOT NULL,
    parameters JSONB DEFAULT '[]'::jsonb,
    category VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id)
);

-- System Metrics
CREATE TABLE IF NOT EXISTS system_metrics (
    metric_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL,
    metric_unit VARCHAR(50),
    tags JSONB DEFAULT '{}'::jsonb,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enum Categories for dynamic enum management
CREATE TABLE IF NOT EXISTS enum_categories (
    category_id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    policy_type VARCHAR(50) NOT NULL,
    field_path VARCHAR(200) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enum Values for dynamic enum management
CREATE TABLE IF NOT EXISTS enum_values (
    value_id SERIAL PRIMARY KEY,
    category_id INTEGER REFERENCES enum_categories(category_id) ON DELETE CASCADE,
    value VARCHAR(200) NOT NULL,
    display_name VARCHAR(200),
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(category_id, value)
);

-- Agent Role Policies junction table
CREATE TABLE IF NOT EXISTS agent_role_policies (
    agent_id UUID NOT NULL REFERENCES agents(agent_id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(role_id) ON DELETE CASCADE,
    group_id UUID NOT NULL REFERENCES policy_groups(group_id) ON DELETE CASCADE,
    policy_id UUID NOT NULL REFERENCES policies(policy_id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (agent_id, role_id, group_id, policy_id),
    FOREIGN KEY (group_id, policy_id) REFERENCES policy_group_policies(group_id, policy_id) ON DELETE CASCADE
);

-- MCP Chat Sessions
CREATE TABLE IF NOT EXISTS mcp_chat_sessions (
    session_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(user_id),
    title VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active',
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP WITH TIME ZONE
);

-- MCP Flow Steps
CREATE TABLE IF NOT EXISTS mcp_flow_steps (
    step_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id UUID REFERENCES mcp_chat_sessions(session_id) ON DELETE CASCADE,
    step_number INTEGER NOT NULL,
    step_type VARCHAR(50),
    input_data JSONB,
    output_data JSONB,
    status VARCHAR(20),
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Chat Messages
CREATE TABLE IF NOT EXISTS chat_messages (
    message_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id UUID REFERENCES mcp_chat_sessions(session_id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- OpenAI API Calls
CREATE TABLE IF NOT EXISTS openai_api_calls (
    call_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id UUID REFERENCES mcp_chat_sessions(session_id),
    model VARCHAR(100),
    prompt_tokens INTEGER,
    completion_tokens INTEGER,
    total_tokens INTEGER,
    cost DECIMAL(10,6),
    latency_ms INTEGER,
    status VARCHAR(20),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ================================================================
-- INDEXES
-- ================================================================

-- User indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_azure_ad ON users(azure_ad_id);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_users_deleted_at ON users(deleted_at) WHERE deleted_at IS NULL;

-- Policy indexes
CREATE INDEX IF NOT EXISTS idx_policies_active ON policies(is_active);
CREATE INDEX IF NOT EXISTS idx_policies_category ON policies(category);
CREATE INDEX IF NOT EXISTS idx_policies_severity ON policies(severity);
CREATE INDEX IF NOT EXISTS idx_policies_roles ON policies USING GIN(applies_to_roles);
CREATE INDEX IF NOT EXISTS idx_policies_deleted_at ON policies(deleted_at) WHERE deleted_at IS NULL;

-- Agent indexes
CREATE INDEX IF NOT EXISTS idx_agents_active ON agents(is_active);
CREATE INDEX IF NOT EXISTS idx_agents_type ON agents(type);
CREATE INDEX IF NOT EXISTS idx_agents_deleted_at ON agents(deleted_at) WHERE deleted_at IS NULL;

-- Policy group indexes
CREATE INDEX IF NOT EXISTS idx_policy_groups_active ON policy_groups(is_active);
CREATE INDEX IF NOT EXISTS idx_policy_groups_deleted_at ON policy_groups(deleted_at) WHERE deleted_at IS NULL;

-- Audit log indexes
CREATE INDEX IF NOT EXISTS idx_audit_log_user ON audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_resource ON audit_log(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_created ON audit_log(created_at);

-- Policy execution indexes
CREATE INDEX IF NOT EXISTS idx_policy_exec_policy ON policy_executions(policy_id);
CREATE INDEX IF NOT EXISTS idx_policy_exec_agent ON policy_executions(agent_id);
CREATE INDEX IF NOT EXISTS idx_policy_exec_session ON policy_executions(session_id);

-- Session indexes
CREATE INDEX IF NOT EXISTS idx_mcp_sessions_user ON mcp_chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_mcp_sessions_status ON mcp_chat_sessions(status);

-- Additional policy indexes for blob and rego fields
CREATE INDEX IF NOT EXISTS idx_policies_blob_path ON policies(blob_path);
CREATE INDEX IF NOT EXISTS idx_policies_last_generation ON policies(last_rego_generation);
CREATE INDEX IF NOT EXISTS idx_policies_rego_status ON policies(opa_sync_status);

-- Agent role policies indexes
CREATE INDEX IF NOT EXISTS idx_arp_agent ON agent_role_policies(agent_id);
CREATE INDEX IF NOT EXISTS idx_arp_agent_role ON agent_role_policies(agent_id, role_id);
CREATE INDEX IF NOT EXISTS idx_arp_group ON agent_role_policies(group_id);
CREATE INDEX IF NOT EXISTS idx_arp_policy ON agent_role_policies(policy_id);

-- ================================================================
-- FUNCTIONS AND TRIGGERS
-- ================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing triggers to avoid conflicts, then create them
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
DROP TRIGGER IF EXISTS update_policies_updated_at ON policies;
DROP TRIGGER IF EXISTS update_agents_updated_at ON agents;
DROP TRIGGER IF EXISTS update_policy_groups_updated_at ON policy_groups;
DROP TRIGGER IF EXISTS update_roles_updated_at ON roles;
DROP TRIGGER IF EXISTS update_mcp_sessions_updated_at ON mcp_chat_sessions;
DROP TRIGGER IF EXISTS update_enum_categories_updated_at ON enum_categories;
DROP TRIGGER IF EXISTS update_enum_values_updated_at ON enum_values;

-- Apply updated_at triggers to all relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_policies_updated_at BEFORE UPDATE ON policies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON agents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_policy_groups_updated_at BEFORE UPDATE ON policy_groups
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_mcp_sessions_updated_at BEFORE UPDATE ON mcp_chat_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_enum_categories_updated_at BEFORE UPDATE ON enum_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_enum_values_updated_at BEFORE UPDATE ON enum_values
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ================================================================
-- DEFAULT DATA
-- ================================================================

-- Insert system user
INSERT INTO users (user_id, email, first_name, last_name, role, is_active) 
VALUES (uuid_generate_v4(), '<EMAIL>', 'System', 'Admin', 'admin', true)
ON CONFLICT (email) DO NOTHING;

-- Insert system agent
INSERT INTO agents (agent_id, name, description, type, is_active)
VALUES (uuid_generate_v4(), 'system', 'System Agent', 'system', true)
ON CONFLICT (name) DO NOTHING;

-- ================================================================
-- STORED FUNCTIONS FOR POLICY OPERATIONS
-- ================================================================

-- Drop existing functions to avoid signature conflicts
DROP FUNCTION IF EXISTS search_policies(TEXT, TEXT, TEXT, BOOLEAN, INTEGER, INTEGER) CASCADE;
DROP FUNCTION IF EXISTS generate_rego_for_policy(UUID) CASCADE;
DROP FUNCTION IF EXISTS rollback_rego_generation(UUID) CASCADE;
DROP FUNCTION IF EXISTS clone_policy(UUID, VARCHAR, TEXT, UUID) CASCADE;
DROP FUNCTION IF EXISTS get_enum_fields_for_policy_type(VARCHAR) CASCADE;
DROP FUNCTION IF EXISTS get_enum_values(VARCHAR) CASCADE;
DROP FUNCTION IF EXISTS get_policy_template_by_category(VARCHAR) CASCADE;
DROP FUNCTION IF EXISTS log_rego_operation(UUID, VARCHAR, VARCHAR, JSONB) CASCADE;
DROP FUNCTION IF EXISTS log_hipaa_audit_event(UUID, VARCHAR, VARCHAR, UUID, JSONB, JSONB, VARCHAR, VARCHAR, VARCHAR, VARCHAR, VARCHAR, VARCHAR) CASCADE;

-- Function to search policies with filters
CREATE OR REPLACE FUNCTION search_policies(
    p_search TEXT DEFAULT NULL,
    p_category TEXT DEFAULT NULL,
    p_severity TEXT DEFAULT NULL,
    p_active BOOLEAN DEFAULT NULL,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    policy_id UUID,
    name VARCHAR(255),
    description TEXT,
    category VARCHAR(100),
    policy_type VARCHAR(50),
    definition JSONB,
    version INTEGER,
    is_active BOOLEAN,
    severity VARCHAR(20),
    applies_to_roles TEXT[],
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    created_by UUID,
    total_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    WITH filtered_policies AS (
        SELECT 
            p.*,
            COUNT(*) OVER() AS total_count
        FROM policies p
        WHERE p.deleted_at IS NULL
            AND (p_search IS NULL OR (
                p.name ILIKE '%' || p_search || '%' OR 
                p.description ILIKE '%' || p_search || '%'
            ))
            AND (p_category IS NULL OR p.category = p_category)
            AND (p_severity IS NULL OR p.severity = p_severity)
            AND (p_active IS NULL OR p.is_active = p_active)
        ORDER BY p.name
        LIMIT p_limit
        OFFSET p_offset
    )
    SELECT 
        fp.policy_id,
        fp.name,
        fp.description,
        fp.category,
        fp.policy_type,
        fp.definition,
        fp.version,
        fp.is_active,
        fp.severity,
        fp.applies_to_roles,
        fp.created_at,
        fp.updated_at,
        fp.created_by,
        fp.total_count
    FROM filtered_policies fp;
END;
$$ LANGUAGE plpgsql;

-- Function to generate Rego for a policy
CREATE OR REPLACE FUNCTION generate_rego_for_policy(p_policy_id UUID)
RETURNS JSONB AS $$
DECLARE
    v_policy RECORD;
    v_rego_code TEXT;
BEGIN
    SELECT * INTO v_policy FROM policies WHERE policy_id = p_policy_id AND deleted_at IS NULL;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object('success', false, 'error', 'Policy not found');
    END IF;
    
    -- Placeholder for actual Rego generation logic
    v_rego_code := 'package policy.' || replace(lower(v_policy.name), ' ', '_') || E'\n\n' ||
                   'default allow = false' || E'\n\n' ||
                   'allow {' || E'\n' ||
                   '    # Policy implementation here' || E'\n' ||
                   '}';
    
    UPDATE policies 
    SET rego_code = v_rego_code,
        last_rego_generation = CURRENT_TIMESTAMP,
        opa_sync_status = 'generated'
    WHERE policy_id = p_policy_id;
    
    RETURN jsonb_build_object(
        'success', true, 
        'rego_code', v_rego_code,
        'generated_at', CURRENT_TIMESTAMP
    );
END;
$$ LANGUAGE plpgsql;

-- Function to rollback Rego generation
CREATE OR REPLACE FUNCTION rollback_rego_generation(p_policy_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE policies 
    SET rego_code = NULL,
        opa_sync_status = 'pending',
        rego_generation_error = NULL
    WHERE policy_id = p_policy_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Function to clone a policy
CREATE OR REPLACE FUNCTION clone_policy(
    p_policy_id UUID,
    p_new_name VARCHAR(255),
    p_new_description TEXT DEFAULT NULL,
    p_created_by UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    v_new_policy_id UUID;
BEGIN
    INSERT INTO policies (
        name,
        description,
        category,
        policy_type,
        definition,
        version,
        is_active,
        severity,
        applies_to_roles,
        original_policy_id,
        created_by
    )
    SELECT 
        p_new_name,
        COALESCE(p_new_description, description || ' (Clone)'),
        category,
        policy_type,
        definition,
        1,
        false,
        severity,
        applies_to_roles,
        p_policy_id,
        p_created_by
    FROM policies
    WHERE policy_id = p_policy_id AND deleted_at IS NULL
    RETURNING policy_id INTO v_new_policy_id;
    
    RETURN v_new_policy_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get enum fields for a policy type
CREATE OR REPLACE FUNCTION get_enum_fields_for_policy_type(p_policy_type VARCHAR)
RETURNS TABLE(field_path VARCHAR, category_name VARCHAR, description TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT ec.field_path, ec.name, ec.description
    FROM enum_categories ec
    WHERE ec.policy_type = p_policy_type
    AND ec.is_active = true
    ORDER BY ec.name;
END;
$$ LANGUAGE plpgsql;

-- Function to get enum values for a category
CREATE OR REPLACE FUNCTION get_enum_values(p_category_name VARCHAR)
RETURNS TABLE(value VARCHAR, display_name VARCHAR, description TEXT, sort_order INTEGER) AS $$
BEGIN
    RETURN QUERY
    SELECT ev.value, ev.display_name, ev.description, ev.sort_order
    FROM enum_values ev
    JOIN enum_categories ec ON ev.category_id = ec.category_id
    WHERE ec.name = p_category_name
    AND ev.is_active = true
    AND ec.is_active = true
    ORDER BY ev.sort_order, ev.value;
END;
$$ LANGUAGE plpgsql;

-- Function to get policy template by category
CREATE OR REPLACE FUNCTION get_policy_template_by_category(p_category VARCHAR)
RETURNS JSONB AS $$
DECLARE
    v_template JSONB;
BEGIN
    SELECT template_schema INTO v_template
    FROM policy_templates
    WHERE category = p_category
    AND is_active = true
    ORDER BY created_at DESC
    LIMIT 1;
    
    RETURN COALESCE(v_template, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql;

-- Function to log Rego operations
CREATE OR REPLACE FUNCTION log_rego_operation(
    p_policy_id UUID,
    p_operation VARCHAR,
    p_status VARCHAR,
    p_details JSONB DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO audit_log (
        resource_type,
        resource_id,
        action_type,
        status,
        metadata,
        created_at
    ) VALUES (
        'policy_rego',
        p_policy_id,
        p_operation,
        p_status,
        p_details,
        CURRENT_TIMESTAMP
    );
END;
$$ LANGUAGE plpgsql;

-- Function for updating enum categories timestamp
CREATE OR REPLACE FUNCTION update_enum_categories_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function for updating enum values timestamp
CREATE OR REPLACE FUNCTION update_enum_values_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to log HIPAA audit events
CREATE OR REPLACE FUNCTION log_hipaa_audit_event(
    p_user_id UUID,
    p_action_type VARCHAR(50),
    p_resource_type VARCHAR(50),
    p_resource_id UUID,
    p_old_value JSONB,
    p_new_value JSONB,
    p_session_id VARCHAR(255),
    p_request_id VARCHAR(255),
    p_user_role VARCHAR(50),
    p_action_category VARCHAR(50),
    p_access_type VARCHAR(20),
    p_data_classification VARCHAR(50)
)
RETURNS UUID AS $$
DECLARE
    v_audit_id UUID;
BEGIN
    INSERT INTO audit_log (
        user_id,
        action_type,
        resource_type,
        resource_id,
        changes,
        session_id,
        metadata
    ) VALUES (
        p_user_id,
        p_action_type,
        p_resource_type,
        p_resource_id,
        jsonb_build_object(
            'old_value', p_old_value,
            'new_value', p_new_value
        ),
        p_session_id,
        jsonb_build_object(
            'request_id', p_request_id,
            'user_role', p_user_role,
            'action_category', p_action_category,
            'access_type', p_access_type,
            'data_classification', p_data_classification
        )
    ) RETURNING audit_id INTO v_audit_id;
    
    RETURN v_audit_id;
END;
$$ LANGUAGE plpgsql;

-- ================================================================
-- ADDITIONAL CONSTRAINTS
-- ================================================================

-- Add unique constraints needed for ON CONFLICT clauses (only if they don't exist)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'users_email_unique') THEN
        ALTER TABLE users ADD CONSTRAINT users_email_unique UNIQUE (email);
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'agents_name_unique') THEN
        ALTER TABLE agents ADD CONSTRAINT agents_name_unique UNIQUE (name);
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'roles_code_unique') THEN
        ALTER TABLE roles ADD CONSTRAINT roles_code_unique UNIQUE (code);
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'policy_groups_name_unique') THEN
        ALTER TABLE policy_groups ADD CONSTRAINT policy_groups_name_unique UNIQUE (name);
    END IF;
END $$;

-- ================================================================
-- TESTING AND OBSERVABILITY PLATFORM TABLES
-- ================================================================

-- Datasets table for storing test data
CREATE TABLE IF NOT EXISTS datasets (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL DEFAULT 'custom',
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    data JSONB NOT NULL DEFAULT '[]'::jsonb,
    record_count INTEGER NOT NULL DEFAULT 0,
    created_by VARCHAR(100) NOT NULL DEFAULT 'system',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Dataset entries table for individual test cases
CREATE TABLE IF NOT EXISTS dataset_entries (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    dataset_id UUID REFERENCES datasets(id) ON DELETE CASCADE,
    test_case_type VARCHAR(20) NOT NULL,
    input JSONB NOT NULL,
    expected_output TEXT,
    context TEXT,
    retrieval_context TEXT[],
    tools_called TEXT[],
    expected_outcome VARCHAR(50),
    scenario TEXT,
    initial_context TEXT,
    entry_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Experiments table for test runs
CREATE TABLE IF NOT EXISTS experiments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    dataset_id UUID REFERENCES datasets(id) ON DELETE CASCADE,
    agent_config JSONB,
    execution_mode VARCHAR(20) NOT NULL DEFAULT 'automated',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    progress INTEGER NOT NULL DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_by VARCHAR(100) NOT NULL DEFAULT 'system',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Evaluations table for storing evaluation results
CREATE TABLE IF NOT EXISTS evaluations (
    id SERIAL PRIMARY KEY,
    evaluation_id VARCHAR(255) NOT NULL,
    experiment_id UUID NOT NULL REFERENCES experiments(id) ON DELETE CASCADE,
    experiment_name VARCHAR(255),
    agent_name VARCHAR(255),
    dataset_name VARCHAR(255),
    evaluation_type VARCHAR(50),
    status VARCHAR(50) DEFAULT 'pending',
    evaluations JSONB,
    summary JSONB,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITHOUT TIME ZONE
);

-- Test results table for individual test case results
CREATE TABLE IF NOT EXISTS test_results (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    experiment_id UUID REFERENCES experiments(id) ON DELETE CASCADE,
    dataset_entry_id UUID REFERENCES dataset_entries(id) ON DELETE CASCADE,
    test_case_type VARCHAR(20) NOT NULL,
    input JSONB NOT NULL,
    expected_output TEXT,
    actual_output TEXT NOT NULL,
    context TEXT,
    retrieval_context TEXT[],
    tools_called TEXT[],
    expected_outcome VARCHAR(50),
    scenario TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    score DECIMAL(5,2),
    latency_ms INTEGER,
    token_count INTEGER,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Evaluation metrics table for defining evaluation criteria
CREATE TABLE IF NOT EXISTS evaluation_metrics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    implementation_type VARCHAR(50),
    config JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Experiment evaluations junction table
CREATE TABLE IF NOT EXISTS experiment_evaluations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    experiment_id UUID NOT NULL REFERENCES experiments(id) ON DELETE CASCADE,
    metric_id UUID NOT NULL REFERENCES evaluation_metrics(id),
    status VARCHAR(20) DEFAULT 'pending',
    score DECIMAL(5,2),
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(experiment_id, metric_id)
);

-- ================================================================
-- TESTING AND OBSERVABILITY INDEXES
-- ================================================================

CREATE INDEX IF NOT EXISTS idx_datasets_status ON datasets(status);
CREATE INDEX IF NOT EXISTS idx_datasets_created_at ON datasets(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_dataset_entries_dataset ON dataset_entries(dataset_id);
CREATE INDEX IF NOT EXISTS idx_experiments_dataset ON experiments(dataset_id);
CREATE INDEX IF NOT EXISTS idx_experiments_status ON experiments(status);
CREATE INDEX IF NOT EXISTS idx_experiments_created_at ON experiments(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_evaluations_experiment ON evaluations(experiment_id);
CREATE INDEX IF NOT EXISTS idx_test_results_experiment ON test_results(experiment_id);
CREATE INDEX IF NOT EXISTS idx_test_results_entry ON test_results(dataset_entry_id);
CREATE INDEX IF NOT EXISTS idx_experiment_evaluations_experiment ON experiment_evaluations(experiment_id);
CREATE INDEX IF NOT EXISTS idx_experiment_evaluations_metric ON experiment_evaluations(metric_id);

-- ================================================================
-- TESTING AND OBSERVABILITY TRIGGERS
-- ================================================================

-- Drop existing testing platform triggers to avoid conflicts
DROP TRIGGER IF EXISTS update_datasets_updated_at ON datasets;
DROP TRIGGER IF EXISTS update_experiments_updated_at ON experiments;
DROP TRIGGER IF EXISTS update_test_results_updated_at ON test_results;
DROP TRIGGER IF EXISTS update_evaluation_metrics_updated_at ON evaluation_metrics;
DROP TRIGGER IF EXISTS update_experiment_evaluations_updated_at ON experiment_evaluations;

CREATE TRIGGER update_datasets_updated_at BEFORE UPDATE ON datasets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_experiments_updated_at BEFORE UPDATE ON experiments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_test_results_updated_at BEFORE UPDATE ON test_results
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_evaluation_metrics_updated_at BEFORE UPDATE ON evaluation_metrics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_experiment_evaluations_updated_at BEFORE UPDATE ON experiment_evaluations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ================================================================
-- COMPLETION MESSAGE
-- ================================================================
DO $$
BEGIN
    RAISE NOTICE 'Database schema created successfully!';
    RAISE NOTICE 'All tables, indexes, functions, and constraints are ready.';
    RAISE NOTICE 'The database is prepared for HIPAA compliance data.';
    RAISE NOTICE 'Stored procedures for policy operations are installed.';
END $$;