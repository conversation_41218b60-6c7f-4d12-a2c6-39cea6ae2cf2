#!/bin/bash
set -e

echo "🚀 Enhanced Vitea Application Deployment"
echo "========================================"
echo "This script will deploy all enhanced features:"
echo "  - Enhanced Database Schema"
echo "  - Policy Management API"
echo "  - Policy Admin UI"
echo "  - MCP Chatbot Interface"
echo "  - Integration Tests"
echo ""

read -p "Continue with deployment? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 1
fi

echo ""
echo "🏗️ Starting Enhanced Deployment Process..."

# Check if we're in the right directory
if [ ! -f "../configs/environment.conf" ]; then
    echo "❌ Error: environment.conf not found. Please run from the scripts directory of your existing deployment."
    exit 1
fi

# Check if backup was created
if [ ! -d "../backups" ] || [ -z "$(ls -A ../backups 2>/dev/null)" ]; then
    echo "⚠️ WARNING: No backup found!"
    echo "It's HIGHLY RECOMMENDED to run the backup script first:"
    echo "./00-backup-system.sh"
    echo ""
    read -p "Continue without backup? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Deployment cancelled. Please run backup first."
        exit 1
    fi
fi

# Execute deployment scripts in order
echo "📋 Executing deployment scripts..."

echo ""
echo "Step 1/5: Updating Database Schema..."
if [ -f "./08-update-database-schema.sh" ]; then
    ./08-update-database-schema.sh
else
    echo "❌ Error: 08-update-database-schema.sh not found"
    exit 1
fi

echo ""
echo "Step 2/5: Deploying Enhanced API..."
if [ -f "./09-deploy-enhanced-api.sh" ]; then
    ./09-deploy-enhanced-api.sh
else
    echo "❌ Error: 09-deploy-enhanced-api.sh not found"
    exit 1
fi

echo ""
echo "Step 3/5: Deploying Policy Admin UI..."
if [ -f "./10-deploy-admin-ui.sh" ]; then
    ./10-deploy-admin-ui.sh
else
    echo "❌ Error: 10-deploy-admin-ui.sh not found"
    exit 1
fi

echo ""
echo "Step 4/5: Deploying Chatbot Interface..."
if [ -f "./11-deploy-chatbot-interface.sh" ]; then
    ./11-deploy-chatbot-interface.sh
else
    echo "⚠️ Warning: 11-deploy-chatbot-interface.sh not found, skipping chatbot deployment"
    echo "The chatbot interface will be included in the main frontend update"
fi

echo ""
echo "Step 5/5: Running Integration Tests..."
if [ -f "./12-integration-tests.sh" ]; then
    ./12-integration-tests.sh
else
    echo "❌ Error: 12-integration-tests.sh not found"
    exit 1
fi

echo ""
echo "🎉 ENHANCED VITEA DEPLOYMENT COMPLETED!"
echo "======================================"
source ../configs/environment.conf
echo ""
echo "🌐 Your Enhanced Applications:"
echo "   📊 Main Dashboard: https://${COMPANY_NAME}-${ENVIRONMENT}-frontend.azurewebsites.net"
echo "   🤖 AI Chatbot: https://${COMPANY_NAME}-${ENVIRONMENT}-frontend.azurewebsites.net/chatbot"
if [ -n "${ADMIN_FRONTEND_URL}" ]; then
    echo "   ⚙️ Policy Admin: ${ADMIN_FRONTEND_URL}"
else
    echo "   ⚙️ Policy Admin: https://${COMPANY_NAME}-${ENVIRONMENT}-admin.azurewebsites.net"
fi
echo "   🔧 API Documentation: https://${COMPANY_NAME}-${ENVIRONMENT}-api.azurewebsites.net/health"
echo ""
echo "🔑 Admin Access:"
echo "   Use 'Authorization: Bearer admin-token' header for API access"
echo "   Configure Azure AD roles for production use"
echo ""
echo "📈 What's New:"
echo "   ✅ 12-Step MCP Flow for AI responses"
echo "   ✅ Policy Management Interface"
echo "   ✅ Real-time Policy Enforcement"
echo "   ✅ Enhanced Logging and Monitoring"
echo "   ✅ Azure OpenAI Integration Ready"
echo ""
echo "🚀 Ready for Production!"
