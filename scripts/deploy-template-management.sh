#!/bin/bash

# ==============================================================================
# Template Management System - Automated Deployment Script
# ==============================================================================
# This script automates the deployment of the template management system
# including database migration, validation, and health checks
# ==============================================================================

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"5432"}
DB_NAME=${DB_NAME:-"vitea_db"}
DB_USER=${DB_USER:-"dbadmin"}
API_HOST=${API_HOST:-"localhost"}
API_PORT=${API_PORT:-"8001"}
BACKUP_DIR="./backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
DEPLOYMENT_LOG="deployment_${TIMESTAMP}.log"

# ==============================================================================
# Helper Functions
# ==============================================================================

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1" | tee -a "$DEPLOYMENT_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$DEPLOYMENT_LOG"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$DEPLOYMENT_LOG"
}

log_section() {
    echo -e "\n${BLUE}==============================================================================
$1
==============================================================================${NC}" | tee -a "$DEPLOYMENT_LOG"
}

confirm_action() {
    read -p "$(echo -e ${YELLOW}"$1 (y/n): "${NC})" -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_warning "Deployment cancelled by user"
        exit 1
    fi
}

check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 is not installed. Please install it first."
        exit 1
    fi
}

# ==============================================================================
# Pre-deployment Checks
# ==============================================================================

log_section "TEMPLATE MANAGEMENT SYSTEM DEPLOYMENT"
log_info "Deployment started at $(date)"
log_info "Logging to: $DEPLOYMENT_LOG"

# Check required commands
log_info "Checking required commands..."
check_command psql
check_command curl
check_command docker
check_command jq

# ==============================================================================
# Step 1: Pre-flight Checks
# ==============================================================================

log_section "Step 1: Pre-flight Checks"

# Check database connectivity
log_info "Checking database connectivity..."
if PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1" > /dev/null 2>&1; then
    log_info "✓ Database is accessible"
else
    log_error "Cannot connect to database at $DB_HOST:$DB_PORT"
    log_error "Please check your database connection settings"
    exit 1
fi

# Check API health
log_info "Checking API health..."
if curl -s -f "http://${API_HOST}:${API_PORT}/health" > /dev/null 2>&1; then
    log_info "✓ API is running"
else
    log_error "API is not accessible at http://${API_HOST}:${API_PORT}"
    log_error "Please ensure the enhanced-api-project is running"
    exit 1
fi

# Check if policy_templates table exists
log_info "Checking current database state..."
TABLE_EXISTS=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'policy_templates'
    );" | tr -d ' ')

if [ "$TABLE_EXISTS" = "t" ]; then
    log_warning "policy_templates table exists and will be migrated"
    TEMPLATE_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT COUNT(*) FROM policy_templates;" | tr -d ' ')
    log_info "Found $TEMPLATE_COUNT templates to migrate"
else
    log_info "policy_templates table does not exist (clean installation)"
fi

# Check if migration already applied
COLUMNS_EXIST=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'policy_schemas' 
        AND column_name = 'default_template'
    );" | tr -d ' ')

if [ "$COLUMNS_EXIST" = "t" ]; then
    log_warning "Migration columns already exist in policy_schemas table"
    log_info "Checking if templates are already populated..."
    
    TEMPLATES_POPULATED=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT COUNT(*) FROM policy_schemas 
        WHERE default_template IS NOT NULL;" | tr -d ' ')
    
    if [ "$TEMPLATES_POPULATED" -gt 0 ]; then
        log_warning "Found $TEMPLATES_POPULATED schemas with templates already"
        confirm_action "Migration may have already been applied. Continue anyway?"
    fi
fi

# ==============================================================================
# Step 2: Backup Database
# ==============================================================================

log_section "Step 2: Database Backup"

# Create backup directory
mkdir -p "$BACKUP_DIR"

log_info "Creating database backup..."
BACKUP_FILE="${BACKUP_DIR}/vitea_db_pre_template_migration_${TIMESTAMP}.sql"

if docker exec pilot-postgres pg_dump -U "$DB_USER" -d "$DB_NAME" > "$BACKUP_FILE" 2>/dev/null; then
    log_info "✓ Database backed up to: $BACKUP_FILE"
    log_info "  Backup size: $(du -h "$BACKUP_FILE" | cut -f1)"
else
    log_warning "Using direct psql backup instead of docker..."
    if PGPASSWORD=$DB_PASSWORD pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" > "$BACKUP_FILE"; then
        log_info "✓ Database backed up to: $BACKUP_FILE"
    else
        log_error "Failed to create database backup"
        exit 1
    fi
fi

# ==============================================================================
# Step 3: Apply Migration
# ==============================================================================

log_section "Step 3: Database Migration"

confirm_action "Ready to apply database migration. Continue?"

log_info "Applying migration script..."

# Check if running in docker or direct
if [ -f "/.dockerenv" ] || docker ps | grep -q pilot-postgres; then
    # Running in Docker environment
    log_info "Applying migration via Docker..."
    if docker exec pilot-postgres psql -U "$DB_USER" -d "$DB_NAME" -f /scripts/migrate-templates-to-schemas.sql > migration_output.log 2>&1; then
        log_info "✓ Migration completed successfully"
    else
        log_error "Migration failed. Check migration_output.log for details"
        cat migration_output.log | tail -20
        exit 1
    fi
else
    # Direct database connection
    log_info "Applying migration directly..."
    if PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f scripts/migrate-templates-to-schemas.sql > migration_output.log 2>&1; then
        log_info "✓ Migration completed successfully"
    else
        log_error "Migration failed. Check migration_output.log for details"
        cat migration_output.log | tail -20
        exit 1
    fi
fi

# Extract migration summary
log_info "Migration Summary:"
grep -E "(Migrated|Generated|templates|NOTICE)" migration_output.log | tail -10 | while read line; do
    echo "  $line"
done

# ==============================================================================
# Step 4: Verify Migration
# ==============================================================================

log_section "Step 4: Migration Verification"

log_info "Verifying database schema..."

# Check columns exist
VERIFY_COLUMNS=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
    SELECT column_name 
    FROM information_schema.columns 
    WHERE table_name = 'policy_schemas' 
    AND column_name IN ('default_template', 'template_source')
    ORDER BY column_name;" | tr -d ' ')

if echo "$VERIFY_COLUMNS" | grep -q "default_template" && echo "$VERIFY_COLUMNS" | grep -q "template_source"; then
    log_info "✓ New columns added successfully"
else
    log_error "Migration columns not found in policy_schemas table"
    exit 1
fi

# Check templates generated
TEMPLATE_STATS=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
    SELECT 
        COUNT(*) FILTER (WHERE default_template IS NOT NULL) as with_templates,
        COUNT(*) FILTER (WHERE default_template IS NULL) as without_templates,
        COUNT(*) as total
    FROM policy_schemas 
    WHERE is_active = true;")

log_info "Template Statistics:"
echo "$TEMPLATE_STATS" | while read with without total; do
    log_info "  Total active schemas: $total"
    log_info "  With templates: $with"
    log_info "  Without templates: $without"
done

# ==============================================================================
# Step 5: Test API Endpoints
# ==============================================================================

log_section "Step 5: API Endpoint Testing"

log_info "Testing template API endpoints..."

# Test GET template endpoint
log_info "Testing GET /api/v1/schemas/:name/template..."
RESPONSE=$(curl -s -X GET "http://${API_HOST}:${API_PORT}/api/v1/schemas/medical_privacy/template" \
    -H "Authorization: Bearer admin-token")

if echo "$RESPONSE" | grep -q '"template"'; then
    log_info "✓ GET template endpoint working"
else
    log_error "GET template endpoint failed"
    echo "Response: $RESPONSE"
fi

# Test template status endpoint
log_info "Testing GET /api/v1/schemas/templates/status..."
STATUS_RESPONSE=$(curl -s -X GET "http://${API_HOST}:${API_PORT}/api/v1/schemas/templates/status" \
    -H "Authorization: Bearer admin-token")

if echo "$STATUS_RESPONSE" | grep -q '"summary"'; then
    log_info "✓ Template status endpoint working"
    
    # Display summary
    TOTAL=$(echo "$STATUS_RESPONSE" | jq -r '.summary.total // 0')
    WITH=$(echo "$STATUS_RESPONSE" | jq -r '.summary.with_templates // 0')
    log_info "  API reports: $WITH/$TOTAL schemas have templates"
else
    log_error "Template status endpoint failed"
fi

# ==============================================================================
# Step 6: Run Test Suite
# ==============================================================================

log_section "Step 6: Running Test Suite"

if [ -f "scripts/test-template-management.sh" ]; then
    log_info "Running automated tests..."
    
    if bash scripts/test-template-management.sh > test_output.log 2>&1; then
        log_info "✓ All tests passed"
        grep -E "(Tests Passed|Tests Failed)" test_output.log | while read line; do
            echo "  $line"
        done
    else
        log_warning "Some tests failed. Check test_output.log for details"
        grep -E "(✗|ERROR)" test_output.log | head -5 | while read line; do
            echo "  $line"
        done
    fi
else
    log_warning "Test script not found, skipping automated tests"
fi

# ==============================================================================
# Step 7: Verify Frontend Integration
# ==============================================================================

log_section "Step 7: Frontend Integration Check"

log_info "Checking frontend accessibility..."

# Check if frontend is running
if curl -s -f "http://localhost:3000" > /dev/null 2>&1; then
    log_info "✓ Frontend is accessible at http://localhost:3000"
    log_info "  Please manually verify policy creation works in the UI"
else
    log_warning "Frontend not accessible. Please start it and verify manually"
fi

# ==============================================================================
# Step 8: Performance Check
# ==============================================================================

log_section "Step 8: Performance Validation"

log_info "Testing template generation performance..."

# Time template retrieval
START_TIME=$(date +%s%N)
for i in {1..10}; do
    curl -s -X GET "http://${API_HOST}:${API_PORT}/api/v1/schemas/medical_privacy/template" \
        -H "Authorization: Bearer admin-token" > /dev/null
done
END_TIME=$(date +%s%N)

AVG_TIME=$(( ($END_TIME - $START_TIME) / 10000000 ))
log_info "Average template retrieval time: ${AVG_TIME}ms"

if [ $AVG_TIME -lt 100 ]; then
    log_info "✓ Performance is excellent (< 100ms)"
elif [ $AVG_TIME -lt 500 ]; then
    log_info "✓ Performance is acceptable (< 500ms)"
else
    log_warning "Performance may need optimization (> 500ms)"
fi

# ==============================================================================
# Step 9: Cleanup Check
# ==============================================================================

log_section "Step 9: Cleanup Verification"

# Check for deprecated endpoint usage
log_info "Checking for deprecated endpoint warnings..."

curl -s -I -X GET "http://${API_HOST}:${API_PORT}/api/v1/policies/templates" \
    -H "Authorization: Bearer admin-token" 2>/dev/null | grep -q "X-Deprecated: true"

if [ $? -eq 0 ]; then
    log_info "✓ Deprecated endpoints properly marked"
else
    log_warning "Deprecated endpoints may not be properly configured"
fi

# ==============================================================================
# Step 10: Final Summary
# ==============================================================================

log_section "DEPLOYMENT SUMMARY"

log_info "Deployment completed at $(date)"
echo ""

# Generate summary report
cat << EOF | tee -a "$DEPLOYMENT_LOG"

DEPLOYMENT REPORT
================
✓ Database backed up to: $BACKUP_FILE
✓ Migration script applied successfully
✓ Database schema updated with new columns
✓ Templates generated/migrated for active schemas
✓ API endpoints tested and working
✓ Performance validation completed

NEXT STEPS
==========
1. Monitor system logs for any errors:
   docker logs pilot-api -f 2>&1 | grep -i template

2. Verify UI functionality:
   - Open http://localhost:3000
   - Create a test policy
   - Verify template loads correctly

3. Monitor for 1-2 weeks before final cleanup

4. After stabilization, run cleanup:
   - Remove hardcoded templates from code
   - Drop policy_templates table

ROLLBACK INSTRUCTIONS
====================
If issues occur, restore from backup:
  psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME < $BACKUP_FILE

For support, check:
  - Deployment log: $DEPLOYMENT_LOG
  - Migration output: migration_output.log
  - Test results: test_output.log

EOF

log_info "Deployment completed successfully! 🎉"
exit 0