-- =============================================================================
-- HIPAA Sample Data for Development Environment (Safe Version)
-- =============================================================================
-- Purpose: Populate database with realistic HIPAA compliance demo data
-- Created: Generated for MVP HIPAA compliance system
-- Environment: Development/Staging only
-- Version: Safe - handles existing data conflicts
-- =============================================================================

BEGIN;

-- =============================================================================
-- 1. USERS TABLE (5 healthcare staff) - with conflict handling
-- =============================================================================

-- Store user IDs in variables for foreign key references
\set admin_user_id 'a1b2c3d4-e5f6-7890-abcd-111111111111'
\set sarah_user_id 'a1b2c3d4-e5f6-7890-abcd-222222222222'
\set jennifer_user_id 'a1b2c3d4-e5f6-7890-abcd-333333333333'
\set michael_user_id 'a1b2c3d4-e5f6-7890-abcd-444444444444'
\set lisa_user_id 'a1b2c3d4-e5f6-7890-abcd-555555555555'

INSERT INTO users (
    user_id, azure_ad_id, email, first_name, last_name, role, is_active, 
    department, status, risk_score, two_factor_enabled, created_at, updated_at
) VALUES 
-- Admin User (System Administrator)
(:'admin_user_id', 'azure-admin-hipaa-001', '<EMAIL>', 
 'HIPAA', 'Administrator', 'admin', true, 'Information Technology', 'active', 0.0, true, NOW(), NOW()),

-- Dr. Sarah Martinez (Medical Director)  
(:'sarah_user_id', 'azure-sarah-martinez-002', '<EMAIL>',
 'Sarah', 'Martinez', 'user', true, 'Medical Affairs', 'active', 1.5, true, NOW(), NOW()),

-- Jennifer Chen (Compliance Officer)
(:'jennifer_user_id', 'azure-jennifer-chen-003', '<EMAIL>',
 'Jennifer', 'Chen', 'user', true, 'Privacy & Compliance', 'active', 0.5, true, NOW(), NOW()),

-- Michael Rodriguez (Clinical Reviewer)
(:'michael_user_id', 'azure-michael-rodriguez-004', '<EMAIL>',
 'Michael', 'Rodriguez', 'user', true, 'Clinical Review', 'active', 1.0, false, NOW(), NOW()),

-- Lisa Thompson (Case Manager)
(:'lisa_user_id', 'azure-lisa-thompson-005', '<EMAIL>',
 'Lisa', 'Thompson', 'user', true, 'Care Management', 'active', 0.8, true, NOW(), NOW())
ON CONFLICT (azure_ad_id) DO NOTHING;

-- =============================================================================
-- 2. ROLES TABLE (5 HIPAA-focused roles) - with conflict handling
-- =============================================================================

\set compliance_role_id 'b1b2c3d4-e5f6-7890-abcd-111111111111'
\set clinical_role_id 'b1b2c3d4-e5f6-7890-abcd-222222222222'
\set medical_dir_role_id 'b1b2c3d4-e5f6-7890-abcd-333333333333'
\set case_mgr_role_id 'b1b2c3d4-e5f6-7890-abcd-444444444444'
\set hipaa_admin_role_id 'b1b2c3d4-e5f6-7890-abcd-555555555555'

INSERT INTO roles (role_id, code, name, description, created_at) VALUES
(:'compliance_role_id', 'HIPAA_COMPLIANCE_OFFICER', 'HIPAA Privacy and Compliance Officer', 
 'Responsible for HIPAA compliance monitoring and privacy oversight', NOW()),

(:'clinical_role_id', 'HIPAA_CLINICAL_REVIEWER', 'Clinical Staff with PHI Access',
 'Healthcare staff authorized to review patient medical information', NOW()),

(:'medical_dir_role_id', 'HIPAA_MEDICAL_DIRECTOR', 'Senior Physician with Full Access',
 'Senior medical staff with unrestricted access to patient data', NOW()),

(:'case_mgr_role_id', 'HIPAA_CASE_MANAGER', 'Patient Care Coordinator',
 'Staff responsible for coordinating patient care across services', NOW()),

(:'hipaa_admin_role_id', 'HIPAA_ADMIN', 'HIPAA System Administrator',
 'Technical staff with HIPAA system administration privileges', NOW())
ON CONFLICT (code) DO NOTHING;

-- =============================================================================
-- 3. USER_ROLES TABLE (User-role assignments) - with conflict handling
-- =============================================================================

INSERT INTO user_roles (user_id, role_id) VALUES
-- HIPAA Admin User
(:'admin_user_id', :'hipaa_admin_role_id'),

-- Dr. Sarah Martinez (Medical Director + Clinical Reviewer)
(:'sarah_user_id', :'medical_dir_role_id'),
(:'sarah_user_id', :'clinical_role_id'),

-- Jennifer Chen (Compliance Officer)
(:'jennifer_user_id', :'compliance_role_id'),

-- Michael Rodriguez (Clinical Reviewer)
(:'michael_user_id', :'clinical_role_id'),

-- Lisa Thompson (Case Manager)
(:'lisa_user_id', :'case_mgr_role_id')
ON CONFLICT (user_id, role_id) DO NOTHING;

-- =============================================================================
-- 4. AGENTS TABLE (1 HIPAA compliance agent) - with conflict handling
-- =============================================================================

\set hipaa_agent_id 'c1b2c3d4-e5f6-7890-abcd-111111111111'

INSERT INTO agents (
    agent_id, name, description, agent_type, is_active, 
    configuration, vendor, department, risk_score, status, created_at, updated_at, created_by
) VALUES (
    :'hipaa_agent_id',
    'Anthem HIPAA Compliance Agent',
    'AI agent specialized in HIPAA compliance monitoring and PHI protection',
    'healthcare_hipaa_compliance',
    true,
    '{
        "redaction_enabled": true,
        "audit_all_access": true,
        "emergency_override": true,
        "supported_redaction_types": ["ssn", "phone", "address", "email", "dob", "insurance_id"],
        "compliance_version": "HIPAA_2023",
        "max_session_duration": 3600,
        "require_two_factor": false,
        "alert_on_violations": true,
        "auto_log_access": true
    }'::jsonb,
    'Anthem Blue Cross',
    'Privacy & Compliance',
    0.2,
    'active',
    NOW(),
    NOW(),
    :'admin_user_id'
)
ON CONFLICT (name) DO NOTHING;

-- =============================================================================
-- 5. POLICY_GROUPS TABLE (1 HIPAA compliance group) - with conflict handling
-- =============================================================================

\set hipaa_group_id 'd1b2c3d4-e5f6-7890-abcd-111111111111'

INSERT INTO policy_groups (
    group_id, name, description, status, created_at, updated_at, created_by
) VALUES (
    :'hipaa_group_id',
    'HIPAA Compliance Policy Suite',
    'Comprehensive HIPAA privacy and security policies for healthcare operations',
    'active',
    NOW(),
    NOW(),
    :'admin_user_id'
)
ON CONFLICT (name) DO NOTHING;

-- =============================================================================
-- 6. POLICY_TEMPLATES TABLE (8 HIPAA templates) - with conflict handling
-- =============================================================================

\set template_1_id 'e1b2c3d4-e5f6-7890-abcd-111111111111'
\set template_2_id 'e1b2c3d4-e5f6-7890-abcd-222222222222'
\set template_3_id 'e1b2c3d4-e5f6-7890-abcd-333333333333'
\set template_4_id 'e1b2c3d4-e5f6-7890-abcd-444444444444'
\set template_5_id 'e1b2c3d4-e5f6-7890-abcd-555555555555'
\set template_6_id 'e1b2c3d4-e5f6-7890-abcd-666666666666'
\set template_7_id 'e1b2c3d4-e5f6-7890-abcd-777777777777'
\set template_8_id 'e1b2c3d4-e5f6-7890-abcd-888888888888'

-- Note: Policy templates don't have unique constraints other than primary key, so we use template_id conflicts
INSERT INTO policy_templates (
    template_id, template_name, description, category, template_schema, 
    is_active, created_at, created_by
) VALUES 
-- Template 1: PHI Access Control
(:'template_1_id', 'HIPAA PHI Access Control Template',
 'Template for controlling access to Protected Health Information',
 'HIPAA Privacy Compliance',
 '{
     "parameters": {
         "authorized_roles": {"type": "array", "default": ["HIPAA_CLINICAL_REVIEWER"]},
         "requires_audit": {"type": "boolean", "default": true},
         "consent_required": {"type": "boolean", "default": true},
         "emergency_override": {"type": "boolean", "default": false}
     }
 }'::jsonb,
 true, NOW(), :'admin_user_id'),

-- Template 2: Medical Record Sharing (with redaction)
(:'template_2_id', 'HIPAA Medical Record Sharing Template',
 'Template for sharing medical records with PHI redaction capabilities',
 'HIPAA Privacy Compliance',
 '{
     "parameters": {
         "sharing_authorized_roles": {"type": "array", "default": ["HIPAA_CLINICAL_REVIEWER"]},
         "redaction_required": {"type": "boolean", "default": true},
         "redaction_fields": {"type": "array", "default": ["ssn", "phone", "address"]},
         "external_sharing_allowed": {"type": "boolean", "default": false}
     }
 }'::jsonb,
 true, NOW(), :'admin_user_id'),

-- Template 3: Patient Consent Management
(:'template_3_id', 'HIPAA Patient Consent Management Template',
 'Template for managing patient consent and authorization',
 'HIPAA Privacy Compliance',
 '{
     "parameters": {
         "consent_types": {"type": "array", "default": ["treatment", "payment", "operations"]},
         "consent_expiry_days": {"type": "number", "default": 365},
         "verbal_consent_allowed": {"type": "boolean", "default": false}
     }
 }'::jsonb,
 true, NOW(), :'admin_user_id'),

-- Template 4: PHI Breach Notification
(:'template_4_id', 'HIPAA PHI Breach Notification Template',
 'Template for PHI breach detection and notification procedures',
 'HIPAA Privacy Compliance',
 '{
     "parameters": {
         "notification_within_hours": {"type": "number", "default": 24},
         "affected_threshold": {"type": "number", "default": 1},
         "auto_notify_authorities": {"type": "boolean", "default": true}
     }
 }'::jsonb,
 true, NOW(), :'admin_user_id'),

-- Template 5: Audit Trail Monitoring
(:'template_5_id', 'HIPAA Audit Trail Monitoring Template',
 'Template for continuous monitoring of PHI access and usage',
 'HIPAA Privacy Compliance',
 '{
     "parameters": {
         "log_all_access": {"type": "boolean", "default": true},
         "retention_days": {"type": "number", "default": 2555},
         "real_time_alerts": {"type": "boolean", "default": true}
     }
 }'::jsonb,
 true, NOW(), :'admin_user_id'),

-- Template 6: Third-Party Data Sharing
(:'template_6_id', 'HIPAA Third-Party Data Sharing Template',
 'Template for secure sharing of PHI with external entities',
 'HIPAA Privacy Compliance',
 '{
     "parameters": {
         "requires_baa": {"type": "boolean", "default": true},
         "encryption_required": {"type": "boolean", "default": true},
         "audit_sharing": {"type": "boolean", "default": true}
     }
 }'::jsonb,
 true, NOW(), :'admin_user_id'),

-- Template 7: Minimum Necessary Standard (with redaction)
(:'template_7_id', 'HIPAA Minimum Necessary Standard Template',
 'Template for limiting PHI disclosure to minimum necessary information',
 'HIPAA Privacy Compliance',
 '{
     "parameters": {
         "auto_redaction_enabled": {"type": "boolean", "default": true},
         "pii_fields_to_redact": {"type": "array", "default": ["ssn", "address", "phone"]},
         "role_based_visibility": {"type": "boolean", "default": true}
     }
 }'::jsonb,
 true, NOW(), :'admin_user_id'),

-- Template 8: Patient Right to Access
(:'template_8_id', 'HIPAA Patient Right to Access Template',
 'Template for patient rights to view and obtain their medical records',
 'HIPAA Privacy Compliance',
 '{
     "parameters": {
         "response_time_days": {"type": "number", "default": 30},
         "electronic_delivery": {"type": "boolean", "default": true},
         "reasonable_fees_allowed": {"type": "boolean", "default": true}
     }
 }'::jsonb,
 true, NOW(), :'admin_user_id')
ON CONFLICT (template_id) DO NOTHING;

-- =============================================================================
-- 7. POLICIES TABLE (8 actual HIPAA policies) - with conflict handling
-- =============================================================================

\set policy_1_id 'f1b2c3d4-e5f6-7890-abcd-111111111111'
\set policy_2_id 'f1b2c3d4-e5f6-7890-abcd-222222222222'
\set policy_3_id 'f1b2c3d4-e5f6-7890-abcd-333333333333'
\set policy_4_id 'f1b2c3d4-e5f6-7890-abcd-444444444444'
\set policy_5_id 'f1b2c3d4-e5f6-7890-abcd-555555555555'
\set policy_6_id 'f1b2c3d4-e5f6-7890-abcd-666666666666'
\set policy_7_id 'f1b2c3d4-e5f6-7890-abcd-777777777777'
\set policy_8_id 'f1b2c3d4-e5f6-7890-abcd-888888888888'

INSERT INTO policies (
    policy_id, name, description, category, policy_type, definition, version, 
    is_active, severity, applies_to_roles, created_at, updated_at, created_by
) VALUES 
-- Policy 1: PHI Access Control Policy
(:'policy_1_id', 'Anthem HIPAA PHI Access Control Policy',
 'Controls access to Protected Health Information for Anthem healthcare staff',
 'HIPAA Privacy Compliance', 'access_control',
 '{
     "authorized_roles": ["HIPAA_CLINICAL_REVIEWER", "HIPAA_MEDICAL_DIRECTOR", "HIPAA_COMPLIANCE_OFFICER"],
     "requires_audit": true,
     "consent_required": true,
     "emergency_override": false,
     "access_logging": "detailed",
     "session_timeout": 3600,
     "two_factor_required": false
 }'::jsonb,
 1, true, 'critical', 
 ARRAY['HIPAA_CLINICAL_REVIEWER', 'HIPAA_MEDICAL_DIRECTOR', 'HIPAA_COMPLIANCE_OFFICER'],
 NOW(), NOW(), :'admin_user_id'),

-- Policy 2: Medical Record Sharing with Redaction
(:'policy_2_id', 'Anthem HIPAA Medical Record Sharing with Redaction',
 'Secure sharing of medical records with automatic PHI redaction',
 'HIPAA Privacy Compliance', 'data_sharing',
 '{
     "sharing_authorized_roles": ["HIPAA_CLINICAL_REVIEWER", "HIPAA_MEDICAL_DIRECTOR"],
     "redaction_required": true,
     "redaction_fields": ["ssn", "phone", "address", "email", "insurance_id"],
     "redaction_rules": {
         "ssn": "***-**-{last_4}",
         "phone": "({area_code}) ***-****",
         "address": "{city}, {state} {zip}",
         "email": "****@****.com",
         "insurance_id": "{first_3}*****"
     },
     "emergency_override": true,
     "audit_redaction": true,
     "external_sharing_allowed": false
 }'::jsonb,
 1, true, 'high',
 ARRAY['HIPAA_CLINICAL_REVIEWER', 'HIPAA_MEDICAL_DIRECTOR'],
 NOW(), NOW(), :'admin_user_id'),

-- Policy 3: Patient Consent Management
(:'policy_3_id', 'Anthem HIPAA Patient Consent Management Policy',
 'Manages patient consent and authorization for PHI use and disclosure',
 'HIPAA Privacy Compliance', 'consent_management',
 '{
     "consent_types": ["treatment", "payment", "operations", "research"],
     "consent_expiry_days": 365,
     "verbal_consent_allowed": false,
     "written_consent_required": true,
     "electronic_signature_accepted": true,
     "consent_withdrawal_allowed": true,
     "audit_consent_changes": true
 }'::jsonb,
 1, true, 'high',
 ARRAY['HIPAA_CLINICAL_REVIEWER', 'HIPAA_MEDICAL_DIRECTOR', 'HIPAA_CASE_MANAGER'],
 NOW(), NOW(), :'admin_user_id'),

-- Policy 4: PHI Breach Notification Protocol
(:'policy_4_id', 'Anthem HIPAA PHI Breach Notification Protocol',
 'Immediate response procedures for PHI breaches and security incidents',
 'HIPAA Privacy Compliance', 'breach_notification',
 '{
     "notification_within_hours": 24,
     "affected_threshold": 1,
     "auto_notify_authorities": true,
     "notify_patients_within_days": 60,
     "escalation_matrix": {
         "minor": ["HIPAA_COMPLIANCE_OFFICER"],
         "major": ["HIPAA_COMPLIANCE_OFFICER", "HIPAA_MEDICAL_DIRECTOR"],
         "critical": ["HIPAA_COMPLIANCE_OFFICER", "HIPAA_MEDICAL_DIRECTOR", "HIPAA_ADMIN"]
     },
     "documentation_required": true
 }'::jsonb,
 1, true, 'critical',
 ARRAY['HIPAA_COMPLIANCE_OFFICER', 'HIPAA_MEDICAL_DIRECTOR', 'HIPAA_ADMIN'],
 NOW(), NOW(), :'admin_user_id'),

-- Policy 5: Audit Trail Monitoring Policy
(:'policy_5_id', 'Anthem HIPAA Audit Trail Monitoring Policy',
 'Continuous monitoring and logging of all PHI access and system usage',
 'HIPAA Privacy Compliance', 'audit_monitoring',
 '{
     "log_all_access": true,
     "retention_days": 2555,
     "real_time_alerts": true,
     "anomaly_detection": true,
     "automated_reports": true,
     "alert_thresholds": {
         "failed_logins": 3,
         "after_hours_access": true,
         "bulk_data_access": 50
     },
     "log_integrity_checks": true
 }'::jsonb,
 1, true, 'medium',
 ARRAY['HIPAA_COMPLIANCE_OFFICER', 'HIPAA_ADMIN'],
 NOW(), NOW(), :'admin_user_id'),

-- Policy 6: Third-Party Data Sharing Rules
(:'policy_6_id', 'Anthem HIPAA Third-Party Data Sharing Rules',
 'Secure guidelines for sharing PHI with external healthcare entities',
 'HIPAA Privacy Compliance', 'third_party_sharing',
 '{
     "requires_baa": true,
     "encryption_required": true,
     "audit_sharing": true,
     "approved_entities_only": true,
     "data_use_limitations": true,
     "sharing_agreements_required": true,
     "periodic_compliance_reviews": true,
     "data_retention_limits": 1095
 }'::jsonb,
 1, true, 'high',
 ARRAY['HIPAA_COMPLIANCE_OFFICER', 'HIPAA_MEDICAL_DIRECTOR'],
 NOW(), NOW(), :'admin_user_id'),

-- Policy 7: Minimum Necessary Standard with Auto-Redaction
(:'policy_7_id', 'Anthem HIPAA Minimum Necessary with Auto-Redaction',
 'Ensures only minimum necessary PHI is disclosed based on user role',
 'HIPAA Privacy Compliance', 'minimum_necessary',
 '{
     "auto_redaction_enabled": true,
     "pii_fields_to_redact": ["ssn", "address", "phone", "email", "dob", "insurance_id"],
     "role_based_visibility": true,
     "visibility_rules": {
         "HIPAA_CLINICAL_REVIEWER": ["medical_info", "basic_demographics"],
         "HIPAA_COMPLIANCE_OFFICER": ["access_logs", "audit_trails"],
         "HIPAA_MEDICAL_DIRECTOR": ["all_fields"],
         "HIPAA_CASE_MANAGER": ["contact_info", "insurance_info"]
     },
     "redaction_methods": {
         "ssn": "***-**-{last_4}",
         "phone": "({area_code}) ***-****",
         "address": "{city}, {state} {zip}",
         "email": "****@****.com",
         "dob": "{month}/{day}/****",
         "insurance_id": "{first_3}*****"
     },
     "minimum_data_only": true,
     "purpose_limitation": true
 }'::jsonb,
 1, true, 'high',
 ARRAY['HIPAA_CLINICAL_REVIEWER', 'HIPAA_MEDICAL_DIRECTOR', 'HIPAA_CASE_MANAGER'],
 NOW(), NOW(), :'admin_user_id'),

-- Policy 8: Patient Right to Access Policy
(:'policy_8_id', 'Anthem HIPAA Patient Right to Access Policy',
 'Ensures patients can access and obtain copies of their PHI as required by law',
 'HIPAA Privacy Compliance', 'patient_access',
 '{
     "response_time_days": 30,
     "electronic_delivery": true,
     "reasonable_fees_allowed": true,
     "fee_schedule": {
         "paper_copies": 0.25,
         "electronic_copies": 5.00,
         "mailing_costs": "actual"
     },
     "formats_available": ["paper", "electronic", "summary"],
     "denied_requests_reviewed": true,
     "appeal_process_available": true
 }'::jsonb,
 1, true, 'medium',
 ARRAY['HIPAA_CLINICAL_REVIEWER', 'HIPAA_MEDICAL_DIRECTOR', 'HIPAA_CASE_MANAGER'],
 NOW(), NOW(), :'admin_user_id')
ON CONFLICT (name, version) DO NOTHING;

-- =============================================================================
-- 8. POLICY_GROUP_POLICIES TABLE (Link policies to the HIPAA group)
-- =============================================================================

INSERT INTO policy_group_policies (group_id, policy_id) VALUES
(:'hipaa_group_id', :'policy_1_id'),
(:'hipaa_group_id', :'policy_2_id'),
(:'hipaa_group_id', :'policy_3_id'),
(:'hipaa_group_id', :'policy_4_id'),
(:'hipaa_group_id', :'policy_5_id'),
(:'hipaa_group_id', :'policy_6_id'),
(:'hipaa_group_id', :'policy_7_id'),
(:'hipaa_group_id', :'policy_8_id')
ON CONFLICT (group_id, policy_id) DO NOTHING;

-- =============================================================================
-- 9. AGENT_POLICIES TABLE (Link agent to policies via group)
-- =============================================================================

INSERT INTO agent_policies (agent_id, policy_id, link_type) VALUES
(:'hipaa_agent_id', :'policy_1_id', 'via_group'),
(:'hipaa_agent_id', :'policy_2_id', 'via_group'),
(:'hipaa_agent_id', :'policy_3_id', 'via_group'),
(:'hipaa_agent_id', :'policy_4_id', 'via_group'),
(:'hipaa_agent_id', :'policy_5_id', 'via_group'),
(:'hipaa_agent_id', :'policy_6_id', 'via_group'),
(:'hipaa_agent_id', :'policy_7_id', 'via_group'),
(:'hipaa_agent_id', :'policy_8_id', 'via_group')
ON CONFLICT (agent_id, policy_id, link_type) DO NOTHING;

-- =============================================================================
-- 10. AGENT_ACCESS TABLE (Role-based access to the agent)
-- =============================================================================

INSERT INTO agent_access (agent_id, role_id, access_level) VALUES
(:'hipaa_agent_id', :'compliance_role_id', 'manage'),
(:'hipaa_agent_id', :'clinical_role_id', 'view'),
(:'hipaa_agent_id', :'medical_dir_role_id', 'manage'),
(:'hipaa_agent_id', :'case_mgr_role_id', 'view'),
(:'hipaa_agent_id', :'hipaa_admin_role_id', 'manage')
ON CONFLICT (agent_id, role_id) DO NOTHING;

-- =============================================================================
-- 11. SAMPLE AUDIT_LOG ENTRIES (10 realistic entries)
-- =============================================================================

INSERT INTO audit_log (
    user_id, action_type, resource_type, resource_id, changes,
    ip_address, user_agent, created_at
) VALUES 
-- Entry 1: PHI Access by Clinical Reviewer
(:'michael_user_id', 'HIPAA_PHI_ACCESS', 'patient_record', 
 uuid_generate_v4(),
 '{"access_type": "read", "redaction_applied": true, "fields_accessed": ["demographics", "medical_history"]}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
 NOW() - INTERVAL '2 hours'),

-- Entry 2: Policy Execution by Compliance Officer
(:'jennifer_user_id', 'HIPAA_POLICY_EXECUTION', 'policy', :'policy_1_id',
 '{"execution_result": "allow", "redaction_count": 3, "compliance_score": 95}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
 NOW() - INTERVAL '1 hour'),

-- Entry 3: Medical Record Sharing with Redaction
(:'sarah_user_id', 'HIPAA_RECORD_SHARING', 'medical_record',
 uuid_generate_v4(),
 '{"sharing_method": "secure_portal", "recipient": "external_specialist", "redaction_applied": true}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
 NOW() - INTERVAL '3 hours'),

-- Entry 4: Patient Consent Update
(:'lisa_user_id', 'HIPAA_CONSENT_UPDATE', 'patient_consent',
 uuid_generate_v4(),
 '{"old_values": {"consent_status": "pending", "consent_types": ["treatment"]}, "new_values": {"consent_status": "granted", "consent_types": ["treatment", "payment", "operations"]}}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36',
 NOW() - INTERVAL '4 hours'),

-- Entry 5: Emergency Override Used
(:'sarah_user_id', 'HIPAA_EMERGENCY_OVERRIDE', 'policy_execution',
 :'policy_2_id',
 '{"old_values": {"redaction_enabled": true}, "new_values": {"redaction_enabled": false, "override_reason": "medical_emergency", "patient_id": "12345"}}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
 NOW() - INTERVAL '8 hours');

-- =============================================================================
-- SUCCESS MESSAGE
-- =============================================================================

-- Display summary of inserted data
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🏥 HIPAA Sample Data Initialization Complete!';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Successfully inserted:';
    RAISE NOTICE '  • % Total Users (% are HIPAA users)', (SELECT COUNT(*) FROM users), (SELECT COUNT(*) FROM users WHERE email LIKE '%anthemhealth.com');
    RAISE NOTICE '  • % Total Roles (% are HIPAA roles)', (SELECT COUNT(*) FROM roles), (SELECT COUNT(*) FROM roles WHERE code LIKE 'HIPAA_%');
    RAISE NOTICE '  • % User-Role assignments', (SELECT COUNT(*) FROM user_roles);
    RAISE NOTICE '  • % Total Agents (% are HIPAA agents)', (SELECT COUNT(*) FROM agents), (SELECT COUNT(*) FROM agents WHERE name LIKE '%HIPAA%');
    RAISE NOTICE '  • % Policy Templates', (SELECT COUNT(*) FROM policy_templates);
    RAISE NOTICE '  • % Total Policies (% are HIPAA policies)', (SELECT COUNT(*) FROM policies), (SELECT COUNT(*) FROM policies WHERE name LIKE '%HIPAA%');
    RAISE NOTICE '  • % Policy Groups', (SELECT COUNT(*) FROM policy_groups);
    RAISE NOTICE '  • % Policy-Group links', (SELECT COUNT(*) FROM policy_group_policies);
    RAISE NOTICE '  • % Agent-Policy links', (SELECT COUNT(*) FROM agent_policies);
    RAISE NOTICE '  • % Agent Access entries', (SELECT COUNT(*) FROM agent_access);
    RAISE NOTICE '  • % Audit Log entries', (SELECT COUNT(*) FROM audit_log);
    RAISE NOTICE '';
    RAISE NOTICE 'Database is ready for HIPAA compliance demos!';
    RAISE NOTICE 'HIPAA users: Dr. Sarah Martinez, Jennifer Chen, Michael Rodriguez, Lisa Thompson';
    RAISE NOTICE 'HIPAA roles: COMPLIANCE_OFFICER, CLINICAL_REVIEWER, MEDICAL_DIRECTOR, CASE_MANAGER, ADMIN';
    RAISE NOTICE 'HIPAA agent: Anthem HIPAA Compliance Agent';
    RAISE NOTICE 'HIPAA policies: 8 comprehensive privacy and redaction policies';
    RAISE NOTICE '';
END $$;

COMMIT;