#!/bin/bash
set -e

# =============================================================================
# HIPAA Demo Policy Data Loading Script
# =============================================================================
# Purpose: Load HIPAA compliance demo policy data into Vitea Policy Management System
# Usage: ./load-hipaa-demo-data.sh [OPTIONS]
# 
# Options:
#   --container <name>   PostgreSQL container name (default: pilot-postgres)
#   --database <name>    Database name (default: vitea_db)
#   --user <name>        Database user (default: dbadmin)
#   --clean              Clean existing demo data before loading
#   --help               Show this help message
# =============================================================================

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONTAINER_NAME="pilot-postgres"
DB_NAME="vitea_db"
DB_USER="dbadmin"
CLEAN_MODE=false

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo ""
    echo -e "${BLUE}📋 STEP: $1${NC}"
    echo "----------------------------------------"
}

# Help function
show_help() {
    echo "Demo Data Loading Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --container <name>   PostgreSQL container name (default: pilot-postgres)"
    echo "  --database <name>    Database name (default: vitea_db)"
    echo "  --user <name>        Database user (default: dbadmin)"
    echo "  --clean              Clean existing demo data before loading"
    echo "  --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                              # Load HIPAA demo policy data"
    echo "  $0 --clean                      # Clean existing data and load HIPAA demo data"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --container)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        --database)
            DB_NAME="$2"
            shift 2
            ;;
        --user)
            DB_USER="$2"
            shift 2
            ;;
        --clean)
            CLEAN_MODE=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done


# Check if Docker container is running
check_container() {
    log_step "Checking Docker container"
    
    if ! docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        log_error "Container '${CONTAINER_NAME}' is not running"
        log_info "Available containers:"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | head -10
        echo ""
        log_info "Start the container with: docker start ${CONTAINER_NAME}"
        exit 1
    fi
    
    log_success "Container '${CONTAINER_NAME}' is running"
}

# Test database connection
test_connection() {
    log_step "Testing database connection"
    
    if docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1; then
        log_success "Database connection successful"
    else
        log_error "Failed to connect to database"
        log_info "Container: $CONTAINER_NAME"
        log_info "Database: $DB_NAME"
        log_info "User: $DB_USER"
        exit 1
    fi
}

# Clean existing HIPAA demo data if requested
clean_demo_data() {
    if [[ "$CLEAN_MODE" == "true" ]]; then
        log_step "Cleaning existing HIPAA demo data"
        
        log_info "Cleaning users, roles, policies, and related data..."
        cat << EOF | docker exec -i "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME"
-- Clean HIPAA demo data in dependency order
DELETE FROM audit_log WHERE user_id LIKE 'a1b2c3d4-%';
DELETE FROM user_role_assignments WHERE user_id LIKE 'a1b2c3d4-%';
DELETE FROM policies WHERE name LIKE '%HIPAA%' OR created_by LIKE 'a1b2c3d4-%';
DELETE FROM roles WHERE name LIKE '%HIPAA%' OR description LIKE '%HIPAA%';
DELETE FROM users WHERE user_id LIKE 'a1b2c3d4-%' OR email LIKE '%anthemhealth.com';
EOF
        
        log_success "HIPAA demo data cleaned"
    fi
}

# Create system user if needed (to avoid foreign key issues)
create_system_user() {
    log_info "Creating system user if needed..."
    cat << EOF | docker exec -i "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" 2>/dev/null || true
-- Create default system user to avoid foreign key constraint issues
INSERT INTO users (
    user_id, azure_ad_id, email, first_name, last_name, role, is_active, 
    department, status, risk_score, two_factor_enabled, created_at, updated_at
) VALUES (
    '00000000-0000-0000-0000-000000000000',
    'system-user',
    '<EMAIL>',
    'System',
    'User',
    'system',
    true,
    'System',
    'active',
    0.0,
    false,
    NOW(),
    NOW()
) ON CONFLICT (user_id) DO NOTHING;
EOF
}

# Load HIPAA demo policy data
load_demo_data() {
    log_step "Loading HIPAA demo policy data"
    
    local sql_file="${SCRIPT_DIR}/hipaa-sample-data-corrected.sql"
    if [[ ! -f "$sql_file" ]]; then
        log_error "HIPAA sample data file not found: $sql_file"
        exit 1
    fi
    
    # Create system user first to avoid foreign key issues
    create_system_user
    
    log_info "Loading HIPAA compliance demo data..."
    cat "$sql_file" | docker exec -i "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" 2>&1 | \
        grep -E "(INSERT|UPDATE|ERROR|NOTICE)" | tail -20
}

# Display summary
display_summary() {
    log_step "HIPAA Demo Data Loading Summary"
    
    # Get counts
    local user_count=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t \
        -c "SELECT COUNT(*) FROM users WHERE email LIKE '%anthemhealth.com';" 2>/dev/null || echo "0")
    local policy_count=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t \
        -c "SELECT COUNT(*) FROM policies WHERE name LIKE '%HIPAA%';" 2>/dev/null || echo "0")
    local role_count=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t \
        -c "SELECT COUNT(*) FROM roles WHERE name LIKE '%HIPAA%';" 2>/dev/null || echo "0")
    
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}HIPAA Demo Data Loading Complete!${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo ""
    echo "Database: ${DB_NAME} on ${CONTAINER_NAME}"
    echo "HIPAA Demo Users: $(echo $user_count | xargs)"
    echo "HIPAA Demo Policies: $(echo $policy_count | xargs)"
    echo "HIPAA Demo Roles: $(echo $role_count | xargs)"
    echo ""
    echo "Next steps:"
    echo "  1. Access the admin UI at http://localhost:3000"
    echo "  2. Login with HIPAA demo credentials"
    echo "  3. Explore HIPAA policies and user management"
    echo ""
}

# Main execution flow
main() {
    log_info "🚀 HIPAA Demo Policy Data Loading Script"
    log_info "Container: $CONTAINER_NAME"
    log_info "Database: $DB_NAME"
    echo "=========================================="
    
    check_container
    test_connection
    clean_demo_data
    load_demo_data
    display_summary
    
    log_success "✅ HIPAA demo policy data loading completed successfully!"
}

# Execute main function
main "$@"