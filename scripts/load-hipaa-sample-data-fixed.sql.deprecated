-- =============================================================================
-- CORRECTED HIPAA Sample Data for Development Environment
-- =============================================================================
-- Fixed issues:
-- 1. Removed problematic ON CONFLICT clauses for tables without unique constraints
-- 2. Proper column names matching schema
-- 3. Proper dependency ordering
-- 4. Added missing granted_by column to agent_access
-- =============================================================================

BEGIN;

-- First, ensure the missing granted_by column exists in agent_access table
ALTER TABLE agent_access ADD COLUMN IF NOT EXISTS granted_by UUID REFERENCES users(user_id);

-- Clear existing demo data to avoid conflicts
DELETE FROM audit_log WHERE user_id IN (
    'a1b2c3d4-e5f6-7890-abcd-111111111111',
    'a1b2c3d4-e5f6-7890-abcd-************',
    'a1b2c3d4-e5f6-7890-abcd-************',
    'a1b2c3d4-e5f6-7890-abcd-************',
    'a1b2c3d4-e5f6-7890-abcd-************'
);
DELETE FROM agent_access WHERE agent_id = 'c1b2c3d4-e5f6-7890-abcd-111111111111';
DELETE FROM agent_policies WHERE agent_id = 'c1b2c3d4-e5f6-7890-abcd-111111111111';
DELETE FROM policy_group_policies WHERE group_id = 'd1b2c3d4-e5f6-7890-abcd-111111111111';
DELETE FROM policies WHERE policy_id IN (
    'f1b2c3d4-e5f6-7890-abcd-111111111111',
    'f1b2c3d4-e5f6-7890-abcd-************',
    'f1b2c3d4-e5f6-7890-abcd-************'
);
-- DELETE FROM policy_templates WHERE template_id IN (
--     'e1b2c3d4-e5f6-7890-abcd-111111111111',
--     'e1b2c3d4-e5f6-7890-abcd-************',
--     'e1b2c3d4-e5f6-7890-abcd-************'
-- );
DELETE FROM policy_groups WHERE group_id = 'd1b2c3d4-e5f6-7890-abcd-111111111111';
DELETE FROM agents WHERE agent_id = 'c1b2c3d4-e5f6-7890-abcd-111111111111';
DELETE FROM user_roles WHERE user_id IN (
    'a1b2c3d4-e5f6-7890-abcd-111111111111',
    'a1b2c3d4-e5f6-7890-abcd-************',
    'a1b2c3d4-e5f6-7890-abcd-************',
    'a1b2c3d4-e5f6-7890-abcd-************',
    'a1b2c3d4-e5f6-7890-abcd-************'
);
DELETE FROM roles WHERE role_id IN (
    'b1b2c3d4-e5f6-7890-abcd-111111111111',
    '************************************',
    'b1b2c3d4-e5f6-7890-abcd-************',
    'b1b2c3d4-e5f6-7890-abcd-************',
    'b1b2c3d4-e5f6-7890-abcd-************'
);
DELETE FROM users WHERE user_id IN (
    'a1b2c3d4-e5f6-7890-abcd-111111111111',
    'a1b2c3d4-e5f6-7890-abcd-************',
    'a1b2c3d4-e5f6-7890-abcd-************',
    'a1b2c3d4-e5f6-7890-abcd-************',
    'a1b2c3d4-e5f6-7890-abcd-************'
);

-- =============================================================================
-- 1. USERS TABLE (5 healthcare staff) - MUST BE FIRST
-- =============================================================================

INSERT INTO users (
    user_id, azure_ad_id, email, first_name, last_name, role, is_active, 
    department, status, risk_score, two_factor_enabled, created_at, updated_at
) VALUES 
-- Admin User (System Administrator)
('a1b2c3d4-e5f6-7890-abcd-111111111111', 'azure-admin-placeholder-001', '<EMAIL>', 
 'HIPAA', 'Administrator', 'admin', true, 'Information Technology', 'active', 0.0, true, NOW(), NOW()),

-- Dr. Sarah Martinez (Medical Director)  
('a1b2c3d4-e5f6-7890-abcd-************', 'azure-sarah-martinez-002', '<EMAIL>',
 'Sarah', 'Martinez', 'user', true, 'Medical Affairs', 'active', 1.5, true, NOW(), NOW()),

-- Jennifer Chen (Compliance Officer)
('a1b2c3d4-e5f6-7890-abcd-************', 'azure-jennifer-chen-003', '<EMAIL>',
 'Jennifer', 'Chen', 'user', true, 'Privacy & Compliance', 'active', 0.5, true, NOW(), NOW()),

-- Michael Rodriguez (Clinical Reviewer)
('a1b2c3d4-e5f6-7890-abcd-************', 'azure-michael-rodriguez-004', '<EMAIL>',
 'Michael', 'Rodriguez', 'user', true, 'Clinical Review', 'active', 1.0, false, NOW(), NOW()),

-- Lisa Thompson (Case Manager)
('a1b2c3d4-e5f6-7890-abcd-************', 'azure-lisa-thompson-005', '<EMAIL>',
 'Lisa', 'Thompson', 'user', true, 'Care Management', 'active', 0.8, true, NOW(), NOW());

-- =============================================================================
-- 2. ROLES TABLE (5 HIPAA-focused roles)
-- =============================================================================

INSERT INTO roles (role_id, code, name, description, created_at) VALUES
('b1b2c3d4-e5f6-7890-abcd-111111111111', 'HIPAA_COMPLIANCE_OFFICER', 'HIPAA Privacy and Compliance Officer', 
 'Responsible for HIPAA compliance monitoring and privacy oversight', NOW()),

('************************************', 'HIPAA_CLINICAL_REVIEWER', 'Clinical Staff with PHI Access',
 'Healthcare staff authorized to review patient medical information', NOW()),

('b1b2c3d4-e5f6-7890-abcd-************', 'HIPAA_MEDICAL_DIRECTOR', 'Senior Physician with Full Access',
 'Senior medical staff with unrestricted access to patient data', NOW()),

('b1b2c3d4-e5f6-7890-abcd-************', 'HIPAA_CASE_MANAGER', 'Patient Care Coordinator',
 'Staff responsible for coordinating patient care across services', NOW()),

('b1b2c3d4-e5f6-7890-abcd-************', 'HIPAA_ADMIN', 'System Administrator',
 'Technical staff with system administration privileges', NOW());

-- =============================================================================
-- 3. USER_ROLES TABLE (User-role assignments)
-- =============================================================================

INSERT INTO user_roles (user_id, role_id) VALUES
-- Admin User
('a1b2c3d4-e5f6-7890-abcd-111111111111', 'b1b2c3d4-e5f6-7890-abcd-************'),

-- Dr. Sarah Martinez (Medical Director + Clinical Reviewer)
('a1b2c3d4-e5f6-7890-abcd-************', 'b1b2c3d4-e5f6-7890-abcd-************'),
('a1b2c3d4-e5f6-7890-abcd-************', '************************************'),

-- Jennifer Chen (Compliance Officer)
('a1b2c3d4-e5f6-7890-abcd-************', 'b1b2c3d4-e5f6-7890-abcd-111111111111'),

-- Michael Rodriguez (Clinical Reviewer)
('a1b2c3d4-e5f6-7890-abcd-************', '************************************'),

-- Lisa Thompson (Case Manager)
('a1b2c3d4-e5f6-7890-abcd-************', 'b1b2c3d4-e5f6-7890-abcd-************');

-- =============================================================================
-- 4. AGENTS TABLE (1 HIPAA compliance agent)
-- =============================================================================

INSERT INTO agents (
    agent_id, name, description, agent_type, is_active, 
    configuration, vendor, department, risk_score, status, created_at, updated_at, created_by
) VALUES (
    'c1b2c3d4-e5f6-7890-abcd-111111111111',
    'BCBS HIPAA Compliance Agent',
    'AI agent specialized in HIPAA compliance monitoring and PHI protection',
    'healthcare_hipaa_compliance',
    true,
    '{
        "redaction_enabled": true,
        "audit_all_access": true,
        "emergency_override": true,
        "supported_redaction_types": ["ssn", "phone", "address", "email", "dob", "insurance_id"],
        "compliance_version": "HIPAA_2023",
        "max_session_duration": 3600,
        "require_two_factor": false,
        "alert_on_violations": true,
        "auto_log_access": true
    }'::jsonb,
    'BCBS Blue Cross',
    'Privacy & Compliance',
    0.2,
    'active',
    NOW(),
    NOW(),
    'a1b2c3d4-e5f6-7890-abcd-111111111111'
);

-- =============================================================================
-- 5. POLICY_GROUPS TABLE (1 HIPAA compliance group)
-- =============================================================================

INSERT INTO policy_groups (
    group_id, name, description, status, created_at, updated_at, created_by
) VALUES (
    'd1b2c3d4-e5f6-7890-abcd-111111111111',
    'HIPAA Compliance Policy Suite',
    'Comprehensive HIPAA privacy and security policies for healthcare operations',
    'active',
    NOW(),
    NOW(),
    'a1b2c3d4-e5f6-7890-abcd-111111111111'
);

-- -- =============================================================================
-- -- 6. POLICY_TEMPLATES TABLE (3 HIPAA templates)
-- -- =============================================================================
-- 
-- INSERT INTO policy_templates (
--     template_id, category, name, description, template_definition, 
--     created_at, created_by
-- ) VALUES 
-- -- Template 1: Medical Record Sharing with Phone Redaction Template
-- ('e1b2c3d4-e5f6-7890-abcd-111111111111', 'HIPAA Privacy Compliance', 'HIPAA PHI Access Control Template',
--  'Template for controlling access to Protected Health Information',
--  '{
--      "parameters": {
--          "sharing_authorized_roles": {"type": "array", "default": ["HIPAA_CLINICAL_REVIEWER", "HIPAA_MEDICAL_DIRECTOR"]},
--          "redaction_required": {"type": "boolean", "default": true},
--          "redaction_fields": {"type": "array", "default": ["phone"]},
--          "redaction_rules": {"type": "object", "default": {"phone": "({area_code}) ***-****"}},
--          "emergency_override": {"type": "boolean", "default": true},
--          "audit_redaction": {"type": "boolean", "default": true},
--          "external_sharing_allowed": {"type": "boolean", "default": false}
--      }
--  }'::jsonb,
--  NOW(), 'a1b2c3d4-e5f6-7890-abcd-111111111111'),
-- 
-- -- Template 2: Minimum Necessary Standard with Email Redaction Template
-- ('e1b2c3d4-e5f6-7890-abcd-************', 'HIPAA Privacy Compliance', 'HIPAA Medical Record Sharing Template',
--  'Template for sharing medical records with PHI redaction capabilities',
--  '{
--      "parameters": {
--          "auto_redaction_enabled": {"type": "boolean", "default": true},
--          "pii_fields_to_redact": {"type": "array", "default": ["email"]},
--          "role_based_visibility": {"type": "boolean", "default": true},
--          "visibility_rules": {"type": "object", "default": {
--              "HIPAA_CLINICAL_REVIEWER": ["medical_info", "basic_demographics", "email"],
--              "HIPAA_COMPLIANCE_OFFICER": ["access_logs", "audit_trails", "email"],
--              "HIPAA_MEDICAL_DIRECTOR": ["all_fields"],
--              "HIPAA_CASE_MANAGER": ["contact_info", "insurance_info", "email"]
--          }},
--          "redaction_methods": {"type": "object", "default": {"email": "****@****.com"}},
--          "minimum_data_only": {"type": "boolean", "default": true},
--          "purpose_limitation": {"type": "boolean", "default": true}
--      }  
--  }'::jsonb,
--  NOW(), 'a1b2c3d4-e5f6-7890-abcd-111111111111'),
-- 
-- -- Template 3: Minimum Necessary Standard with Auto-Redaction Template
-- ('e1b2c3d4-e5f6-7890-abcd-************', 'HIPAA Privacy Compliance', 'HIPAA Patient Consent Management Template',
--  'Template for managing patient consent and authorization',
--  '{
--      "parameters": {
--          "auto_redaction_enabled": {"type": "boolean", "default": true},
--          "pii_fields_to_redact": {"type": "array", "default": ["ssn", "address", "phone", "email", "dob", "insurance_id"]},
--          "role_based_visibility": {"type": "boolean", "default": true},
--          "visibility_rules": {"type": "object", "default": {
--              "HIPAA_CLINICAL_REVIEWER": ["medical_info", "basic_demographics", "email", "phone", "address", "dob", "insurance_id"],
--              "HIPAA_COMPLIANCE_OFFICER": ["access_logs", "audit_trails", "email", "phone", "address", "dob", "insurance_id"],
--              "HIPAA_MEDICAL_DIRECTOR": ["all_fields"],
--              "HIPAA_CASE_MANAGER": ["contact_info", "insurance_info", "email", "phone", "address", "dob", "insurance_id"]
--          }},
--          "redaction_methods": {"type": "object", "default": {
--              "ssn": "***-**-{last_4}",
--              "phone": "({area_code}) ***-****",
--              "address": "{city}, {state} {zip}",
--              "email": "****@****.com",
--              "dob": "{month}/{day}/****",
--              "insurance_id": "{first_3}*****"
--          }},
--          "minimum_data_only": {"type": "boolean", "default": true},
--          "purpose_limitation": {"type": "boolean", "default": true}
--      }
--  }'::jsonb,
--  NOW(), 'a1b2c3d4-e5f6-7890-abcd-111111111111');
-- 
-- =============================================================================
-- 7. POLICIES TABLE (3 actual HIPAA policies)
-- =============================================================================
-- 
INSERT INTO policies (
    policy_id, name, description, category, policy_type, definition, version, 
    is_active, severity, applies_to_roles, created_at, updated_at, created_by
) VALUES 
-- Policy 1: Medical Record Sharing with Phone Redaction
('f1b2c3d4-e5f6-7890-abcd-111111111111', 'BCBS Minimum Necessary with Phone Redaction',
 'Secure sharing of medical records with automatic PHI redaction',
 'HIPAA Privacy Compliance', 'opa',
 '{
     "sharing_authorized_roles": ["HIPAA_CLINICAL_REVIEWER", "HIPAA_MEDICAL_DIRECTOR", "HIPAA_COMPLIANCE_OFFICER"],
     "redaction_required": true,
     "redaction_fields": ["phone"],
     "redaction_rules": {
         "phone": "({area_code}) ***-****"
     },
     "emergency_override": true,
     "audit_redaction": true,
     "external_sharing_allowed": false
 }'::jsonb,
 1, true, 'high',
 ARRAY['HIPAA_CLINICAL_REVIEWER', 'HIPAA_MEDICAL_DIRECTOR', 'HIPAA_COMPLIANCE_OFFICER'],
 NOW(), NOW(), 'a1b2c3d4-e5f6-7890-abcd-111111111111'),

-- Policy 2: Minimum Necessary Standard with Email Redaction
('f1b2c3d4-e5f6-7890-abcd-************', 'BCBS Minimum Necessary with Email Redaction',
 'Ensures only minimum necessary PHI is disclosed based on user role',
 'HIPAA Privacy Compliance', 'opa',
 '{
     "auto_redaction_enabled": true,
     "pii_fields_to_redact": ["email"],
     "role_based_visibility": true,
     "visibility_rules": {
         "CLINICAL_REVIEWER": ["medical_info", "basic_demographics"],
         "COMPLIANCE_OFFICER": ["access_logs", "audit_trails"],
         "MEDICAL_DIRECTOR": ["all_fields"],
         "CASE_MANAGER": ["contact_info", "insurance_info"]
     },
     "redaction_methods": {
         "email": "****@****.com"
     },
     "minimum_data_only": true,
     "purpose_limitation": true
 }'::jsonb,
 1, true, 'high',
 ARRAY['HIPAA_CLINICAL_REVIEWER', 'HIPAA_MEDICAL_DIRECTOR', 'HIPAA_CASE_MANAGER'],
 NOW(), NOW(), 'a1b2c3d4-e5f6-7890-abcd-111111111111'),

-- Policy 3: Minimum Necessary Standard with Auto-Redaction
('f1b2c3d4-e5f6-7890-abcd-************', 'BCBS Minimum Necessary with Auto-Redaction',
 'Ensures only minimum necessary PHI is disclosed based on user role',
 'HIPAA Privacy Compliance', 'opa',
 '{
     "auto_redaction_enabled": true,
     "pii_fields_to_redact": ["ssn", "address", "phone", "email", "dob", "insurance_id"],
     "role_based_visibility": true,
     "visibility_rules": {
         "CLINICAL_REVIEWER": ["medical_info", "basic_demographics"],
         "COMPLIANCE_OFFICER": ["access_logs", "audit_trails"],
         "MEDICAL_DIRECTOR": ["all_fields"],
         "CASE_MANAGER": ["contact_info", "insurance_info"]
     },
     "redaction_methods": {
         "ssn": "***-**-{last_4}",
         "phone": "({area_code}) ***-****",
         "address": "{city}, {state} {zip}",
         "email": "****@****.com",
         "dob": "{month}/{day}/****",
         "insurance_id": "{first_3}*****"
     },
     "minimum_data_only": true,
     "purpose_limitation": true
 }'::jsonb,
 1, true, 'high',
 ARRAY['HIPAA_CLINICAL_REVIEWER', 'HIPAA_MEDICAL_DIRECTOR', 'HIPAA_CASE_MANAGER'],
 NOW(), NOW(), 'a1b2c3d4-e5f6-7890-abcd-111111111111');

-- =============================================================================
-- 8. POLICY_GROUP_POLICIES TABLE (Link policies to the HIPAA group)
-- =============================================================================

INSERT INTO policy_group_policies (group_id, policy_id) VALUES
('d1b2c3d4-e5f6-7890-abcd-111111111111', 'f1b2c3d4-e5f6-7890-abcd-111111111111'),
('d1b2c3d4-e5f6-7890-abcd-111111111111', 'f1b2c3d4-e5f6-7890-abcd-************'),
('d1b2c3d4-e5f6-7890-abcd-111111111111', 'f1b2c3d4-e5f6-7890-abcd-************');

-- =============================================================================
-- 9. AGENT_POLICIES TABLE (Link agent to policies)
-- =============================================================================

INSERT INTO agent_policies (agent_id, policy_id, link_type) VALUES
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'f1b2c3d4-e5f6-7890-abcd-111111111111', 'via_group'),
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'f1b2c3d4-e5f6-7890-abcd-************', 'via_group'),
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'f1b2c3d4-e5f6-7890-abcd-************', 'via_group');

-- =============================================================================
-- 10. AGENT_ACCESS TABLE (Role-based access to the agent)
-- =============================================================================

INSERT INTO agent_access (agent_id, role_id, access_level, granted_by) VALUES
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'b1b2c3d4-e5f6-7890-abcd-111111111111', 'manage', 'a1b2c3d4-e5f6-7890-abcd-111111111111'),
('c1b2c3d4-e5f6-7890-abcd-111111111111', '************************************', 'view', 'a1b2c3d4-e5f6-7890-abcd-111111111111'),
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'b1b2c3d4-e5f6-7890-abcd-************', 'manage', 'a1b2c3d4-e5f6-7890-abcd-111111111111'),
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'b1b2c3d4-e5f6-7890-abcd-************', 'view', 'a1b2c3d4-e5f6-7890-abcd-111111111111'),
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'b1b2c3d4-e5f6-7890-abcd-************', 'manage', 'a1b2c3d4-e5f6-7890-abcd-111111111111');

-- =============================================================================
-- 11. SAMPLE AUDIT_LOG ENTRIES (10 realistic entries)
-- =============================================================================

INSERT INTO audit_log (
    user_id, action, resource_type, resource_id, new_values,
    ip_address, user_agent, timestamp
) VALUES 
-- Entry 1: PHI Access by Clinical Reviewer
('a1b2c3d4-e5f6-7890-abcd-************', 'HIPAA_PHI_ACCESS', 'patient_record', 
 uuid_generate_v4(),
 '{"access_type": "read", "redaction_applied": true, "fields_accessed": ["demographics", "medical_history"]}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
 NOW() - INTERVAL '2 hours'),

-- Entry 2: Policy Execution by Compliance Officer
('a1b2c3d4-e5f6-7890-abcd-************', 'HIPAA_POLICY_EXECUTION', 'policy', 'f1b2c3d4-e5f6-7890-abcd-111111111111',
 '{"execution_result": "allow", "redaction_count": 3, "compliance_score": 95}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
 NOW() - INTERVAL '1 hour'),

-- Entry 3: Medical Record Sharing with Redaction
('a1b2c3d4-e5f6-7890-abcd-************', 'HIPAA_RECORD_SHARING', 'medical_record',
 uuid_generate_v4(),
 '{"sharing_method": "secure_portal", "recipient": "external_specialist", "redaction_applied": true}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
 NOW() - INTERVAL '3 hours'),

-- Entry 4: Patient Consent Update
('a1b2c3d4-e5f6-7890-abcd-************', 'HIPAA_CONSENT_UPDATE', 'patient_consent',
 uuid_generate_v4(),
 '{"consent_status": "granted", "consent_types": ["treatment", "payment", "operations"]}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36',
 NOW() - INTERVAL '4 hours'),

-- Entry 5: Failed Login Attempt
('a1b2c3d4-e5f6-7890-abcd-************', 'HIPAA_FAILED_LOGIN', 'user_session',
 NULL,
 '{"failure_reason": "invalid_password", "attempt_count": 2, "source_ip": "*************"}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
 NOW() - INTERVAL '5 hours'),

-- Entry 6: Audit Report Generation
('a1b2c3d4-e5f6-7890-abcd-************', 'HIPAA_AUDIT_REPORT', 'system_report',
 uuid_generate_v4(),
 '{"report_type": "monthly_access", "records_count": 1247, "violations_found": 0}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
 NOW() - INTERVAL '6 hours'),

-- Entry 7: Emergency Override Used
('a1b2c3d4-e5f6-7890-abcd-************', 'HIPAA_EMERGENCY_OVERRIDE', 'policy_execution',
 'f1b2c3d4-e5f6-7890-abcd-************',
 '{"redaction_enabled": false, "override_reason": "medical_emergency", "patient_id": "12345"}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
 NOW() - INTERVAL '8 hours'),

-- Entry 8: Third-Party Data Request
('a1b2c3d4-e5f6-7890-abcd-************', 'HIPAA_THIRD_PARTY_REQUEST', 'data_sharing',
 uuid_generate_v4(),
 '{"requesting_entity": "Mayo Clinic", "baa_status": "verified", "data_types": ["lab_results", "imaging"]}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
 NOW() - INTERVAL '12 hours'),

-- Entry 9: Patient Access Request
('a1b2c3d4-e5f6-7890-abcd-************', 'HIPAA_PATIENT_ACCESS_REQUEST', 'patient_request',
 uuid_generate_v4(),
 '{"request_type": "medical_records", "delivery_method": "electronic", "patient_verified": true}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
 NOW() - INTERVAL '1 day'),

-- Entry 10: System Configuration Change
('a1b2c3d4-e5f6-7890-abcd-111111111111', 'HIPAA_CONFIG_CHANGE', 'system_config',
 uuid_generate_v4(),
 '{"redaction_timeout": 3600}'::jsonb,
 '************'::inet,
 'Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36',
 NOW() - INTERVAL '2 days');

-- =============================================================================
-- SUCCESS MESSAGE
-- =============================================================================

-- Display summary of inserted data
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🏥 HIPAA Sample Data Initialization Complete!';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Successfully inserted:';
    RAISE NOTICE '  • % Users', (SELECT COUNT(*) FROM users);
    RAISE NOTICE '  • % Roles', (SELECT COUNT(*) FROM roles);
    RAISE NOTICE '  • % User-Role assignments', (SELECT COUNT(*) FROM user_roles);
    RAISE NOTICE '  • % Agents', (SELECT COUNT(*) FROM agents);
    -- RAISE NOTICE '  • % Policy Templates', 0; -- Table removed, templates auto-generated
    RAISE NOTICE '  • % Policies', (SELECT COUNT(*) FROM policies);
    RAISE NOTICE '  • % Policy Groups', (SELECT COUNT(*) FROM policy_groups);
    RAISE NOTICE '  • % Policy-Group links', (SELECT COUNT(*) FROM policy_group_policies);
    RAISE NOTICE '  • % Agent-Policy links', (SELECT COUNT(*) FROM agent_policies);
    RAISE NOTICE '  • % Agent Access entries', (SELECT COUNT(*) FROM agent_access);
    RAISE NOTICE '  • % Audit Log entries', (SELECT COUNT(*) FROM audit_log);
    RAISE NOTICE '';
    RAISE NOTICE 'Database is ready for HIPAA compliance demos!';
    RAISE NOTICE '';
END $$;

COMMIT;