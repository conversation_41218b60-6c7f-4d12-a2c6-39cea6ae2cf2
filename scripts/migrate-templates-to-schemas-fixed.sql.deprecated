-- ==============================================================================
-- Template Management System - Migration Script (Fixed)
-- ==============================================================================
-- This script migrates templates from policy_templates table to policy_schemas
-- and adds auto-generation capabilities for templates
-- ==============================================================================

BEGIN;

-- Step 1: Add new columns to policy_schemas if they don't exist
-- ---------------------------------------------
DO $$
BEGIN
    -- Add default_template column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'policy_schemas' 
        AND column_name = 'default_template'
    ) THEN
        ALTER TABLE policy_schemas 
        ADD COLUMN default_template JSONB;
        
        RAISE NOTICE 'Added default_template column to policy_schemas';
    END IF;
    
    -- Add template_source column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'policy_schemas' 
        AND column_name = 'template_source'
    ) THEN
        ALTER TABLE policy_schemas 
        ADD COLUMN template_source VARCHAR(20) DEFAULT 'auto_generated'
        CHECK (template_source IN ('auto_generated', 'manual_override', 'external_provided', 'migrated_legacy'));
        
        RAISE NOTICE 'Added template_source column to policy_schemas';
    END IF;
END $$;

-- Step 2: Create template generation function
-- ---------------------------------------------
CREATE OR REPLACE FUNCTION generate_default_template(schema_content JSONB)
RETURNS JSONB AS $$
DECLARE
    template JSONB := '{}';
    prop_key TEXT;
    prop_def JSONB;
    nested_template JSONB;
BEGIN
    -- Handle properties
    IF schema_content ? 'properties' THEN
        FOR prop_key, prop_def IN 
            SELECT key, value FROM jsonb_each(schema_content->'properties')
        LOOP
            -- Check for default value
            IF prop_def ? 'default' THEN
                template := jsonb_set(template, 
                    ARRAY[prop_key], 
                    prop_def->'default');
            -- Check for const value
            ELSIF prop_def ? 'const' THEN
                template := jsonb_set(template, 
                    ARRAY[prop_key], 
                    prop_def->'const');
            -- Check for enum and use first value
            ELSIF prop_def ? 'enum' AND jsonb_array_length(prop_def->'enum') > 0 THEN
                template := jsonb_set(template, 
                    ARRAY[prop_key], 
                    prop_def->'enum'->0);
            -- Handle nested objects
            ELSIF prop_def->>'type' = 'object' THEN
                nested_template := generate_default_template(prop_def);
                template := jsonb_set(template, 
                    ARRAY[prop_key], 
                    nested_template);
            -- Handle arrays with default items
            ELSIF prop_def->>'type' = 'array' THEN
                IF prop_def->'items' ? 'enum' THEN
                    template := jsonb_set(template, 
                        ARRAY[prop_key], 
                        jsonb_build_array(prop_def->'items'->'enum'->0));
                ELSE
                    template := jsonb_set(template, 
                        ARRAY[prop_key], 
                        '[]'::jsonb);
                END IF;
            -- Set type-specific defaults for required fields
            ELSIF schema_content->'required' @> to_jsonb(prop_key) THEN
                CASE prop_def->>'type'
                    WHEN 'string' THEN
                        template := jsonb_set(template, ARRAY[prop_key], '""');
                    WHEN 'number', 'integer' THEN
                        template := jsonb_set(template, ARRAY[prop_key], '0');
                    WHEN 'boolean' THEN
                        template := jsonb_set(template, ARRAY[prop_key], 'false');
                    WHEN 'array' THEN
                        template := jsonb_set(template, ARRAY[prop_key], '[]');
                    WHEN 'object' THEN
                        template := jsonb_set(template, ARRAY[prop_key], '{}');
                END CASE;
            END IF;
        END LOOP;
    END IF;
    
    RETURN template;
END;
$$ LANGUAGE plpgsql;

-- Step 3: Migrate existing templates from policy_templates table
-- ---------------------------------------------
DO $$
DECLARE
    template_record RECORD;
    schema_name_val VARCHAR(255);
    migrated_count INTEGER := 0;
BEGIN
    -- Check if policy_templates table exists
    IF EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'policy_templates'
    ) THEN
        FOR template_record IN 
            SELECT * FROM policy_templates
        LOOP
            -- Map category to schema_name
            schema_name_val := CASE template_record.category
                WHEN 'Data Privacy' THEN 'data_privacy'
                WHEN 'Access Control' THEN 'access_control'
                WHEN 'Medical Privacy' THEN 'medical_privacy'
                WHEN 'Compliance' THEN 'compliance'
                ELSE LOWER(REPLACE(template_record.category, ' ', '_'))
            END;
            
            -- Update the corresponding schema
            UPDATE policy_schemas
            SET 
                default_template = template_record.template_definition,
                template_source = 'migrated_legacy',
                updated_at = CURRENT_TIMESTAMP
            WHERE schema_name = schema_name_val
            AND is_active = true;
            
            IF FOUND THEN
                migrated_count := migrated_count + 1;
                RAISE NOTICE 'Migrated template for schema: %', schema_name_val;
            END IF;
        END LOOP;
        
        RAISE NOTICE 'Migration complete. Migrated % templates.', migrated_count;
    ELSE
        RAISE NOTICE 'policy_templates table does not exist. Skipping migration.';
    END IF;
END $$;

-- Step 4: Auto-generate templates for schemas without them
-- ---------------------------------------------
DO $$
DECLARE
    schema_record RECORD;
    generated_template JSONB;
    generated_count INTEGER := 0;
BEGIN
    RAISE NOTICE 'Auto-generating templates for schemas without defaults...';
    
    FOR schema_record IN 
        SELECT id, schema_name, schema_content 
        FROM policy_schemas 
        WHERE is_active = true 
        AND default_template IS NULL
    LOOP
        -- Generate template from schema
        generated_template := generate_default_template(schema_record.schema_content);
        
        -- Update schema with generated template
        UPDATE policy_schemas
        SET 
            default_template = generated_template,
            template_source = 'auto_generated',
            updated_at = CURRENT_TIMESTAMP
        WHERE id = schema_record.id;
        
        generated_count := generated_count + 1;
        RAISE NOTICE 'Generated template for schema: %', schema_record.schema_name;
    END LOOP;
    
    RAISE NOTICE 'Template generation complete. Generated % templates.', generated_count;
END $$;

-- Step 5: Log migration in audit_log (only if audit_log exists and has proper columns)
-- ---------------------------------------------
DO $$
BEGIN
    -- Check if audit_log table exists
    IF EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'audit_log'
    ) THEN
        -- Check what columns exist in audit_log
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'audit_log' 
            AND column_name = 'user_id'
        ) THEN
            -- Insert audit log entry (adjust based on actual schema)
            INSERT INTO audit_log (
                log_id,
                user_id,
                action,
                resource_type,
                resource_name,
                timestamp
            ) VALUES (
                uuid_generate_v4(),
                '00000000-0000-0000-0000-000000000000', -- System user
                'MIGRATE',
                'policy_templates',
                'template_migration',
                CURRENT_TIMESTAMP
            );
            RAISE NOTICE 'Migration logged in audit_log';
        ELSE
            RAISE NOTICE 'audit_log table exists but has different schema. Skipping audit logging.';
        END IF;
    ELSE
        RAISE NOTICE 'audit_log table does not exist. Skipping audit logging.';
    END IF;
END $$;

-- Step 6: Create indexes for better performance
-- ---------------------------------------------
CREATE INDEX IF NOT EXISTS idx_policy_schemas_template_source 
ON policy_schemas(template_source) 
WHERE is_active = true;

-- Step 7: Verification queries
-- ---------------------------------------------
DO $$
DECLARE
    total_schemas INTEGER;
    schemas_with_templates INTEGER;
    schemas_without_templates INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_schemas 
    FROM policy_schemas WHERE is_active = true;
    
    SELECT COUNT(*) INTO schemas_with_templates 
    FROM policy_schemas 
    WHERE is_active = true AND default_template IS NOT NULL;
    
    SELECT COUNT(*) INTO schemas_without_templates 
    FROM policy_schemas 
    WHERE is_active = true AND default_template IS NULL;
    
    RAISE NOTICE '';
    RAISE NOTICE '=====================================';
    RAISE NOTICE 'MIGRATION SUMMARY';
    RAISE NOTICE '=====================================';
    RAISE NOTICE 'Total active schemas: %', total_schemas;
    RAISE NOTICE 'Schemas with templates: %', schemas_with_templates;
    RAISE NOTICE 'Schemas without templates: %', schemas_without_templates;
    RAISE NOTICE '=====================================';
END $$;

COMMIT;

-- Final verification query
SELECT 
    schema_name,
    template_source,
    default_template IS NOT NULL as has_template,
    updated_at
FROM policy_schemas
WHERE is_active = true
ORDER BY schema_name;