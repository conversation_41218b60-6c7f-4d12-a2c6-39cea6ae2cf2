-- =====================================================
-- Migration Script: Policy Templates to Policy Schemas
-- =====================================================
-- Purpose: Migrate from policy_templates table to policy_schemas.default_template
-- Author: Vitea.ai Team
-- Date: 2025-08-29
-- =====================================================

-- Start transaction for safe migration
BEGIN;

-- Step 1: Add new columns to policy_schemas if they don't exist
-- ---------------------------------------------
ALTER TABLE policy_schemas 
ADD COLUMN IF NOT EXISTS default_template JSONB,
ADD COLUMN IF NOT EXISTS template_source VARCHAR(20) DEFAULT 'auto_generated' 
    CHECK (template_source IN ('auto_generated', 'manual_override', 'external_provided', 'migrated_legacy'));

-- Add comment for documentation
COMMENT ON COLUMN policy_schemas.default_template IS 'Default template values for this policy type';
COMMENT ON COLUMN policy_schemas.template_source IS 'Source of the template: auto_generated from schema, manual_override by admin, external_provided by external system, or migrated_legacy from old system';

-- Step 2: Create template generation function
-- ---------------------------------------------
CREATE OR REPLACE FUNCTION generate_default_template(schema_json JSONB)
RETURNS JSONB AS $$
DECLARE
    template JSONB = '{}';
    prop_key TEXT;
    prop_def JSONB;
    prop_type TEXT;
    required_fields TEXT[];
BEGIN
    -- Extract required fields
    IF schema_json ? 'required' THEN
        required_fields := ARRAY(SELECT jsonb_array_elements_text(schema_json->'required'));
    ELSE
        required_fields := '{}';
    END IF;

    -- Iterate through schema properties
    FOR prop_key, prop_def IN 
        SELECT * FROM jsonb_each(schema_json->'properties')
    LOOP
        -- Extract default value based on priority
        -- 1. Explicit default value
        IF prop_def ? 'default' THEN
            template = jsonb_set(template, ARRAY[prop_key], prop_def->'default');
            
        -- 2. Const value (fixed value)
        ELSIF prop_def ? 'const' THEN
            template = jsonb_set(template, ARRAY[prop_key], prop_def->'const');
            
        -- 3. First enum value
        ELSIF prop_def->'enum' IS NOT NULL AND jsonb_array_length(prop_def->'enum') > 0 THEN
            template = jsonb_set(template, ARRAY[prop_key], prop_def->'enum'->0);
            
        -- 4. Type-specific defaults for required fields
        ELSIF prop_key = ANY(required_fields) THEN
            prop_type := prop_def->>'type';
            CASE prop_type
                WHEN 'string' THEN
                    template = jsonb_set(template, ARRAY[prop_key], '""'::jsonb);
                WHEN 'number' THEN
                    template = jsonb_set(template, ARRAY[prop_key], '0'::jsonb);
                WHEN 'integer' THEN
                    template = jsonb_set(template, ARRAY[prop_key], '0'::jsonb);
                WHEN 'boolean' THEN
                    template = jsonb_set(template, ARRAY[prop_key], 'false'::jsonb);
                WHEN 'array' THEN
                    template = jsonb_set(template, ARRAY[prop_key], '[]'::jsonb);
                WHEN 'object' THEN
                    -- Recursively generate for nested objects if they have properties
                    IF prop_def ? 'properties' THEN
                        template = jsonb_set(template, ARRAY[prop_key], 
                            generate_default_template(prop_def));
                    ELSE
                        template = jsonb_set(template, ARRAY[prop_key], '{}'::jsonb);
                    END IF;
                ELSE
                    -- Default to null for unknown types
                    template = jsonb_set(template, ARRAY[prop_key], 'null'::jsonb);
            END CASE;
        END IF;
    END LOOP;
    
    RETURN template;
END;
$$ LANGUAGE plpgsql;

-- Step 3: Migrate existing templates from policy_templates to policy_schemas
-- ---------------------------------------------
DO $$
DECLARE
    migrated_count INTEGER := 0;
    template_rec RECORD;
    matching_schema_name VARCHAR(255);
BEGIN
    -- Check if policy_templates table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables 
               WHERE table_schema = 'public' 
               AND table_name = 'policy_templates') THEN
        
        RAISE NOTICE 'Starting migration from policy_templates to policy_schemas...';
        
        -- Iterate through policy_templates
        FOR template_rec IN 
            SELECT * FROM policy_templates 
            WHERE is_system_template = true
        LOOP
            -- Map category to schema_name
            -- Handle different naming conventions
            matching_schema_name := CASE 
                WHEN template_rec.category = 'data_masking' THEN 'data_privacy'
                WHEN template_rec.category = 'content_safety' THEN 'content_safety'
                ELSE template_rec.category
            END;
            
            -- Update policy_schemas with template if schema exists
            UPDATE policy_schemas 
            SET 
                default_template = template_rec.template_definition,
                template_source = 'migrated_legacy',
                updated_at = CURRENT_TIMESTAMP
            WHERE 
                schema_name = matching_schema_name
                AND default_template IS NULL; -- Don't overwrite existing templates
            
            IF FOUND THEN
                migrated_count := migrated_count + 1;
                RAISE NOTICE 'Migrated template for schema: %', matching_schema_name;
            END IF;
        END LOOP;
        
        RAISE NOTICE 'Migration complete. Migrated % templates.', migrated_count;
    ELSE
        RAISE NOTICE 'policy_templates table not found. Skipping migration.';
    END IF;
END $$;

-- Step 4: Auto-generate templates for schemas without templates
-- ---------------------------------------------
DO $$
DECLARE
    generated_count INTEGER := 0;
    schema_rec RECORD;
    generated_template JSONB;
BEGIN
    RAISE NOTICE 'Auto-generating templates for schemas without defaults...';
    
    FOR schema_rec IN 
        SELECT schema_name, schema_content 
        FROM policy_schemas 
        WHERE is_active = true 
        AND default_template IS NULL
        AND schema_content IS NOT NULL
    LOOP
        -- Generate template from schema
        generated_template := generate_default_template(schema_rec.schema_content);
        
        -- Add the type field based on schema name
        generated_template := jsonb_set(generated_template, '{type}', 
            to_jsonb(schema_rec.schema_name));
        
        -- Update the schema with generated template
        UPDATE policy_schemas 
        SET 
            default_template = generated_template,
            template_source = 'auto_generated',
            updated_at = CURRENT_TIMESTAMP
        WHERE schema_name = schema_rec.schema_name;
        
        generated_count := generated_count + 1;
        RAISE NOTICE 'Generated template for schema: %', schema_rec.schema_name;
    END LOOP;
    
    RAISE NOTICE 'Template generation complete. Generated % templates.', generated_count;
END $$;

-- Step 5: Create audit log entry for migration
-- ---------------------------------------------
INSERT INTO audit_log (
    user_id, 
    action, 
    resource_type, 
    resource_id, 
    details,
    created_at
) VALUES (
    '00000000-0000-0000-0000-000000000000', -- System user
    'MIGRATE',
    'policy_templates',
    'migration',
    jsonb_build_object(
        'migration_name', 'policy_templates_to_schemas',
        'migration_date', CURRENT_TIMESTAMP,
        'templates_migrated', (
            SELECT COUNT(*) 
            FROM policy_schemas 
            WHERE template_source = 'migrated_legacy'
        ),
        'templates_generated', (
            SELECT COUNT(*) 
            FROM policy_schemas 
            WHERE template_source = 'auto_generated'
        )
    ),
    CURRENT_TIMESTAMP
);

-- Step 6: Create indexes for better performance
-- ---------------------------------------------
CREATE INDEX IF NOT EXISTS idx_policy_schemas_template_source 
ON policy_schemas(template_source) 
WHERE is_active = true;

-- Step 7: Verification queries
-- ---------------------------------------------
DO $$
DECLARE
    total_schemas INTEGER;
    schemas_with_templates INTEGER;
    schemas_without_templates INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_schemas 
    FROM policy_schemas WHERE is_active = true;
    
    SELECT COUNT(*) INTO schemas_with_templates 
    FROM policy_schemas 
    WHERE is_active = true AND default_template IS NOT NULL;
    
    SELECT COUNT(*) INTO schemas_without_templates 
    FROM policy_schemas 
    WHERE is_active = true AND default_template IS NULL;
    
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Migration Summary:';
    RAISE NOTICE '  Total active schemas: %', total_schemas;
    RAISE NOTICE '  Schemas with templates: %', schemas_with_templates;
    RAISE NOTICE '  Schemas without templates: %', schemas_without_templates;
    RAISE NOTICE '';
    RAISE NOTICE 'Template sources breakdown:';
    
    FOR r IN 
        SELECT template_source, COUNT(*) as cnt 
        FROM policy_schemas 
        WHERE is_active = true 
        GROUP BY template_source
    LOOP
        RAISE NOTICE '  %: %', COALESCE(r.template_source, 'NULL'), r.cnt;
    END LOOP;
    
    RAISE NOTICE '========================================';
END $$;

-- Step 8: Drop policy_templates table (commented out for safety)
-- ---------------------------------------------
-- IMPORTANT: Uncomment these lines only after verifying the migration was successful
-- and all systems are working with the new template structure

-- DROP TABLE IF EXISTS policy_templates CASCADE;
-- RAISE NOTICE 'Dropped policy_templates table';

-- Commit the transaction
COMMIT;

-- Post-migration notes:
-- 1. Verify all templates are correctly migrated
-- 2. Test policy creation with new templates
-- 3. Update application code to use new template endpoints
-- 4. After verification, uncomment and run the DROP TABLE command
-- 5. Update all documentation to reflect new template structure