--
-- PostgreSQL database dump
--

\restrict 8ln3blCnfbFGB8aoquWfZr631LyOekyNcRyZQ1UOu5VGo8rHbmZ0wky15vxcinT

-- Dumped from database version 15.14
-- Dumped by pg_dump version 15.14

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.users (user_id, azure_ad_id, email, first_name, last_name, role, is_active, created_at, updated_at, created_by, updated_by, department, status, risk_score, last_login, two_factor_enabled) FROM stdin;
00000000-0000-0000-0000-000000000000	system	<EMAIL>	System	User	system	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00	\N	\N	\N	active	0.00	\N	f
a1b2c3d4-e5f6-7890-abcd-111111111111	azure-admin-placeholder-001	<EMAIL>	HIPAA	Administrator	admin	t	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	\N	Information Technology	active	0.00	\N	t
a1b2c3d4-e5f6-7890-abcd-222222222222	azure-sarah-martinez-002	<EMAIL>	Sarah	Martinez	user	t	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	\N	Medical Affairs	active	1.50	\N	t
a1b2c3d4-e5f6-7890-abcd-333333333333	azure-jennifer-chen-003	<EMAIL>	Jennifer	Chen	user	t	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	\N	Privacy & Compliance	active	0.50	\N	t
************************************	azure-michael-rodriguez-004	<EMAIL>	Michael	Rodriguez	user	t	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	\N	Clinical Review	active	1.00	\N	f
a1b2c3d4-e5f6-7890-abcd-**********55	azure-lisa-thompson-005	<EMAIL>	Lisa	Thompson	user	t	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	\N	Care Management	active	0.80	\N	t
\.


--
-- Data for Name: agents; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.agents (agent_id, name, description, agent_type, endpoint_url, is_active, configuration, created_at, updated_at, created_by, vendor, department, risk_score, status, deleted_at) FROM stdin;
1b126746-792f-4a37-b052-537962196a48	Test Medical Agent	Test agent for medical privacy policy	policy_engine	\N	t	{}	2025-08-27 22:01:47.268285+00	2025-08-27 22:01:47.268285+00	00000000-0000-0000-0000-000000000000	\N	\N	0.00	active	\N
c1b2c3d4-e5f6-7890-abcd-111111111111	BCBS HIPAA Compliance Agent test	AI agent specialized in HIPAA compliance monitoring and PHI protection test	healthcare_hipaa_compliance	\N	t	{"auto_log_access": true, "audit_all_access": true, "redaction_enabled": true, "compliance_version": "HIPAA_2023", "emergency_override": true, "require_two_factor": false, "alert_on_violations": true, "max_session_duration": 3600, "supported_redaction_types": ["ssn", "phone", "address", "email", "dob", "insurance_id"]}	2025-08-27 17:57:54.32967+00	2025-08-28 05:08:06.220713+00	a1b2c3d4-e5f6-7890-abcd-111111111111	BCBS Blue Cross	Privacy & Compliance	0.20	active	\N
\.


--
-- Data for Name: roles; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.roles (role_id, code, name, description, is_system_role, created_at, updated_at, created_by, permissions) FROM stdin;
b1b2c3d4-e5f6-7890-abcd-111111111111	HIPAA_COMPLIANCE_OFFICER	HIPAA Privacy and Compliance Officer	Responsible for HIPAA compliance monitoring and privacy oversight	f	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	{}
b1b2c3d4-e5f6-7890-abcd-222222222222	HIPAA_CLINICAL_REVIEWER	Clinical Staff with PHI Access	Healthcare staff authorized to review patient medical information	f	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	{}
b1b2c3d4-e5f6-7890-abcd-333333333333	HIPAA_MEDICAL_DIRECTOR	Senior Physician with Full Access	Senior medical staff with unrestricted access to patient data	f	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	{}
b1b2c3d4-e5f6-7890-abcd-444444444444	HIPAA_CASE_MANAGER	Patient Care Coordinator	Staff responsible for coordinating patient care across services	f	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	{}
b1b2c3d4-e5f6-7890-abcd-**********55	HIPAA_ADMIN	System Administrator	Technical staff with system administration privileges	f	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	\N	{}
\.


--
-- Data for Name: agent_access; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.agent_access (agent_id, role_id, access_level, granted_by) FROM stdin;
c1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-111111111111	manage	a1b2c3d4-e5f6-7890-abcd-111111111111
c1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-222222222222	view	a1b2c3d4-e5f6-7890-abcd-111111111111
c1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-333333333333	manage	a1b2c3d4-e5f6-7890-abcd-111111111111
c1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-444444444444	view	a1b2c3d4-e5f6-7890-abcd-111111111111
c1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-**********55	manage	a1b2c3d4-e5f6-7890-abcd-111111111111
\.


--
-- Data for Name: policies; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policies (policy_id, name, description, category, policy_type, definition, version, is_active, severity, applies_to_roles, created_at, updated_at, created_by, updated_by, rego_code, blob_container, blob_path, blob_url, rego_template_id, opa_sync_status, last_rego_generation, rego_generation_error, rego_version, original_policy_id, cloned_from_policy_name, deleted_at, guardrail_id) FROM stdin;
f1b2c3d4-e5f6-7890-abcd-111111111111	BCBS Minimum Necessary with Phone Redaction	Secure sharing of medical records with automatic PHI redaction	HIPAA Privacy Compliance	opa	{"audit_redaction": true, "redaction_rules": {"phone": "({area_code}) ***-****"}, "redaction_fields": ["phone"], "emergency_override": true, "redaction_required": true, "external_sharing_allowed": false, "sharing_authorized_roles": ["HIPAA_CLINICAL_REVIEWER", "HIPAA_MEDICAL_DIRECTOR", "HIPAA_COMPLIANCE_OFFICER"]}	1	t	high	{HIPAA_CLINICAL_REVIEWER,HIPAA_MEDICAL_DIRECTOR,HIPAA_COMPLIANCE_OFFICER}	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	a1b2c3d4-e5f6-7890-abcd-111111111111	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
f1b2c3d4-e5f6-7890-abcd-222222222222	BCBS Minimum Necessary with Email Redaction	Ensures only minimum necessary PHI is disclosed based on user role	HIPAA Privacy Compliance	opa	{"visibility_rules": {"CASE_MANAGER": ["contact_info", "insurance_info"], "MEDICAL_DIRECTOR": ["all_fields"], "CLINICAL_REVIEWER": ["medical_info", "basic_demographics"], "COMPLIANCE_OFFICER": ["access_logs", "audit_trails"]}, "minimum_data_only": true, "redaction_methods": {"email": "****@****.com"}, "purpose_limitation": true, "pii_fields_to_redact": ["email"], "role_based_visibility": true, "auto_redaction_enabled": true}	1	t	high	{HIPAA_CLINICAL_REVIEWER,HIPAA_MEDICAL_DIRECTOR,HIPAA_CASE_MANAGER}	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	a1b2c3d4-e5f6-7890-abcd-111111111111	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
f1b2c3d4-e5f6-7890-abcd-333333333333	BCBS Minimum Necessary with Auto-Redaction	Ensures only minimum necessary PHI is disclosed based on user role	HIPAA Privacy Compliance	opa	{"visibility_rules": {"CASE_MANAGER": ["contact_info", "insurance_info"], "MEDICAL_DIRECTOR": ["all_fields"], "CLINICAL_REVIEWER": ["medical_info", "basic_demographics"], "COMPLIANCE_OFFICER": ["access_logs", "audit_trails"]}, "minimum_data_only": true, "redaction_methods": {"dob": "{month}/{day}/****", "ssn": "***-**-{last_4}", "email": "****@****.com", "phone": "({area_code}) ***-****", "address": "{city}, {state} {zip}", "insurance_id": "{first_3}*****"}, "purpose_limitation": true, "pii_fields_to_redact": ["ssn", "address", "phone", "email", "dob", "insurance_id"], "role_based_visibility": true, "auto_redaction_enabled": true}	1	t	high	{HIPAA_CLINICAL_REVIEWER,HIPAA_MEDICAL_DIRECTOR,HIPAA_CASE_MANAGER}	2025-08-27 17:57:54.32967+00	2025-08-27 17:57:54.32967+00	a1b2c3d4-e5f6-7890-abcd-111111111111	\N	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
00accc7f-7fd6-42cd-acbd-e9016b48eaec	Test Database Schema Policy	Testing policy creation with database schemas	Access Control	access_control	{"type": "access_control", "enabled": true, "severity": "medium", "allowed_roles": ["admin", "user"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}	1	t	medium	\N	2025-08-27 22:17:01.52259+00	2025-09-01 04:10:43.595437+00	00000000-0000-0000-0000-000000000000	00000000-0000-0000-0000-000000000000	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
4855b086-ab50-4488-9094-58f0be3f2cf3	Test Medical Privacy Policy	Testing complete policy lifecycle with database schemas	medical_privacy	medical_privacy	{"type": "medical_privacy", "enabled": true, "severity": "high", "allowed_roles": ["doctor", "nurse", "admin"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders", "medical_record_number"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}	1	t	high	\N	2025-08-27 21:38:06.072074+00	2025-09-01 04:11:33.146553+00	00000000-0000-0000-0000-000000000000	00000000-0000-0000-0000-000000000000	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
f4c1a348-7f94-4ed5-908c-4aee04e82f37	Test DB Schema Policy **********	Testing policy creation with database schemas	Access Control	access_control	{"type": "access_control", "enabled": false, "severity": "medium", "allowed_roles": ["admin", "user"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}	1	f	medium	\N	2025-08-27 22:18:03.518299+00	2025-09-01 04:11:55.191684+00	00000000-0000-0000-0000-000000000000	00000000-0000-0000-0000-000000000000	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2	Demo Medical Privacy Policy	Test medical privacy policy for demo flow - UPDATED	medical_privacy	opa	{"type": "medical_privacy", "enabled": false, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}	1	f	high	{doctor,nurse}	2025-08-28 04:27:16.865765+00	2025-09-01 04:11:52.514032+00	00000000-0000-0000-0000-000000000000	00000000-0000-0000-0000-000000000000	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
5c36b6fd-c0ce-40e1-9681-75c2d500d588	Test Fix Policy **********	Testing after fixing JsonStructureGuide	data_privacy	opa	{"type": "data_privacy", "enabled": true, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}	1	t	high	{admin,manager}	2025-08-27 22:30:11.908103+00	2025-08-28 04:04:53.021326+00	00000000-0000-0000-0000-000000000000	00000000-0000-0000-0000-000000000000	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
fb33044a-3bcc-4b23-9865-32131155c11c	E2E Emergency Policy 1	Emergency operational policy for testing	operational	e2e_comprehensive_test	{"tags": ["emergency", "operational", "activated"], "enabled": true, "priority": 100, "policy_type": "emergency", "access_control": {"max_sessions": 1, "allowed_roles": ["admin"]}, "severity_level": "emergency"}	1	t	emergency	\N	2025-08-31 10:43:52.203793+00	2025-08-31 10:44:13.70568+00	00000000-0000-0000-0000-000000000000	00000000-0000-0000-0000-000000000000	\N	rego-policies	\N	\N	\N	pending	\N	\N	1	\N	\N	\N	\N
\.


--
-- Data for Name: agent_policies; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.agent_policies (agent_id, policy_id, link_type) FROM stdin;
c1b2c3d4-e5f6-7890-abcd-111111111111	f1b2c3d4-e5f6-7890-abcd-111111111111	via_group
c1b2c3d4-e5f6-7890-abcd-111111111111	f1b2c3d4-e5f6-7890-abcd-222222222222	via_group
c1b2c3d4-e5f6-7890-abcd-111111111111	f1b2c3d4-e5f6-7890-abcd-333333333333	via_group
\.


--
-- Data for Name: policy_groups; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policy_groups (group_id, name, description, is_template, severity, status, version, tags, created_at, updated_at, created_by, deleted_at) FROM stdin;
11a01996-cb91-40ef-88c1-8e71328ca7f4	PI Bundle	PI Bun	t	medium	deprecated	v1.0.0	{}	2025-08-27 22:04:32.76169+00	2025-09-01 04:46:00.200881+00	\N	\N
d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	Test Medical Group	Test policy group for medical privacy policies	f	medium	active	v1.0.0	{}	2025-08-27 22:01:09.725675+00	2025-08-27 22:01:09.725675+00	\N	\N
bf6bde98-2093-4da8-b6d8-3929ef674aff	Test Policy Group Demo	Test group for demo flow verification	f	medium	active	v1.0.0	{}	2025-08-28 04:25:26.763433+00	2025-08-28 04:25:26.763433+00	\N	\N
d1b2c3d4-e5f6-7890-abcd-111111111111	HIPAA Compliance Policy Suite	Comprehensive HIPAA privacy and security policies for healthcare operations	f	medium	active	v1.0.0	{"Patient Intake",CDI}	2025-08-27 17:57:54.32967+00	2025-08-29 22:03:18.942228+00	a1b2c3d4-e5f6-7890-abcd-111111111111	\N
71e6509d-15af-44de-be9d-3b3040f7d6ed	E2E Test Policy Group	Policy group created for comprehensive end-to-end testing	f	high	active	v1.0.0	{testing,e2e,automation,validation}	2025-08-31 10:42:23.593801+00	2025-08-31 10:42:23.593801+00	\N	\N
\.


--
-- Data for Name: policy_group_policies; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policy_group_policies (group_id, policy_id) FROM stdin;
d1b2c3d4-e5f6-7890-abcd-111111111111	f1b2c3d4-e5f6-7890-abcd-111111111111
d1b2c3d4-e5f6-7890-abcd-111111111111	f1b2c3d4-e5f6-7890-abcd-222222222222
d1b2c3d4-e5f6-7890-abcd-111111111111	f1b2c3d4-e5f6-7890-abcd-333333333333
d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	4855b086-ab50-4488-9094-58f0be3f2cf3
d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	5c36b6fd-c0ce-40e1-9681-75c2d500d588
bf6bde98-2093-4da8-b6d8-3929ef674aff	3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2
71e6509d-15af-44de-be9d-3b3040f7d6ed	fb33044a-3bcc-4b23-9865-32131155c11c
\.


--
-- Data for Name: agent_role_policies; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.agent_role_policies (agent_id, role_id, group_id, policy_id, created_at) FROM stdin;
c1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-111111111111	d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	5c36b6fd-c0ce-40e1-9681-75c2d500d588	2025-08-28 05:14:25.257781+00
c1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-444444444444	d1b2c3d4-e5f6-7890-abcd-111111111111	f1b2c3d4-e5f6-7890-abcd-333333333333	2025-09-01 04:16:14.781017+00
\.


--
-- Data for Name: alembic_version; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.alembic_version (version_num) FROM stdin;
\.


--
-- Data for Name: audit_log; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.audit_log (log_id, user_id, action, resource_type, resource_id, old_values, new_values, ip_address, user_agent, "timestamp", session_id, request_id, user_role, resource_name, access_level, data_classification) FROM stdin;
45633e22-7876-4d26-a4c2-678ae42d558a	00000000-0000-0000-0000-000000000000	schema_creation	database	\N	\N	{"update": "Complete database schema created from consolidated migration files"}	\N	\N	2025-08-27 17:57:54.285266+00	\N	\N	\N	\N	\N	\N
3735f69f-327f-4a82-abd4-fd32b2f252c9	************************************	HIPAA_PHI_ACCESS	patient_record	7f18997e-873f-4a81-a866-e41d2830b227	\N	{"access_type": "read", "fields_accessed": ["demographics", "medical_history"], "redaction_applied": true}	*************	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36	2025-08-27 15:57:54.32967+00	\N	\N	\N	\N	\N	\N
4f64540f-8b2c-4347-b4a2-c432b36fca49	a1b2c3d4-e5f6-7890-abcd-333333333333	HIPAA_POLICY_EXECUTION	policy	f1b2c3d4-e5f6-7890-abcd-111111111111	\N	{"redaction_count": 3, "compliance_score": 95, "execution_result": "allow"}	*************	Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36	2025-08-27 16:57:54.32967+00	\N	\N	\N	\N	\N	\N
7216d0c4-fbea-42a6-bd3b-3538de460378	a1b2c3d4-e5f6-7890-abcd-222222222222	HIPAA_RECORD_SHARING	medical_record	4fcd6ab2-b295-47ca-bde8-6be4d7802052	\N	{"recipient": "external_specialist", "sharing_method": "secure_portal", "redaction_applied": true}	*************	Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0	2025-08-27 14:57:54.32967+00	\N	\N	\N	\N	\N	\N
0de24c4a-b5c9-4707-ab14-8d368e065122	a1b2c3d4-e5f6-7890-abcd-**********55	HIPAA_CONSENT_UPDATE	patient_consent	2f32307c-edfb-4772-a124-66c59ba5c397	\N	{"consent_types": ["treatment", "payment", "operations"], "consent_status": "granted"}	*************	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36	2025-08-27 13:57:54.32967+00	\N	\N	\N	\N	\N	\N
f94160a8-d6b9-4333-bd7d-d72f6feace66	************************************	HIPAA_FAILED_LOGIN	user_session	\N	\N	{"source_ip": "*************", "attempt_count": 2, "failure_reason": "invalid_password"}	*************	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36	2025-08-27 12:57:54.32967+00	\N	\N	\N	\N	\N	\N
b367ecb0-c5cc-46ed-a4ba-3ec34aa66de8	a1b2c3d4-e5f6-7890-abcd-333333333333	HIPAA_AUDIT_REPORT	system_report	0888dc4c-ee8c-4433-b6a4-d428b58c1a44	\N	{"report_type": "monthly_access", "records_count": 1247, "violations_found": 0}	*************	Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36	2025-08-27 11:57:54.32967+00	\N	\N	\N	\N	\N	\N
e2e2e897-20b6-4361-b8da-e673991d8457	a1b2c3d4-e5f6-7890-abcd-222222222222	HIPAA_EMERGENCY_OVERRIDE	policy_execution	f1b2c3d4-e5f6-7890-abcd-222222222222	\N	{"patient_id": "12345", "override_reason": "medical_emergency", "redaction_enabled": false}	*************	Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0	2025-08-27 09:57:54.32967+00	\N	\N	\N	\N	\N	\N
408e783d-3d54-4fe8-866c-6a31bad6fe59	a1b2c3d4-e5f6-7890-abcd-333333333333	HIPAA_THIRD_PARTY_REQUEST	data_sharing	7ccab097-6a1f-4154-8b65-7f2e9fee1c99	\N	{"baa_status": "verified", "data_types": ["lab_results", "imaging"], "requesting_entity": "Mayo Clinic"}	*************	Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36	2025-08-27 05:57:54.32967+00	\N	\N	\N	\N	\N	\N
aa7077bf-7295-4533-9ceb-8acd4f766bb2	a1b2c3d4-e5f6-7890-abcd-**********55	HIPAA_PATIENT_ACCESS_REQUEST	patient_request	1f14246c-3936-4e67-b2b4-7e3d655c02c9	\N	{"request_type": "medical_records", "delivery_method": "electronic", "patient_verified": true}	*************	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36	2025-08-26 17:57:54.32967+00	\N	\N	\N	\N	\N	\N
3c1462b0-de84-4fa3-8783-14001819dc8f	a1b2c3d4-e5f6-7890-abcd-111111111111	HIPAA_CONFIG_CHANGE	system_config	9af31731-2d43-4370-ab5b-b16dbd8fa74d	\N	{"redaction_timeout": 3600}	************	Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36	2025-08-25 17:57:54.32967+00	\N	\N	\N	\N	\N	\N
5180199d-4609-4945-ad6d-eb9e7f1c8f31	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	bd7363f8-6104-449e-873a-db0539f0fa17	\N	{"name": "p1", "category": "data_privacy", "severity": "medium", "is_active": true}	**********		2025-08-27 18:11:04.650915+00	\N	\N	admin	policy_creation	write	sensitive
c5611c26-1296-4a31-b7b4-4a30af001009	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	759c48a8-4e86-4430-8da0-73973abba594	\N	{"name": "p2", "category": "access_control", "severity": "low", "is_active": true}	**********		2025-08-27 20:07:59.80883+00	\N	\N	admin	policy_creation	write	sensitive
eff3a986-775a-4f88-a3aa-513a60a6152f	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	4855b086-ab50-4488-9094-58f0be3f2cf3	\N	{"name": "Test Medical Privacy Policy", "category": "medical_privacy", "severity": "high"}	**********		2025-08-27 21:38:06.078962+00	\N	\N	admin	policy_creation	write	sensitive
1426512f-2c79-4f10-a185-ccf675374438	00000000-0000-0000-0000-000000000000	CREATE	policy_group	d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	\N	{"name": "Test Medical Group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf", "severity": "medium", "created_at": "2025-08-27T22:01:09.725Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-27T22:01:09.725Z", "description": "Test policy group for medical privacy policies", "is_template": false}	**********		2025-08-27 22:01:09.729771+00	\N	\N	system	create	write	sensitive
085a5c4d-e992-48eb-9613-a87cb5d54b66	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	\N	{"addedPolicyIds": ["4855b086-ab50-4488-9094-58f0be3f2cf3"]}	**********		2025-08-27 22:01:28.858032+00	\N	\N	system	add_policy	write	sensitive
6c817782-92eb-493f-829f-07c8802d4375	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	9b3d812f-bde0-43e9-a211-5d48379f1320	\N	{"name": "p3", "category": "medical_privacy", "severity": "medium", "is_active": true}	**********		2025-08-27 22:01:37.884014+00	\N	\N	admin	policy_creation	write	sensitive
720b4a8a-0172-4162-8a11-4323d7b5f751	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d	\N	{"name": "p4", "category": "access_control", "severity": "low", "is_active": true}	**********		2025-08-27 22:02:54.832003+00	\N	\N	admin	policy_creation	write	sensitive
5952df59-3fc2-4612-be6a-2cd76780e883	00000000-0000-0000-0000-000000000000	CREATE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	{"name": "PI Bundle", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "11a01996-cb91-40ef-88c1-8e71328ca7f4", "severity": "medium", "created_at": "2025-08-27T22:04:32.761Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-27T22:04:32.761Z", "description": "PI Bun", "is_template": true}	**********		2025-08-27 22:04:32.766584+00	\N	\N	system	create	write	sensitive
3aff4aef-a9cb-4336-9198-f2a00a9fbf00	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	00accc7f-7fd6-42cd-acbd-e9016b48eaec	\N	{"name": "Test Database Schema Policy", "category": "Access Control", "severity": "medium"}	**********		2025-08-27 22:17:01.527401+00	\N	\N	admin	policy_creation	write	sensitive
a9315c67-b720-4489-9d65-9f300f8743d7	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	3c7b0cb1-0c84-4c64-8ebb-bedc7c262e53	\N	{"name": "Test DB Schema Policy 1756333061", "category": "Access Control", "severity": "medium"}	**********		2025-08-27 22:17:41.282415+00	\N	\N	admin	policy_creation	write	sensitive
9cbf9a2f-5d60-464f-80a5-9739679044f3	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	f4c1a348-7f94-4ed5-908c-4aee04e82f37	\N	{"name": "Test DB Schema Policy **********", "category": "Access Control", "severity": "medium"}	**********		2025-08-27 22:18:03.521453+00	\N	\N	admin	policy_creation	write	sensitive
ff5050b3-e43e-4fda-b7ab-9187bf579bde	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	5c36b6fd-c0ce-40e1-9681-75c2d500d588	\N	{"name": "Test Fix Policy **********", "category": "Data Privacy", "severity": "high"}	**********		2025-08-27 22:30:11.912441+00	\N	\N	admin	policy_creation	write	sensitive
3951d83b-db58-4cf8-a6fc-18fdefb1db83	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	62124064-1825-4bfa-99be-580b1a1c625d	\N	{"name": "p5", "category": "access_control", "severity": "medium", "is_active": true}	**********		2025-08-27 22:30:58.628141+00	\N	\N	admin	policy_creation	write	sensitive
75d724d0-e014-4e7e-aba8-e77ff377b087	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	\N	{"name": "p6", "category": "compliance", "severity": "medium", "is_active": true}	**********		2025-08-28 04:17:49.447202+00	\N	\N	admin	policy_creation	write	sensitive
e79664c7-15da-4094-a252-a5afedddaaad	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	6123f679-d34c-4b99-abea-46249bd69db1	\N	\N	**********		2025-08-28 20:35:22.678795+00	\N	\N	system	role_deleted	write	sensitive
1b8fa7d0-b5aa-4564-85ac-6f44ecdd6054	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	b82ce312-7cda-43a6-b3ef-a97b58e19e17	\N	\N	**********		2025-08-28 20:35:22.682002+00	\N	\N	system	deprecate	write	sensitive
a760edde-59f6-4f71-be4e-a1d516e6217c	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	e1414965-9a7d-4fdb-aa06-666be8dc37bf	\N	{"name": "e2e_test_1756413348147_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:35:48.219765+00	\N	\N	admin	policy_creation	write	sensitive
cb37fc59-6ea6-4423-af48-5e1c3cfc43e3	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	5c36b6fd-c0ce-40e1-9681-75c2d500d588	{"name": "Test Fix Policy **********", "version": 1, "blob_url": null, "category": "Data Privacy", "severity": "high", "blob_path": null, "is_active": null, "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "rego_code": null, "created_at": "2025-08-27T22:30:11.908Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-27T22:30:11.908Z", "updated_by": null, "description": "Testing after fixing JsonStructureGuide", "policy_type": "data_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Test Fix Policy **********", "version": 1, "blob_url": null, "category": "Data Privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "rego_code": null, "created_at": "2025-08-27T22:30:11.908Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-28T03:45:18.454Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing after fixing JsonStructureGuide", "policy_type": "data_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 03:45:18.465335+00	\N	\N	admin	policy_update	write	sensitive
53856f06-01f6-4ec9-a816-feba5a6191c7	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	5c36b6fd-c0ce-40e1-9681-75c2d500d588	{"name": "Test Fix Policy **********", "version": 1, "blob_url": null, "category": "Data Privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "rego_code": null, "created_at": "2025-08-27T22:30:11.908Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-28T03:45:18.454Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing after fixing JsonStructureGuide", "policy_type": "data_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Test Fix Policy **********", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "rego_code": null, "created_at": "2025-08-27T22:30:11.908Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-28T03:45:51.355Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing after fixing JsonStructureGuide", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin", "manager"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 03:45:51.362784+00	\N	\N	admin	policy_update	write	sensitive
92078949-82fa-47b6-aa00-96f9ece40e55	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	5c36b6fd-c0ce-40e1-9681-75c2d500d588	{"name": "Test Fix Policy **********", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "rego_code": null, "created_at": "2025-08-27T22:30:11.908Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-28T03:45:51.355Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing after fixing JsonStructureGuide", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin", "manager"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Test Fix Policy **********", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "high", "blob_path": null, "is_active": false, "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "rego_code": null, "created_at": "2025-08-27T22:30:11.908Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": false, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-28T04:04:37.642Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing after fixing JsonStructureGuide", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin", "manager"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 04:04:37.65283+00	\N	\N	admin	policy_update	write	sensitive
dded515e-2d70-4346-bf25-f5be9921f44c	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	5c36b6fd-c0ce-40e1-9681-75c2d500d588	{"name": "Test Fix Policy **********", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "high", "blob_path": null, "is_active": false, "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "rego_code": null, "created_at": "2025-08-27T22:30:11.908Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": false, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-28T04:04:37.642Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing after fixing JsonStructureGuide", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin", "manager"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Test Fix Policy **********", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "5c36b6fd-c0ce-40e1-9681-75c2d500d588", "rego_code": null, "created_at": "2025-08-27T22:30:11.908Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-28T04:04:53.021Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing after fixing JsonStructureGuide", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin", "manager"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 04:04:53.027872+00	\N	\N	admin	policy_update	write	sensitive
989d7ca6-f740-4de5-8a33-63c985c1eb55	00000000-0000-0000-0000-000000000000	RESTORE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 18:49:07.874216+00	\N	\N	system	restore	write	sensitive
25b27674-dda4-4dc4-8fef-2f513c5a384d	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "medium", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "severity": "medium", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-28T04:17:49.436Z", "updated_by": null, "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-28T04:18:06.626Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 04:18:06.634313+00	\N	\N	admin	policy_update	write	sensitive
d8676a88-5883-41b6-9b85-c6a118b293d3	00000000-0000-0000-0000-000000000000	CREATE	policy_group	bf6bde98-2093-4da8-b6d8-3929ef674aff	\N	{"name": "Test Policy Group Demo", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "bf6bde98-2093-4da8-b6d8-3929ef674aff", "severity": "medium", "created_at": "2025-08-28T04:25:26.763Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T04:25:26.763Z", "description": "Test group for demo flow verification", "is_template": false}	**********		2025-08-28 04:25:26.769067+00	\N	\N	system	create	write	sensitive
53ef02e7-1abd-4cd9-8920-6f48389a57e7	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2	\N	{"name": "Demo Medical Privacy Policy", "category": "Medical Privacy", "severity": "medium"}	**********		2025-08-28 04:27:16.870204+00	\N	\N	admin	policy_creation	write	sensitive
4091d8be-e933-49f2-b346-e6953a2ab9ff	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	bf6bde98-2093-4da8-b6d8-3929ef674aff	\N	{"addedPolicyIds": ["3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2"]}	**********		2025-08-28 04:28:19.91075+00	\N	\N	system	add_policy	write	sensitive
21b9d447-03b4-4070-9e0b-1c56c6d3ee77	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2	{"name": "Demo Medical Privacy Policy", "version": 1, "blob_url": null, "category": "Medical Privacy", "severity": "medium", "blob_path": null, "is_active": null, "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "rego_code": null, "created_at": "2025-08-28T04:27:16.865Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "medium", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-08-28T04:27:16.865Z", "updated_by": null, "description": "Test medical privacy policy for demo flow", "policy_type": "medical_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Demo Medical Privacy Policy", "version": 1, "blob_url": null, "category": "Medical Privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "rego_code": null, "created_at": "2025-08-28T04:27:16.865Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-08-28T04:29:13.577Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Test medical privacy policy for demo flow - UPDATED", "policy_type": "medical_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 04:29:13.590399+00	\N	\N	admin	policy_update	write	sensitive
078ea76c-1447-4468-9cc7-7fc627a6a7fb	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	bf6bde98-2093-4da8-b6d8-3929ef674aff	\N	{"addedPolicyIds": ["3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2"]}	**********		2025-08-28 04:33:04.248445+00	\N	\N	system	add_policy	write	sensitive
dcaad9fd-aa97-4b54-acb5-433246806d95	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-28T04:18:06.626Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-28T06:10:34.652Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 06:10:34.658972+00	\N	\N	admin	policy_update	write	sensitive
19d3b28d-4261-430c-ab9b-31babf77e121	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	7dcc8340-2f3a-4a2d-9e85-b821940a6560	\N	{"addedPolicyIds": ["8d723cde-418b-4c56-94de-f6ff3edb7703"]}	**********		2025-08-28 20:34:58.742654+00	\N	\N	system	add_policy	write	sensitive
5ede877f-e856-44fb-8ed1-7353c11ebe47	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	c281f81a-761e-4502-8d6a-9fc0e4e24872	\N	{"code": "e2e_test_1756413298660_HCO", "name": "e2e_test_1756413298660_healthcare_officer"}	**********		2025-08-28 20:34:58.748897+00	\N	\N	system	role_created	write	sensitive
13d6cf8d-6bd0-418b-9fc9-c32bf8cc78fa	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	c281f81a-761e-4502-8d6a-9fc0e4e24872	\N	\N	**********		2025-08-28 20:34:58.762773+00	\N	\N	system	role_deleted	write	sensitive
153f5e35-6fcd-4df3-bb83-18755da308bc	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	7dcc8340-2f3a-4a2d-9e85-b821940a6560	\N	\N	**********		2025-08-28 20:34:58.765995+00	\N	\N	system	deprecate	write	sensitive
6368a226-927d-4ca0-a788-abf279b88a6b	00000000-0000-0000-0000-000000000000	CREATE	policy_group	3884f39f-c810-46e8-ad63-9353c491574a	\N	{"name": "e2e_test_1756413348147_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "3884f39f-c810-46e8-ad63-9353c491574a", "severity": "medium", "created_at": "2025-08-28T20:35:48.211Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:35:48.211Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:35:48.148Z)", "is_template": false}	**********		2025-08-28 20:35:48.213254+00	\N	\N	system	create	write	sensitive
909cdf71-2ea9-49af-9977-e9f8936d7d18	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-28T06:10:34.652Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": true, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-28T06:10:46.049Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 06:10:46.055616+00	\N	\N	admin	policy_update	write	sensitive
4e97ba66-8892-4a58-bf51-5353dcb09d1f	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2	{"name": "Demo Medical Privacy Policy", "version": 1, "blob_url": null, "category": "Medical Privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "rego_code": null, "created_at": "2025-08-28T04:27:16.865Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-08-28T04:29:13.577Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Test medical privacy policy for demo flow - UPDATED", "policy_type": "medical_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Demo Medical Privacy Policy", "version": 1, "blob_url": null, "category": "Medical Privacy", "severity": "high", "blob_path": null, "is_active": false, "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "rego_code": null, "created_at": "2025-08-28T04:27:16.865Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-08-28T06:23:03.140Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Test medical privacy policy for demo flow - UPDATED", "policy_type": "medical_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 06:23:03.144815+00	\N	\N	admin	policy_update	write	sensitive
807f99b7-412e-44c6-a5d8-2854f9d14cbd	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2	{"name": "Demo Medical Privacy Policy", "version": 1, "blob_url": null, "category": "Medical Privacy", "severity": "high", "blob_path": null, "is_active": false, "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "rego_code": null, "created_at": "2025-08-28T04:27:16.865Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-08-28T06:23:03.140Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Test medical privacy policy for demo flow - UPDATED", "policy_type": "medical_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Demo Medical Privacy Policy", "version": 1, "blob_url": null, "category": "Medical Privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "rego_code": null, "created_at": "2025-08-28T04:27:16.865Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-08-28T06:29:34.940Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Test medical privacy policy for demo flow - UPDATED", "policy_type": "medical_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 06:29:34.947579+00	\N	\N	admin	policy_update	write	sensitive
da36e481-8350-4171-8a26-82b7b2ef2973	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	9340a076-0ac3-4c43-8845-6143b2bc032a	\N	{"name": "p7", "category": "data_privacy", "severity": "low", "is_active": true}	**********		2025-08-28 19:37:57.474747+00	\N	\N	admin	policy_creation	write	sensitive
58f99252-30ac-4f0d-993e-3581e9f9b0ea	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	c3bd8586-ce3f-4d21-bdb0-f1afb644fc4a	\N	{"name": "p8", "category": "test_schema_1756409008", "severity": "low", "is_active": true}	**********		2025-08-28 19:59:32.200848+00	\N	\N	admin	policy_creation	write	sensitive
3ff0afda-b759-44af-9717-d6549e7a31fd	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	2ada8019-e800-4aed-a43d-69f7c461b7f0	\N	{"name": "p9", "category": "test_schema_1756409008", "severity": "low", "is_active": true}	**********		2025-08-28 20:06:24.594666+00	\N	\N	admin	policy_creation	write	sensitive
092f4810-710e-4474-9499-2ae716a5ce0f	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	177115f8-1121-4fa0-8e81-f6db326fabf6	\N	{"name": "p10", "category": "test_schema_1756409008", "severity": "low", "is_active": false}	**********		2025-08-28 20:15:26.905399+00	\N	\N	admin	policy_creation	write	sensitive
d0e6c12a-c1d6-4599-9f87-813ddd6380f8	00000000-0000-0000-0000-000000000000	CREATE	policy_group	b82ce312-7cda-43a6-b3ef-a97b58e19e17	\N	{"name": "e2e_test_1756413322576_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "b82ce312-7cda-43a6-b3ef-a97b58e19e17", "severity": "medium", "created_at": "2025-08-28T20:35:22.641Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:35:22.641Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:35:22.577Z)", "is_template": false}	**********		2025-08-28 20:35:22.643172+00	\N	\N	system	create	write	sensitive
67b490c0-cd4c-44d3-9c17-c2cb6891ca3b	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	19fe1b8d-89f4-4019-8f59-2d08382d356d	\N	{"name": "e2e_test_1756413322576_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:35:22.653422+00	\N	\N	admin	policy_creation	write	sensitive
1d3b1d99-0ea9-4e8e-ba75-cc168e1e78f2	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	b82ce312-7cda-43a6-b3ef-a97b58e19e17	\N	{"addedPolicyIds": ["19fe1b8d-89f4-4019-8f59-2d08382d356d"]}	**********		2025-08-28 20:35:22.656634+00	\N	\N	system	add_policy	write	sensitive
2c1da6e5-3ded-48cf-9a16-a732fcc8ff4f	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	6123f679-d34c-4b99-abea-46249bd69db1	\N	{"code": "e2e_test_1756413322576_HCO", "name": "e2e_test_1756413322576_healthcare_officer"}	**********		2025-08-28 20:35:22.662204+00	\N	\N	system	role_created	write	sensitive
ea6a0511-47d4-42cc-80fc-1f2f0d542eba	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	177115f8-1121-4fa0-8e81-f6db326fabf6	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "", "type": "test_schema_1756409008", "active": false, "enabled": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-28T20:15:26.894Z", "updated_by": null, "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-28T20:15:40.781Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-28 20:15:40.791133+00	\N	\N	admin	policy_update	write	sensitive
6d8c7cf5-b7b2-49f9-a764-faac2663d8bf	00000000-0000-0000-0000-000000000000	CREATE	policy_group	309aef0b-8e87-4d69-b6e7-21b7c89417cb	\N	{"name": "e2e_test_1756413012146_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "309aef0b-8e87-4d69-b6e7-21b7c89417cb", "severity": "medium", "created_at": "2025-08-28T20:30:12.211Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:30:12.211Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:30:12.146Z)", "is_template": false}	**********		2025-08-28 20:30:12.213483+00	\N	\N	system	create	write	sensitive
0cb86f69-321d-4dde-9bd9-daaef71f473c	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	f2d309ee-d92c-4f88-aadc-d714e08d286c	\N	{"name": "e2e_test_1756413012146_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:30:12.222024+00	\N	\N	admin	policy_creation	write	sensitive
416dc813-89fb-4be1-93e6-7d3de3812e9e	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	309aef0b-8e87-4d69-b6e7-21b7c89417cb	\N	\N	**********		2025-08-28 20:30:12.236159+00	\N	\N	system	deprecate	write	sensitive
48b57aa5-1898-4fd5-9fb3-e02497a1b637	00000000-0000-0000-0000-000000000000	CREATE	policy_group	1bd513d1-1cf3-473d-828a-d0420fe00265	\N	{"name": "e2e_test_1756413202760_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "1bd513d1-1cf3-473d-828a-d0420fe00265", "severity": "medium", "created_at": "2025-08-28T20:33:22.823Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:33:22.823Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:33:22.760Z)", "is_template": false}	**********		2025-08-28 20:33:22.827385+00	\N	\N	system	create	write	sensitive
8c59b0af-d497-4169-8669-a7511cbf34b0	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	ea7ec861-35f9-4897-9c04-b5b05823ca32	\N	{"name": "e2e_test_1756413202760_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:33:22.834432+00	\N	\N	admin	policy_creation	write	sensitive
c8dba458-5337-463a-9526-9cdd80838979	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	1bd513d1-1cf3-473d-828a-d0420fe00265	\N	{"addedPolicyIds": ["ea7ec861-35f9-4897-9c04-b5b05823ca32"]}	**********		2025-08-28 20:33:22.837291+00	\N	\N	system	add_policy	write	sensitive
865e1397-7537-4bae-97e6-1d6d59995c9c	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	1bd513d1-1cf3-473d-828a-d0420fe00265	\N	\N	**********		2025-08-28 20:33:22.843475+00	\N	\N	system	deprecate	write	sensitive
cc687639-3939-4b59-a850-dd42a38c717b	00000000-0000-0000-0000-000000000000	CREATE	policy_group	0f3d98da-c217-4ed7-9adf-432da4f42cba	\N	{"name": "e2e_test_1756413223928_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "0f3d98da-c217-4ed7-9adf-432da4f42cba", "severity": "medium", "created_at": "2025-08-28T20:33:44.002Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:33:44.002Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:33:43.929Z)", "is_template": false}	**********		2025-08-28 20:33:44.004463+00	\N	\N	system	create	write	sensitive
0ee47967-24e3-4918-8ffa-09cbd1a99883	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	d58edf2e-641b-496b-b92e-d0c297f9684a	\N	{"name": "e2e_test_1756413223928_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:33:44.011969+00	\N	\N	admin	policy_creation	write	sensitive
c0e23eff-129c-471f-b8a1-c23a6f2fd9c2	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	0f3d98da-c217-4ed7-9adf-432da4f42cba	\N	{"addedPolicyIds": ["d58edf2e-641b-496b-b92e-d0c297f9684a"]}	**********		2025-08-28 20:33:44.014993+00	\N	\N	system	add_policy	write	sensitive
dbcc801b-ecc4-431d-8cd0-647293a9688e	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	0f3d98da-c217-4ed7-9adf-432da4f42cba	\N	\N	**********		2025-08-28 20:33:44.027057+00	\N	\N	system	deprecate	write	sensitive
ae77929f-716f-4a7d-8e85-b79071216be1	00000000-0000-0000-0000-000000000000	CREATE	policy_group	8b1c2a2c-8741-4d76-b92f-a5b806ed431a	\N	{"name": "e2e_test_1756413259601_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "8b1c2a2c-8741-4d76-b92f-a5b806ed431a", "severity": "medium", "created_at": "2025-08-28T20:34:19.665Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:34:19.665Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:34:19.601Z)", "is_template": false}	**********		2025-08-28 20:34:19.66804+00	\N	\N	system	create	write	sensitive
30a81fd8-2bf9-477f-8b18-71b3f99bb8ea	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	afc5a3cf-82e8-450b-a846-2c4529ac170b	\N	{"name": "e2e_test_1756413259601_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:34:19.680678+00	\N	\N	admin	policy_creation	write	sensitive
f7112022-c8b1-4857-a9c7-5fe30329320a	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	8b1c2a2c-8741-4d76-b92f-a5b806ed431a	\N	{"addedPolicyIds": ["afc5a3cf-82e8-450b-a846-2c4529ac170b"]}	**********		2025-08-28 20:34:19.683605+00	\N	\N	system	add_policy	write	sensitive
bef1fd79-c02f-4e5f-b734-1bbc27eff6d6	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	0dcc858a-5610-4abc-952b-67a3ede00efc	\N	{"code": "e2e_test_1756413259601_HCO", "name": "e2e_test_1756413259601_healthcare_officer"}	**********		2025-08-28 20:34:19.690153+00	\N	\N	system	role_created	write	sensitive
61a64171-933e-4e54-a2df-a462498239e7	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	0dcc858a-5610-4abc-952b-67a3ede00efc	\N	\N	**********		2025-08-28 20:34:19.703413+00	\N	\N	system	role_deleted	write	sensitive
e873cf3b-6119-474c-b6c4-9618c80b3372	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	8b1c2a2c-8741-4d76-b92f-a5b806ed431a	\N	\N	**********		2025-08-28 20:34:19.70752+00	\N	\N	system	deprecate	write	sensitive
c43836f8-ab68-45e0-bae1-9178c4e0627f	00000000-0000-0000-0000-000000000000	CREATE	policy_group	7dcc8340-2f3a-4a2d-9e85-b821940a6560	\N	{"name": "e2e_test_1756413298660_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "7dcc8340-2f3a-4a2d-9e85-b821940a6560", "severity": "medium", "created_at": "2025-08-28T20:34:58.729Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:34:58.729Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:34:58.661Z)", "is_template": false}	**********		2025-08-28 20:34:58.732158+00	\N	\N	system	create	write	sensitive
8e9f1a9d-00f8-4026-a341-4537552d4c0c	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	8d723cde-418b-4c56-94de-f6ff3edb7703	\N	{"name": "e2e_test_1756413298660_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:34:58.73963+00	\N	\N	admin	policy_creation	write	sensitive
4cccd91e-5e4d-4b9b-aa06-94c83a7eddb9	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	3884f39f-c810-46e8-ad63-9353c491574a	\N	{"addedPolicyIds": ["e1414965-9a7d-4fdb-aa06-666be8dc37bf"]}	**********		2025-08-28 20:35:48.223468+00	\N	\N	system	add_policy	write	sensitive
50adf91c-3052-482e-8a1b-2973683f6c19	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	28db8e6c-4f60-4d7d-b706-ad3e181da2bb	\N	{"code": "e2e_test_1756413348147_HCO", "name": "e2e_test_1756413348147_healthcare_officer"}	**********		2025-08-28 20:35:48.23635+00	\N	\N	system	role_created	write	sensitive
a64d442c-2f2f-4f9c-b476-b98a1e8e32ab	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	28db8e6c-4f60-4d7d-b706-ad3e181da2bb	\N	\N	**********		2025-08-28 20:35:49.27525+00	\N	\N	system	role_deleted	write	sensitive
64219805-7b97-4e2d-9ab0-7e97a41deebd	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	3884f39f-c810-46e8-ad63-9353c491574a	\N	\N	**********		2025-08-28 20:35:49.286235+00	\N	\N	system	deprecate	write	sensitive
3f32234f-6228-4177-8bff-26ff30a688de	00000000-0000-0000-0000-000000000000	CREATE	policy_group	b504593d-3a44-455d-8fe1-59656ea1753a	\N	{"name": "e2e_test_1756413382997_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "b504593d-3a44-455d-8fe1-59656ea1753a", "severity": "medium", "created_at": "2025-08-28T20:36:23.057Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:36:23.057Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:36:22.997Z)", "is_template": false}	**********		2025-08-28 20:36:23.059467+00	\N	\N	system	create	write	sensitive
e7a9455a-2cd2-4f38-a759-785bb143a063	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	3ffa6959-db5b-492a-a963-944763a7c7b1	\N	{"name": "e2e_test_1756413382997_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:36:23.067045+00	\N	\N	admin	policy_creation	write	sensitive
6af18f71-6e02-46c9-9c44-3b5eaaf3d73d	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	b504593d-3a44-455d-8fe1-59656ea1753a	\N	{"addedPolicyIds": ["3ffa6959-db5b-492a-a963-944763a7c7b1"]}	**********		2025-08-28 20:36:23.069724+00	\N	\N	system	add_policy	write	sensitive
c60dd60e-3b4c-4573-b60e-8d90629902a0	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	4519f7b7-580b-401c-9477-d8910e4c4073	\N	{"code": "e2e_test_1756413382997_HCO", "name": "e2e_test_1756413382997_healthcare_officer"}	**********		2025-08-28 20:36:23.075478+00	\N	\N	system	role_created	write	sensitive
a4243976-dd23-400a-be63-5a558cba6be5	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	4519f7b7-580b-401c-9477-d8910e4c4073	\N	\N	**********		2025-08-28 20:36:24.117057+00	\N	\N	system	role_deleted	write	sensitive
29b98db6-7be8-4721-98e0-28d6581bb507	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	b504593d-3a44-455d-8fe1-59656ea1753a	\N	\N	**********		2025-08-28 20:36:24.125238+00	\N	\N	system	deprecate	write	sensitive
ae98e751-d0a7-44ec-bf99-b2aaabf1c06c	00000000-0000-0000-0000-000000000000	CREATE	policy_group	3ec7f725-252e-43a7-bcd7-82e8d7b82d9f	\N	{"name": "e2e_test_1756413399924_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "3ec7f725-252e-43a7-bcd7-82e8d7b82d9f", "severity": "medium", "created_at": "2025-08-28T20:36:39.970Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:36:39.970Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:36:39.928Z)", "is_template": false}	**********		2025-08-28 20:36:39.972204+00	\N	\N	system	create	write	sensitive
6d3284ed-8579-4ea4-980e-e479241f21e3	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	1604904e-f5d0-4842-b59c-a11390052789	\N	{"name": "e2e_test_1756413399924_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:36:39.979728+00	\N	\N	admin	policy_creation	write	sensitive
8dbe88f7-2876-46a5-b168-4668b8ab2586	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	3ec7f725-252e-43a7-bcd7-82e8d7b82d9f	\N	{"addedPolicyIds": ["1604904e-f5d0-4842-b59c-a11390052789"]}	**********		2025-08-28 20:36:39.983452+00	\N	\N	system	add_policy	write	sensitive
c816bd1a-31c8-4718-af6f-39f20deac818	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	6e5b3ebc-7db5-4b8a-9ae2-411b5a93ac62	\N	{"code": "e2e_test_1756413399924_HCO", "name": "e2e_test_1756413399924_healthcare_officer"}	**********		2025-08-28 20:36:39.989539+00	\N	\N	system	role_created	write	sensitive
df2b08ca-3fd2-4153-bbc4-138e14ae8795	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	4eccd6ca-bc28-428d-9e0d-0cdc4257fa33	\N	{"name": "e2e_test_1756413399924_group_test_policy", "category": "healthcare_compliance", "severity": "medium"}	**********		2025-08-28 20:36:41.035014+00	\N	\N	admin	policy_creation	write	sensitive
b7a1a46c-1e2a-4348-b6ab-86ed39733c73	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	3ec7f725-252e-43a7-bcd7-82e8d7b82d9f	\N	{"addedPolicyIds": ["4eccd6ca-bc28-428d-9e0d-0cdc4257fa33"]}	**********		2025-08-28 20:36:41.041087+00	\N	\N	system	add_policy	write	sensitive
3d7b28df-1d0c-47fe-a05f-945a6210b773	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	3ec7f725-252e-43a7-bcd7-82e8d7b82d9f	\N	\N	**********		2025-08-28 20:36:41.047015+00	\N	\N	system	deprecate	write	sensitive
7a6f1509-2865-4761-a89c-14076033c375	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	6e5b3ebc-7db5-4b8a-9ae2-411b5a93ac62	\N	\N	**********		2025-08-28 20:36:41.055298+00	\N	\N	system	role_deleted	write	sensitive
6a54a316-65d0-4664-b695-dba6f736ec8b	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	3ec7f725-252e-43a7-bcd7-82e8d7b82d9f	\N	\N	**********		2025-08-28 20:36:41.062906+00	\N	\N	system	deprecate	write	sensitive
ec5918a0-d363-4f3a-9417-c13b7ce191b3	00000000-0000-0000-0000-000000000000	CREATE	policy_group	5c51e454-aa47-4bfe-b81a-815be0cce9cb	\N	{"name": "e2e_test_1756413483366_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "5c51e454-aa47-4bfe-b81a-815be0cce9cb", "severity": "medium", "created_at": "2025-08-28T20:38:03.457Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:38:03.457Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:38:03.366Z)", "is_template": false}	**********		2025-08-28 20:38:03.459788+00	\N	\N	system	create	write	sensitive
6e89ea9a-4c68-4ba0-b551-9e25103956f8	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	80073610-ee06-49db-bd2a-531c5c39a76c	\N	{"name": "e2e_test_1756413483366_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:38:03.466995+00	\N	\N	admin	policy_creation	write	sensitive
8421efb7-6160-485b-a5da-78fdea53e6d9	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	5c51e454-aa47-4bfe-b81a-815be0cce9cb	\N	{"addedPolicyIds": ["80073610-ee06-49db-bd2a-531c5c39a76c"]}	**********		2025-08-28 20:38:03.470164+00	\N	\N	system	add_policy	write	sensitive
1dc5b7a3-cdc2-4c6a-9ef7-754d60e31e5c	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	8f482811-77fa-4661-b79e-597049b7aaef	\N	{"code": "e2e_test_1756413483366_HCO", "name": "e2e_test_1756413483366_healthcare_officer"}	**********		2025-08-28 20:38:03.479515+00	\N	\N	system	role_created	write	sensitive
38e3007d-2bd4-429c-8ddc-3866293463ea	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	0c1e7f1c-18a6-4df9-973d-55fe90202b0f	\N	{"name": "e2e_test_1756413483366_group_test_policy", "category": "healthcare_compliance", "severity": "medium"}	**********		2025-08-28 20:38:04.517469+00	\N	\N	admin	policy_creation	write	sensitive
5362ee31-7dc8-4ebb-b337-b3534bf51be0	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	5c51e454-aa47-4bfe-b81a-815be0cce9cb	\N	{"addedPolicyIds": ["0c1e7f1c-18a6-4df9-973d-55fe90202b0f"]}	**********		2025-08-28 20:38:04.524184+00	\N	\N	system	add_policy	write	sensitive
6903c426-8853-4028-9caf-00b24bea38c6	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	5c51e454-aa47-4bfe-b81a-815be0cce9cb	\N	\N	**********		2025-08-28 20:38:04.530244+00	\N	\N	system	deprecate	write	sensitive
c81c0a5c-3241-41d5-8165-a5cb426c5bfc	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	8f482811-77fa-4661-b79e-597049b7aaef	\N	\N	**********		2025-08-28 20:38:05.564631+00	\N	\N	system	role_deleted	write	sensitive
834eaf47-46b2-4d2a-9ed6-990db7bd4b13	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	5c51e454-aa47-4bfe-b81a-815be0cce9cb	\N	\N	**********		2025-08-28 20:38:05.575208+00	\N	\N	system	deprecate	write	sensitive
bae17043-9dc9-4451-913e-9aec89c16e16	00000000-0000-0000-0000-000000000000	CREATE	policy_group	05c16aef-9eb0-4452-9b46-57aeb4f6e3e5	\N	{"name": "e2e_test_1756413525743_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "05c16aef-9eb0-4452-9b46-57aeb4f6e3e5", "severity": "medium", "created_at": "2025-08-28T20:38:45.802Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:38:45.802Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:38:45.744Z)", "is_template": false}	**********		2025-08-28 20:38:45.805103+00	\N	\N	system	create	write	sensitive
fc327169-cc66-4913-ae23-8f575b22384a	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	3c35d5ca-164b-4b75-bf0b-596fa2ee2627	\N	{"name": "e2e_test_1756413525743_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:38:45.813774+00	\N	\N	admin	policy_creation	write	sensitive
cb9c67dd-dac6-4edf-99e0-242060490d5e	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	05c16aef-9eb0-4452-9b46-57aeb4f6e3e5	\N	{"addedPolicyIds": ["3c35d5ca-164b-4b75-bf0b-596fa2ee2627"]}	**********		2025-08-28 20:38:45.816887+00	\N	\N	system	add_policy	write	sensitive
c141d6c7-74bc-4fe2-b92a-a92cec27124a	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	30086819-845b-42fc-8f09-9a042841eab5	\N	{"code": "e2e_test_1756413525743_HCO", "name": "e2e_test_1756413525743_healthcare_officer"}	**********		2025-08-28 20:38:45.82332+00	\N	\N	system	role_created	write	sensitive
abc67b31-cdd5-4fe9-9990-8de63718fa68	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	7ee9ae41-de86-4244-90f8-25c2da0623b2	\N	{"name": "e2e_test_1756413525743_group_test_policy", "category": "healthcare_compliance", "severity": "medium"}	**********		2025-08-28 20:38:46.856834+00	\N	\N	admin	policy_creation	write	sensitive
72992cd5-4ad4-497a-8832-0aac23ceb070	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	05c16aef-9eb0-4452-9b46-57aeb4f6e3e5	\N	{"addedPolicyIds": ["7ee9ae41-de86-4244-90f8-25c2da0623b2"]}	**********		2025-08-28 20:38:46.863397+00	\N	\N	system	add_policy	write	sensitive
b4a5f3a4-e257-4633-a00c-4df6875fb5eb	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	05c16aef-9eb0-4452-9b46-57aeb4f6e3e5	\N	\N	**********		2025-08-28 20:38:46.869205+00	\N	\N	system	deprecate	write	sensitive
c99241b8-302d-4ef2-8a79-f5a8d95a1d3e	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	30086819-845b-42fc-8f09-9a042841eab5	\N	\N	**********		2025-08-28 20:38:47.902969+00	\N	\N	system	role_deleted	write	sensitive
109314e3-ad71-4c4d-bf7c-62a2cfd04002	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	05c16aef-9eb0-4452-9b46-57aeb4f6e3e5	\N	\N	**********		2025-08-28 20:38:47.908609+00	\N	\N	system	deprecate	write	sensitive
3dca5209-9097-4b12-b618-3156fffba835	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	30086819-845b-42fc-8f09-9a042841eab5	\N	\N	**********		2025-08-28 20:38:47.920641+00	\N	\N	system	role_deleted	write	sensitive
89b6bbe9-ca2b-445c-9f31-0bf44f288a7a	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	05c16aef-9eb0-4452-9b46-57aeb4f6e3e5	\N	\N	**********		2025-08-28 20:38:47.925414+00	\N	\N	system	deprecate	write	sensitive
d855acf1-90e9-408e-b393-7c84bce8b47b	00000000-0000-0000-0000-000000000000	CREATE	policy_group	33b0b43b-f336-49e0-ad22-a8ab1ca8fc70	\N	{"name": "e2e_test_1756413938766_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "33b0b43b-f336-49e0-ad22-a8ab1ca8fc70", "severity": "medium", "created_at": "2025-08-28T20:45:38.819Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:45:38.819Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:45:38.767Z)", "is_template": false}	**********		2025-08-28 20:45:38.822159+00	\N	\N	system	create	write	sensitive
e65f1ffc-b8e4-4c50-9471-262b7ac1144a	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	6f2c6077-2364-478f-96e6-c2196d623b4e	\N	{"name": "e2e_test_1756413938766_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:45:38.829147+00	\N	\N	admin	policy_creation	write	sensitive
725c562c-470f-4944-83d6-759f00aa92e5	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	33b0b43b-f336-49e0-ad22-a8ab1ca8fc70	\N	{"addedPolicyIds": ["6f2c6077-2364-478f-96e6-c2196d623b4e"]}	**********		2025-08-28 20:45:38.831579+00	\N	\N	system	add_policy	write	sensitive
e059d13d-2983-457e-a25d-8966f4ea1a9a	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	63db9c9d-b64d-452c-9d54-194005f0c329	\N	{"code": "e2e_test_1756413938766_HCO", "name": "e2e_test_1756413938766_healthcare_officer"}	**********		2025-08-28 20:45:38.836556+00	\N	\N	system	role_created	write	sensitive
1822f11c-09fb-49fa-b6fd-adee822bc826	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	63db9c9d-b64d-452c-9d54-194005f0c329	\N	\N	**********		2025-08-28 20:45:38.843587+00	\N	\N	system	role_deleted	write	sensitive
7519e7fb-5341-499d-8b96-d8ae857b40d8	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	33b0b43b-f336-49e0-ad22-a8ab1ca8fc70	\N	\N	**********		2025-08-28 20:45:38.846727+00	\N	\N	system	deprecate	write	sensitive
90a80c5c-86a5-4535-8117-345021f53249	00000000-0000-0000-0000-000000000000	CREATE	policy_group	dda421b1-14c6-4ede-b3fe-84b4fc2e4805	\N	{"name": "e2e_test_1756414222865_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "dda421b1-14c6-4ede-b3fe-84b4fc2e4805", "severity": "medium", "created_at": "2025-08-28T20:50:22.918Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:50:22.918Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:50:22.866Z)", "is_template": false}	**********		2025-08-28 20:50:22.919887+00	\N	\N	system	create	write	sensitive
1e799b0f-ec28-4d67-b140-ff49fca36691	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	96c01e28-12b6-4aac-a83f-15bb4527f26a	\N	{"name": "e2e_test_1756414222865_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:50:22.931378+00	\N	\N	admin	policy_creation	write	sensitive
2ea2f0cf-5d37-4723-9e58-41438a137775	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	dda421b1-14c6-4ede-b3fe-84b4fc2e4805	\N	{"addedPolicyIds": ["96c01e28-12b6-4aac-a83f-15bb4527f26a"]}	**********		2025-08-28 20:50:22.936093+00	\N	\N	system	add_policy	write	sensitive
01db11ab-2848-4197-bc5f-b8aa72b56f37	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	b19d20da-80cc-4626-ac74-4d57160829c8	\N	{"code": "e2e_test_1756414222865_HCO", "name": "e2e_test_1756414222865_healthcare_officer"}	**********		2025-08-28 20:50:22.941625+00	\N	\N	system	role_created	write	sensitive
6ec6ccd9-aa74-48e0-90a2-f3794648355a	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	b19d20da-80cc-4626-ac74-4d57160829c8	\N	\N	**********		2025-08-28 20:50:22.948338+00	\N	\N	system	role_deleted	write	sensitive
0a44764c-d311-4422-8eab-460f3ebf1fbc	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	dda421b1-14c6-4ede-b3fe-84b4fc2e4805	\N	\N	**********		2025-08-28 20:50:22.951528+00	\N	\N	system	deprecate	write	sensitive
e9e9e885-be80-42be-ba4b-39bba3e142dc	00000000-0000-0000-0000-000000000000	CREATE	policy_group	22106b70-899a-4851-8f03-d858b0589f01	\N	{"name": "e2e_test_1756414247713_healthcare_group", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "22106b70-899a-4851-8f03-d858b0589f01", "severity": "medium", "created_at": "2025-08-28T20:50:47.771Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-28T20:50:47.771Z", "description": "E2E Test Policy Group - Healthcare Policies (2025-08-28T20:50:47.713Z)", "is_template": false}	**********		2025-08-28 20:50:47.775648+00	\N	\N	system	create	write	sensitive
6ad9c89c-bf2c-493c-b6df-4c79e759336d	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	3e428b15-e58c-4ef9-ab58-bfe6303ecf4f	\N	{"name": "e2e_test_1756414247713_patient_data_protection", "category": "healthcare_compliance", "severity": "critical"}	**********		2025-08-28 20:50:47.79002+00	\N	\N	admin	policy_creation	write	sensitive
5d65b6ed-5bb5-4ae9-b78d-c6e6f80931ff	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	22106b70-899a-4851-8f03-d858b0589f01	\N	{"addedPolicyIds": ["3e428b15-e58c-4ef9-ab58-bfe6303ecf4f"]}	**********		2025-08-28 20:50:47.792813+00	\N	\N	system	add_policy	write	sensitive
29d24ad5-603c-4f88-a4c9-cd53b2cad3f3	00000000-0000-0000-0000-000000000000	ROLE_CREATED	role	942e55df-944d-413a-9270-b00a25156c7d	\N	{"code": "e2e_test_1756414247713_HCO", "name": "e2e_test_1756414247713_healthcare_officer"}	**********		2025-08-28 20:50:47.798721+00	\N	\N	system	role_created	write	sensitive
ab30fa0b-0bca-4df9-9981-6c525052759d	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	2a052eef-c31d-44e3-92f8-5a9aec35d792	\N	{"name": "e2e_test_1756414247713_group_test_policy", "category": "healthcare_compliance", "severity": "medium"}	**********		2025-08-28 20:50:48.833264+00	\N	\N	admin	policy_creation	write	sensitive
dc46d193-3d5e-458e-a6eb-25d206239389	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	22106b70-899a-4851-8f03-d858b0589f01	\N	{"addedPolicyIds": ["2a052eef-c31d-44e3-92f8-5a9aec35d792"]}	**********		2025-08-28 20:50:48.840223+00	\N	\N	system	add_policy	write	sensitive
f5dabeed-e606-4b7b-bc2c-3e716c0cd5e7	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	22106b70-899a-4851-8f03-d858b0589f01	\N	\N	**********		2025-08-28 20:50:48.845856+00	\N	\N	system	deprecate	write	sensitive
eb5b6f5d-2493-4f2f-b31a-ca5c14a7dc00	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	942e55df-944d-413a-9270-b00a25156c7d	\N	\N	**********		2025-08-28 20:50:49.874237+00	\N	\N	system	role_deleted	write	sensitive
ad908a64-695a-4da4-805b-8c2ebff13b74	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	22106b70-899a-4851-8f03-d858b0589f01	\N	\N	**********		2025-08-28 20:50:49.882448+00	\N	\N	system	deprecate	write	sensitive
80a99b4b-613a-451d-b7f2-bc7a10036af2	00000000-0000-0000-0000-000000000000	ROLE_DELETED	role	942e55df-944d-413a-9270-b00a25156c7d	\N	\N	**********		2025-08-28 20:50:49.898974+00	\N	\N	system	role_deleted	write	sensitive
8c0d38d6-974b-409b-bd1e-25ad11a0eafe	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	22106b70-899a-4851-8f03-d858b0589f01	\N	\N	**********		2025-08-28 20:50:49.903264+00	\N	\N	system	deprecate	write	sensitive
b42299e2-185b-49b2-a4db-12cc96989dd2	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 18:55:10.779036+00	\N	\N	system	deprecate	write	sensitive
a1e7cd3b-e694-4ea7-bf71-e74366d775b8	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	40a63e03-d616-4877-9f4b-cbf5fcad8cdc	\N	{"name": "p11", "category": "test_schema_1756409008", "severity": "low", "is_active": true}	**********		2025-08-29 08:30:16.841696+00	\N	\N	admin	policy_creation	write	sensitive
4ca766bf-6a59-4d48-b971-2f0badc4e144	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	86d75236-d62b-4f41-8eb9-70047dc986bc	\N	{"name": "p12", "category": "test_schema_1756409008", "severity": "low", "is_active": true}	**********		2025-08-29 08:34:22.84542+00	\N	\N	admin	policy_creation	write	sensitive
a8e44a01-23ed-446d-a0b1-bf90c208d3cb	00000000-0000-0000-0000-000000000000	MIGRATE	policy_templates	\N	\N	\N	\N	\N	2025-08-29 12:01:08.784361+00	\N	\N	\N	template_migration	\N	\N
1de5b18a-801e-46f9-982c-101393aa7264	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	ff38ed17-4f11-4f18-a366-64984925238e	\N	{"name": "p13", "category": "test_schema_1756409008", "severity": "low", "is_active": true}	**********		2025-08-29 12:38:34.098341+00	\N	\N	admin	policy_creation	write	sensitive
b5698d0f-5e38-441c-a683-7bbc4523817f	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	\N	{"name": "p14", "category": "medical_privacy", "severity": "critical", "is_active": true}	**********		2025-08-29 12:53:27.921261+00	\N	\N	admin	policy_creation	write	sensitive
dc9cc50c-c500-480a-9ee2-4fbd9e651492	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	6ecebaf7-5767-4790-93d6-f6b7ac634352	\N	{"name": "p15", "category": "test_schema_1756409008", "severity": "low", "is_active": true}	**********		2025-08-29 15:43:49.163298+00	\N	\N	admin	policy_creation	write	sensitive
aaa6fa75-3982-4fd6-aec1-f28fa65ceac1	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	ff38ed17-4f11-4f18-a366-64984925238e	{"name": "p13", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "ff38ed17-4f11-4f18-a366-64984925238e", "rego_code": null, "created_at": "2025-08-29T12:38:34.090Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"name": "nn_test", "type": "test_schema_1756409008", "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T12:38:34.090Z", "updated_by": null, "description": "p13", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p13", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "ff38ed17-4f11-4f18-a366-64984925238e", "rego_code": null, "created_at": "2025-08-29T12:38:34.090Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"name": "nn_test", "type": "test_schema_1756409008", "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T15:58:57.058Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p13", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 15:58:57.076652+00	\N	\N	admin	policy_update	write	sensitive
31379cfe-cfb2-4afb-a2d9-ed8d8be7583b	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	86d75236-d62b-4f41-8eb9-70047dc986bc	{"name": "p12", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "86d75236-d62b-4f41-8eb9-70047dc986bc", "rego_code": null, "created_at": "2025-08-29T08:34:22.830Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 12, "name": "", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T08:34:22.830Z", "updated_by": null, "description": "p12", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p12", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "86d75236-d62b-4f41-8eb9-70047dc986bc", "rego_code": null, "created_at": "2025-08-29T08:34:22.830Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"name": "test", "type": "test_schema_1756409008", "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T16:21:13.576Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p12", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 16:21:13.58502+00	\N	\N	admin	policy_update	write	sensitive
c033974d-5fec-4e0b-bb92-aafc6f27640c	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	40a63e03-d616-4877-9f4b-cbf5fcad8cdc	{"name": "p11", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "40a63e03-d616-4877-9f4b-cbf5fcad8cdc", "rego_code": null, "created_at": "2025-08-29T08:30:16.830Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 11, "name": "", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T08:30:16.830Z", "updated_by": null, "description": "p11", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p11", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "40a63e03-d616-4877-9f4b-cbf5fcad8cdc", "rego_code": null, "created_at": "2025-08-29T08:30:16.830Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 11, "name": "test", "type": "test_schema_1756409008", "active": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T16:27:05.278Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p11", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 16:27:05.288176+00	\N	\N	admin	policy_update	write	sensitive
902f0dd6-d26f-45dd-808e-c7e52385f2f4	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 19:01:42.712372+00	\N	\N	system	deprecate	write	sensitive
cb7d62b5-6de8-426e-a1ba-54d6d8d344f7	00000000-0000-0000-0000-000000000000	RESTORE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 19:16:38.207886+00	\N	\N	system	restore	write	sensitive
d6a1743e-c63d-4e2d-969d-9997f288473a	00000000-0000-0000-0000-000000000000	RESTORE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 19:20:38.562588+00	\N	\N	system	restore	write	sensitive
70fa2c17-9ae1-47ac-902f-8e4be00cfee3	00000000-0000-0000-0000-000000000000	RESTORE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 19:28:52.730652+00	\N	\N	system	restore	write	sensitive
ade5b401-2267-459b-9d4a-d8de5df23147	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 19:32:02.210898+00	\N	\N	system	deprecate	write	sensitive
579f2c15-57ea-4edd-9be6-50996d150b4f	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 19:32:40.630147+00	\N	\N	system	deprecate	write	sensitive
f94c7c4e-5abf-43ea-8d71-edf5f2c03f01	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	177115f8-1121-4fa0-8e81-f6db326fabf6	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-28T20:15:40.781Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test", "type": "test_schema_1756409008", "active": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T16:38:29.074Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 16:38:29.088052+00	\N	\N	admin	policy_update	write	sensitive
1546823e-f333-49e0-9ec9-3576fae0c471	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	40a63e03-d616-4877-9f4b-cbf5fcad8cdc	{"name": "p11", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "40a63e03-d616-4877-9f4b-cbf5fcad8cdc", "rego_code": null, "created_at": "2025-08-29T08:30:16.830Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 11, "name": "test", "type": "test_schema_1756409008", "active": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T16:27:05.278Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p11", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p11", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "40a63e03-d616-4877-9f4b-cbf5fcad8cdc", "rego_code": null, "created_at": "2025-08-29T08:30:16.830Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 11, "name": "test", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T17:06:05.918Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p11", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 17:06:05.925988+00	\N	\N	admin	policy_update	write	sensitive
7a890231-f61e-49e9-b8b2-c1ea1b1b94bd	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	40a63e03-d616-4877-9f4b-cbf5fcad8cdc	{"name": "p11", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "40a63e03-d616-4877-9f4b-cbf5fcad8cdc", "rego_code": null, "created_at": "2025-08-29T08:30:16.830Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 11, "name": "test", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T17:06:05.918Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p11", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p11", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "40a63e03-d616-4877-9f4b-cbf5fcad8cdc", "rego_code": null, "created_at": "2025-08-29T08:30:16.830Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 11, "name": "test", "type": "test_schema_1756409008", "active": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T17:06:45.524Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p11", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 17:06:45.535843+00	\N	\N	admin	policy_update	write	sensitive
400dd550-8cf3-410a-8eed-e76ca6b1daf0	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	556ab33f-83eb-41c8-96df-afec69641f6f	\N	{"name": "p16", "category": "test_schema_1756409008", "severity": "low", "is_active": true}	**********		2025-08-29 17:20:49.661716+00	\N	\N	admin	policy_creation	write	sensitive
1f2f5093-83d6-43fd-a062-352628e71204	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	556ab33f-83eb-41c8-96df-afec69641f6f	{"name": "p16", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "556ab33f-83eb-41c8-96df-afec69641f6f", "rego_code": null, "created_at": "2025-08-29T17:20:49.647Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"name": "test", "type": "nn_type", "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T17:20:49.647Z", "updated_by": null, "description": "p16", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p16", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "556ab33f-83eb-41c8-96df-afec69641f6f", "rego_code": null, "created_at": "2025-08-29T17:20:49.647Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"name": "test", "type": "nn_type", "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T17:21:45.854Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p16", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 17:21:45.865854+00	\N	\N	admin	policy_update	write	sensitive
8ff4c797-46ed-487d-9087-186413f31781	00000000-0000-0000-0000-000000000000	RESTORE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 19:15:00.9114+00	\N	\N	system	restore	write	sensitive
6c672c2f-ce46-49d4-a938-c651a9288ee3	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	556ab33f-83eb-41c8-96df-afec69641f6f	{"name": "p16", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "556ab33f-83eb-41c8-96df-afec69641f6f", "rego_code": null, "created_at": "2025-08-29T17:20:49.647Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"name": "test", "type": "nn_type", "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T17:21:45.854Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p16", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p16", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "556ab33f-83eb-41c8-96df-afec69641f6f", "rego_code": null, "created_at": "2025-08-29T17:20:49.647Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"name": "test", "type": "test", "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T17:35:16.496Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p16", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 17:35:16.505256+00	\N	\N	admin	policy_update	write	sensitive
68c47baa-cd76-4732-bd20-a5cfd1180e59	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	556ab33f-83eb-41c8-96df-afec69641f6f	{"name": "p16", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "556ab33f-83eb-41c8-96df-afec69641f6f", "rego_code": null, "created_at": "2025-08-29T17:20:49.647Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"name": "test", "type": "test", "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T17:35:16.496Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p16", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p16", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "556ab33f-83eb-41c8-96df-afec69641f6f", "rego_code": null, "created_at": "2025-08-29T17:20:49.647Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"name": "test", "type": "test", "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T17:35:53.995Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p16", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 17:35:54.000369+00	\N	\N	admin	policy_update	write	sensitive
6c5653b0-bde2-427b-8f75-05ae19d196db	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	556ab33f-83eb-41c8-96df-afec69641f6f	{"name": "p16", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "556ab33f-83eb-41c8-96df-afec69641f6f", "rego_code": null, "created_at": "2025-08-29T17:20:49.647Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"name": "test", "type": "test", "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T17:35:53.995Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p16", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p16", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "556ab33f-83eb-41c8-96df-afec69641f6f", "rego_code": null, "created_at": "2025-08-29T17:20:49.647Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"name": "test", "type": "test", "enabled": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T17:36:03.360Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p16", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 17:36:03.368564+00	\N	\N	admin	policy_update	write	sensitive
c417a5af-15b0-40f5-9007-0b5c5f7b53f7	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	556ab33f-83eb-41c8-96df-afec69641f6f	{"name": "p16", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "556ab33f-83eb-41c8-96df-afec69641f6f", "rego_code": null, "created_at": "2025-08-29T17:20:49.647Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"name": "test", "type": "test", "enabled": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T17:36:03.360Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p16", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p16", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "556ab33f-83eb-41c8-96df-afec69641f6f", "rego_code": null, "created_at": "2025-08-29T17:20:49.647Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"name": "test", "type": "test", "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T17:36:06.430Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p16", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 17:36:06.437325+00	\N	\N	admin	policy_update	write	sensitive
f51ebfac-c959-4101-80e8-8b5e14b137a1	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	d6a525b7-4d00-4138-a63d-1af704837e69	\N	{"name": "p17", "category": "compliance", "severity": "medium", "is_active": true}	**********		2025-08-29 18:00:04.422488+00	\N	\N	admin	policy_creation	write	sensitive
8498fce8-9e75-446a-bbc6-b16d087d5a6b	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 19:16:14.501891+00	\N	\N	system	deprecate	write	sensitive
c0a4d400-630c-4466-8b6a-0f9255d938a6	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 19:17:15.385192+00	\N	\N	system	deprecate	write	sensitive
07c03a35-076c-4401-abf5-747e56962682	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 19:21:00.969084+00	\N	\N	system	deprecate	write	sensitive
2b894d08-00d6-4423-b41d-1c2d50cf0455	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	40a63e03-d616-4877-9f4b-cbf5fcad8cdc	{"name": "p11", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "40a63e03-d616-4877-9f4b-cbf5fcad8cdc", "rego_code": null, "created_at": "2025-08-29T08:30:16.830Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 11, "name": "test", "type": "test_schema_1756409008", "active": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T17:06:45.524Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p11", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p11", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "40a63e03-d616-4877-9f4b-cbf5fcad8cdc", "rego_code": null, "created_at": "2025-08-29T08:30:16.830Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 11, "name": "test", "type": "test_schema_1756409008", "active": false, "enabled": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T18:11:41.390Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p11", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 18:11:41.397397+00	\N	\N	admin	policy_update	write	sensitive
87e86907-9d13-4e9b-858f-6c14ff965571	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	177115f8-1121-4fa0-8e81-f6db326fabf6	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test", "type": "test_schema_1756409008", "active": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T16:38:29.074Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test", "type": "test_schema_1756409008", "active": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T18:13:24.760Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 18:13:24.774147+00	\N	\N	admin	policy_update	write	sensitive
14553e3e-b344-4dbd-977a-cc7f30c5970f	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	2ada8019-e800-4aed-a43d-69f7c461b7f0	{"name": "p9", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2ada8019-e800-4aed-a43d-69f7c461b7f0", "rego_code": null, "created_at": "2025-08-28T20:06:24.582Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test_name", "type": "test_schema_1756409008", "active": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-28T20:06:24.582Z", "updated_by": null, "description": "p9", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p9", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2ada8019-e800-4aed-a43d-69f7c461b7f0", "rego_code": null, "created_at": "2025-08-28T20:06:24.582Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test_name", "type": "test_schema_1756409008", "active": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T18:13:39.824Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p9", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 18:13:39.83545+00	\N	\N	admin	policy_update	write	sensitive
032db9ec-e1d1-4a50-a50a-aa1560579e57	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 18:13:51.817383+00	\N	\N	system	deprecate	write	sensitive
76ab02af-e5b8-4950-a3bd-5bfcb9a2093a	00000000-0000-0000-0000-000000000000	RESTORE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 18:16:55.192779+00	\N	\N	system	restore	write	sensitive
10b055a6-6010-44b7-9171-a8ca108d2448	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 18:17:10.02008+00	\N	\N	system	deprecate	write	sensitive
91ea146e-45fa-4cc8-a35c-080354c5dbec	00000000-0000-0000-0000-000000000000	RESTORE	policy_group	309aef0b-8e87-4d69-b6e7-21b7c89417cb	\N	\N	**********		2025-08-29 18:30:17.762502+00	\N	\N	system	restore	write	sensitive
08e86231-7099-452c-810e-99e09b9685fa	00000000-0000-0000-0000-000000000000	RESTORE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 18:31:29.938435+00	\N	\N	system	restore	write	sensitive
e1156a5d-a6c6-4563-8da7-51370e6a2e3d	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	309aef0b-8e87-4d69-b6e7-21b7c89417cb	\N	\N	**********		2025-08-29 18:31:44.725878+00	\N	\N	system	deprecate	write	sensitive
485f604b-3d6a-4424-98fe-26a1774e68a1	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 18:35:45.177469+00	\N	\N	system	deprecate	write	sensitive
41e05f33-cf11-475c-9470-e5f659447142	00000000-0000-0000-0000-000000000000	RESTORE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 18:46:43.306182+00	\N	\N	system	restore	write	sensitive
a6e89ce6-51ab-4d08-a650-a7be1aedbde6	00000000-0000-0000-0000-000000000000	RESTORE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 18:46:52.775804+00	\N	\N	system	restore	write	sensitive
8780b7e1-e021-474e-becf-5912299a912f	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 18:47:08.590224+00	\N	\N	system	deprecate	write	sensitive
cb24beae-0e37-4b51-a797-89410775dd3f	00000000-0000-0000-0000-000000000000	RESTORE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 18:47:15.392359+00	\N	\N	system	restore	write	sensitive
b6a20cbc-28ba-4910-8303-eed080aaf5e7	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	d1b2c3d4-e5f6-7890-abcd-111111111111	\N	\N	**********		2025-08-29 18:47:47.162987+00	\N	\N	system	deprecate	write	sensitive
e128977c-1064-4e36-a56d-59f4d82e8042	00000000-0000-0000-0000-000000000000	RESTORE	policy_group	d1b2c3d4-e5f6-7890-abcd-111111111111	\N	\N	**********		2025-08-29 18:47:55.576057+00	\N	\N	system	restore	write	sensitive
74058832-ff97-4448-9cdc-2f051726e499	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 18:48:56.040836+00	\N	\N	system	deprecate	write	sensitive
2ee10f63-a475-4ab9-b1ef-ea3cf8eaa4fd	00000000-0000-0000-0000-000000000000	RESTORE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-08-29 19:32:51.000339+00	\N	\N	system	restore	write	sensitive
6a7a3aa3-2fb9-4bd9-822e-fb809d3a2117	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	2ada8019-e800-4aed-a43d-69f7c461b7f0	{"name": "p9", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2ada8019-e800-4aed-a43d-69f7c461b7f0", "rego_code": null, "created_at": "2025-08-28T20:06:24.582Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test_name", "type": "test_schema_1756409008", "active": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T18:13:39.824Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p9", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p9", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "2ada8019-e800-4aed-a43d-69f7c461b7f0", "rego_code": null, "created_at": "2025-08-28T20:06:24.582Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test_name", "type": "test_schema_1756409008", "active": false, "enabled": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:35:40.797Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p9", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 19:35:40.804615+00	\N	\N	admin	policy_update	write	sensitive
05fee0d7-778b-4a86-9170-a1f2f65d782a	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	2ada8019-e800-4aed-a43d-69f7c461b7f0	{"name": "p9", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "2ada8019-e800-4aed-a43d-69f7c461b7f0", "rego_code": null, "created_at": "2025-08-28T20:06:24.582Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test_name", "type": "test_schema_1756409008", "active": false, "enabled": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:35:40.797Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p9", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p9", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2ada8019-e800-4aed-a43d-69f7c461b7f0", "rego_code": null, "created_at": "2025-08-28T20:06:24.582Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test_name", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:35:46.794Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p9", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 19:35:46.798687+00	\N	\N	admin	policy_update	write	sensitive
1f8ebb15-94ba-486d-8a5e-2c630beb4d65	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	177115f8-1121-4fa0-8e81-f6db326fabf6	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test", "type": "test_schema_1756409008", "active": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T18:13:24.760Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test", "type": "test_schema_1756409008", "active": false, "enabled": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:35:49.878Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 19:35:49.884192+00	\N	\N	admin	policy_update	write	sensitive
9ce17b23-d0cd-4448-ad7a-742023ae9a2e	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	177115f8-1121-4fa0-8e81-f6db326fabf6	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test", "type": "test_schema_1756409008", "active": false, "enabled": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:35:49.878Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:36:05.831Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 19:36:05.83701+00	\N	\N	admin	policy_update	write	sensitive
80870355-9e94-421b-96a3-bb7f03cafd57	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	177115f8-1121-4fa0-8e81-f6db326fabf6	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:36:05.831Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:36:55.654Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 19:36:55.662975+00	\N	\N	admin	policy_update	write	sensitive
5bc08c90-7e0e-4f99-a887-6ac5be5e9e31	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	2ada8019-e800-4aed-a43d-69f7c461b7f0	{"name": "p9", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2ada8019-e800-4aed-a43d-69f7c461b7f0", "rego_code": null, "created_at": "2025-08-28T20:06:24.582Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test_name", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:35:46.794Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p9", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p9", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2ada8019-e800-4aed-a43d-69f7c461b7f0", "rego_code": null, "created_at": "2025-08-28T20:06:24.582Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test_name", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:37:07.439Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p9", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 19:37:07.446546+00	\N	\N	admin	policy_update	write	sensitive
ea4a1552-bb2c-4e07-b1d6-d4317ca38517	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	177115f8-1121-4fa0-8e81-f6db326fabf6	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:36:55.654Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test", "type": "test_schema_1756409008", "active": false, "enabled": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:37:39.112Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 19:37:39.1231+00	\N	\N	admin	policy_update	write	sensitive
7a5f89e6-95f5-443e-8323-d48fa7d9a9f0	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	177115f8-1121-4fa0-8e81-f6db326fabf6	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test", "type": "test_schema_1756409008", "active": false, "enabled": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:37:39.112Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:37:50.185Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 19:37:50.190438+00	\N	\N	admin	policy_update	write	sensitive
680dac87-1409-4b00-8146-e23daab09a94	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	{"addedPolicyIds": ["a0592a8e-2286-444b-88ab-d5d69558510c"]}	**********		2025-08-29 20:37:39.387291+00	\N	\N	system	add_policy	write	sensitive
8c6adca9-fd2a-47f4-b2d7-795da5928ec9	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	2ada8019-e800-4aed-a43d-69f7c461b7f0	{"name": "p9", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2ada8019-e800-4aed-a43d-69f7c461b7f0", "rego_code": null, "created_at": "2025-08-28T20:06:24.582Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test_name", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:37:07.439Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p9", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p9", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "2ada8019-e800-4aed-a43d-69f7c461b7f0", "rego_code": null, "created_at": "2025-08-28T20:06:24.582Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test_name", "type": "test_schema_1756409008", "active": false, "enabled": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:38:43.663Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p9", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 19:38:43.666734+00	\N	\N	admin	policy_update	write	sensitive
3346ae3e-c8c5-4511-9b22-32821c7ae703	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	2ada8019-e800-4aed-a43d-69f7c461b7f0	{"name": "p9", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "2ada8019-e800-4aed-a43d-69f7c461b7f0", "rego_code": null, "created_at": "2025-08-28T20:06:24.582Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test_name", "type": "test_schema_1756409008", "active": false, "enabled": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:38:43.663Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p9", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p9", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2ada8019-e800-4aed-a43d-69f7c461b7f0", "rego_code": null, "created_at": "2025-08-28T20:06:24.582Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test_name", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:38:57.202Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p9", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 19:38:57.212552+00	\N	\N	admin	policy_update	write	sensitive
395c6871-d704-4168-a321-d7e8501728f0	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	2ada8019-e800-4aed-a43d-69f7c461b7f0	{"name": "p9", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2ada8019-e800-4aed-a43d-69f7c461b7f0", "rego_code": null, "created_at": "2025-08-28T20:06:24.582Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test_name", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:38:57.202Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p9", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p9", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2ada8019-e800-4aed-a43d-69f7c461b7f0", "rego_code": null, "created_at": "2025-08-28T20:06:24.582Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test_name", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:39:06.149Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p9", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 19:39:06.156229+00	\N	\N	admin	policy_update	write	sensitive
df596d6e-950e-4257-b91d-9b5cc1626769	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	177115f8-1121-4fa0-8e81-f6db326fabf6	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:37:50.185Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p10", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "177115f8-1121-4fa0-8e81-f6db326fabf6", "rego_code": null, "created_at": "2025-08-28T20:15:26.894Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 0, "name": "test", "type": "test_schema_1756409008", "active": false, "enabled": true, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-29T19:39:15.681Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p10", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 19:39:15.686778+00	\N	\N	admin	policy_update	write	sensitive
f0a73d34-3d9f-4fe6-93e1-84edd93081ee	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	a0592a8e-2286-444b-88ab-d5d69558510c	\N	{"name": "test_active_policy", "category": "test_schema_1756409008", "severity": "medium"}	**********		2025-08-29 20:37:33.418399+00	\N	\N	admin	policy_creation	write	sensitive
a0b4ea0e-887f-4623-a0a8-0d90ace67277	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	c3bd8586-ce3f-4d21-bdb0-f1afb644fc4a	{"name": "p8", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "c3bd8586-ce3f-4d21-bdb0-f1afb644fc4a", "rego_code": null, "created_at": "2025-08-28T19:59:32.192Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 12, "name": "test_name", "type": "test_type", "active": false, "severity": "low"}, "deleted_at": null, "updated_at": "2025-08-28T19:59:32.192Z", "updated_by": null, "description": "p8", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p8", "version": 1, "blob_url": null, "category": "test_schema_1756409008", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "c3bd8586-ce3f-4d21-bdb0-f1afb644fc4a", "rego_code": null, "created_at": "2025-08-28T19:59:32.192Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"age": 12, "name": "test_name", "type": "test_type", "active": false, "severity": "critical"}, "deleted_at": null, "updated_at": "2025-08-29T20:57:38.269Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p8", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 20:57:38.275849+00	\N	\N	admin	policy_update	write	sensitive
88faff9d-2cc3-4fa9-9b16-57fb8ad43795	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-29T12:53:27.892Z", "updated_by": null, "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-29T20:57:45.925Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 20:57:45.933474+00	\N	\N	admin	policy_update	write	sensitive
f1beb518-bb82-4665-a7de-512da5ce9e61	00000000-0000-0000-0000-000000000000	UPDATE	policy_group	d1b2c3d4-e5f6-7890-abcd-111111111111	{"name": "HIPAA Compliance Policy Suite", "tags": [], "status": "active", "version": "v1.0.0", "group_id": "d1b2c3d4-e5f6-7890-abcd-111111111111", "severity": "medium", "created_at": "2025-08-27T17:57:54.329Z", "created_by": "a1b2c3d4-e5f6-7890-abcd-111111111111", "deleted_at": null, "updated_at": "2025-08-29T18:47:55.573Z", "description": "Comprehensive HIPAA privacy and security policies for healthcare operations", "is_template": false}	{"name": "HIPAA Compliance Policy Suite", "tags": ["Patient Intake", "CDI"], "status": "active", "version": "v1.0.0", "group_id": "d1b2c3d4-e5f6-7890-abcd-111111111111", "severity": "medium", "created_at": "2025-08-27T17:57:54.329Z", "created_by": "a1b2c3d4-e5f6-7890-abcd-111111111111", "deleted_at": null, "updated_at": "2025-08-29T22:03:18.942Z", "description": "Comprehensive HIPAA privacy and security policies for healthcare operations", "is_template": false}	**********		2025-08-29 22:03:18.952325+00	\N	\N	system	update	write	sensitive
01c821de-f0cd-4c20-9216-46920e23a9ee	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-29T20:57:45.925Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-29T22:09:14.745Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 22:09:14.752276+00	\N	\N	admin	policy_update	write	sensitive
e3da9f53-6de6-4dc7-88d9-43ddf3ae3c04	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": true, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-28T06:10:46.049Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": true, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-29T22:11:24.725Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 22:11:24.732045+00	\N	\N	admin	policy_update	write	sensitive
5cb61ddd-0c4a-44cf-9a34-6dd3724b3483	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2	{"name": "Demo Medical Privacy Policy", "version": 1, "blob_url": null, "category": "Medical Privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "rego_code": null, "created_at": "2025-08-28T04:27:16.865Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-08-28T06:29:34.940Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Test medical privacy policy for demo flow - UPDATED", "policy_type": "medical_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Demo Medical Privacy Policy", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "rego_code": null, "created_at": "2025-08-28T04:27:16.865Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-08-29T22:14:23.982Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Test medical privacy policy for demo flow - UPDATED", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-29 22:14:23.989363+00	\N	\N	admin	policy_update	write	sensitive
eb0a746a-20ac-41a1-97bf-6c47570c49b6	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": true, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-29T22:11:24.725Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T06:59:53.278Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 06:59:53.297108+00	\N	\N	admin	policy_update	write	sensitive
cea1e6aa-c5af-4429-8e75-4def1177dfd3	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T06:59:53.278Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T07:00:57.855Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 07:00:57.866313+00	\N	\N	admin	policy_update	write	sensitive
6e522fbf-516b-475c-ae97-d3d2361a175f	00000000-0000-0000-0000-000000000000	REMOVE_POLICY	policy_group	bf6bde98-2093-4da8-b6d8-3929ef674aff	{"policyId": "6fe10905-ed59-42e8-bc52-e037c86e69fc"}	\N	**********		2025-08-30 07:02:07.476319+00	\N	\N	system	remove_policy	write	sensitive
f54f334c-404d-4979-bb17-09a00299697d	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T07:00:57.855Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T07:02:27.630Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 07:02:27.640232+00	\N	\N	admin	policy_update	write	sensitive
18c125a7-d060-4085-b96e-ee57c6c0af79	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-29T22:09:14.745Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T08:29:02.049Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 08:29:02.06181+00	\N	\N	admin	policy_update	write	sensitive
4a35e367-8df2-4215-98ff-d61d137944e6	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T08:29:02.049Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T08:29:12.119Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 08:29:12.122491+00	\N	\N	admin	policy_update	write	sensitive
34443964-aa0d-4a06-bbd2-d6d9283d8b5f	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T08:29:12.119Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T08:29:14.678Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 08:29:14.681929+00	\N	\N	admin	policy_update	write	sensitive
7eec0a74-0c6e-413f-ac6a-13570999579a	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T08:29:14.678Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T08:29:44.275Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 08:29:44.288072+00	\N	\N	admin	policy_update	write	sensitive
9dbf6111-7ecb-4f7d-a892-56619cf985ca	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T08:29:44.275Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T08:29:54.526Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 08:29:54.531835+00	\N	\N	admin	policy_update	write	sensitive
1a81c9f2-f007-4e05-aa6d-811957443200	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T08:29:54.526Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T08:30:18.056Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 08:30:18.066329+00	\N	\N	admin	policy_update	write	sensitive
a79525c3-1da2-42ab-b1dd-158780bf7516	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T08:30:18.056Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T09:52:27.159Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 09:52:27.1673+00	\N	\N	admin	policy_update	write	sensitive
003f7811-ad08-437c-a886-9b24acb0662b	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T09:52:27.159Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T14:26:21.112Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 14:26:21.12679+00	\N	\N	admin	policy_update	write	sensitive
6df7c96a-5d0d-4e9e-86a5-2107096e47c4	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T14:26:21.112Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T14:26:36.014Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 14:26:36.022488+00	\N	\N	admin	policy_update	write	sensitive
e679be0b-06af-4a47-8394-6f622a0e9bab	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T14:26:36.014Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T14:27:30.767Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 14:27:30.778185+00	\N	\N	admin	policy_update	write	sensitive
1bf8035b-104d-4c69-a592-206283d7356c	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T07:02:27.630Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:36:10.552Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:36:10.563559+00	\N	\N	admin	policy_update	write	sensitive
303827ed-55e8-4db5-8727-fc8f51215a80	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:36:10.552Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:43:35.963Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:43:35.974846+00	\N	\N	admin	policy_update	write	sensitive
37f9f4ab-8355-49ac-b61a-c98f302712bc	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:43:35.963Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": true, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:43:40.521Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:43:40.529638+00	\N	\N	admin	policy_update	write	sensitive
9e64b73d-e69e-4cc7-9724-8b7a53133e38	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": true, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:43:40.521Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:43:45.746Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:43:45.749582+00	\N	\N	admin	policy_update	write	sensitive
0f10a210-8bb8-41fb-8ce9-b9d07797e1e4	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:43:45.746Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": true, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:44:20.531Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:44:20.537597+00	\N	\N	admin	policy_update	write	sensitive
b4ca87d1-ec61-4ece-9b21-c4b7a45a289a	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": true, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:44:20.531Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:44:24.004Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:44:24.006613+00	\N	\N	admin	policy_update	write	sensitive
6dca3944-30a3-447e-a684-c0303637f7de	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T14:27:30.767Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T17:44:29.542Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:44:29.554345+00	\N	\N	admin	policy_update	write	sensitive
f466ae8e-b5b6-4710-ad7a-7641b3eb04d7	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T17:44:29.542Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T17:44:34.410Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:44:34.413214+00	\N	\N	admin	policy_update	write	sensitive
adfb790e-4847-4c08-9d23-538242ed99ca	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T17:44:34.410Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T17:44:53.060Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:44:53.071322+00	\N	\N	admin	policy_update	write	sensitive
9f5f3cfe-d824-4870-8835-f1a213619891	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:44:24.004Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:45:31.339Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:45:31.355019+00	\N	\N	admin	policy_update	write	sensitive
1b974df0-3743-4c87-8b0e-ada94b46f935	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:45:31.339Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:45:58.064Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:45:58.075808+00	\N	\N	admin	policy_update	write	sensitive
a163a28c-8342-45e6-af8e-e3b0323901d4	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:45:58.064Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:46:22.474Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:46:22.484942+00	\N	\N	admin	policy_update	write	sensitive
6f2410d8-97d0-4ff5-9c42-62c271936717	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:46:22.474Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:50:21.793Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:50:21.802845+00	\N	\N	admin	policy_update	write	sensitive
6022100d-a923-4f1a-a135-9de2d99564a8	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:50:21.793Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": true, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:54:59.376Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:54:59.383418+00	\N	\N	admin	policy_update	write	sensitive
895ebbfb-b570-4912-81bd-0f662bcaec7a	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": true, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:54:59.376Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:55:13.825Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:55:13.834579+00	\N	\N	admin	policy_update	write	sensitive
1d056579-5290-4636-88ed-ec8fb4c51a9c	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	9340a076-0ac3-4c43-8845-6143b2bc032a	{"name": "p7", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "rego_code": null, "created_at": "2025-08-28T19:37:57.463Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-28T19:37:57.463Z", "updated_by": null, "description": "p7", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p7", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "rego_code": null, "created_at": "2025-08-28T19:37:57.463Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": false, "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T17:55:36.022Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p7", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:55:36.055095+00	\N	\N	admin	policy_update	write	sensitive
5b7e5684-11c7-4947-8f35-9afc07961a40	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:55:13.825Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:55:53.352Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:55:53.357608+00	\N	\N	admin	policy_update	write	sensitive
b4670d69-d81b-4a27-b8ad-bb096a3ded97	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	6fe10905-ed59-42e8-bc52-e037c86e69fc	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:55:53.352Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p6", "version": 1, "blob_url": null, "category": "compliance", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "6fe10905-ed59-42e8-bc52-e037c86e69fc", "rego_code": null, "created_at": "2025-08-28T04:17:49.436Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "compliance", "enabled": false, "severity": "low", "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}, "deleted_at": null, "updated_at": "2025-08-30T17:56:11.206Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p6", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["compliance_officer"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 17:56:11.217666+00	\N	\N	admin	policy_update	write	sensitive
7efc9aec-a96b-4fe2-a16e-f483340a5e54	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-27T22:02:54.811Z", "updated_by": null, "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:03:16.592Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 18:03:16.602334+00	\N	\N	admin	policy_update	write	sensitive
ae07f335-8a54-4a9b-8f01-2af02e9e0488	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	\N	{"addedPolicyIds": ["2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d"]}	**********		2025-08-30 18:08:44.251338+00	\N	\N	system	add_policy	write	sensitive
cc5746af-4f84-4619-a348-effaad35cacb	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:03:16.592Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:09:06.380Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 18:09:06.386644+00	\N	\N	admin	policy_update	write	sensitive
fead5b2a-fd70-43c3-a0b8-a63023fef044	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:09:06.380Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:09:32.288Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 18:09:32.291301+00	\N	\N	admin	policy_update	write	sensitive
4919b02a-2549-4a0a-afe4-5fa1a0933a45	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	\N	{"addedPolicyIds": ["2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d"]}	**********		2025-08-30 18:09:38.733449+00	\N	\N	system	add_policy	write	sensitive
822c40ba-9f5b-401d-979d-d92a823e35d7	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	\N	{"addedPolicyIds": ["2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d"]}	**********		2025-08-30 18:20:13.419794+00	\N	\N	system	add_policy	write	sensitive
1f7fccf5-8722-4adf-97fa-15cc8c50a1bb	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:09:32.288Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:22:55.939Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 18:22:55.947321+00	\N	\N	admin	policy_update	write	sensitive
a9ea5346-8a89-44ed-a80b-f2559e301c80	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	\N	{"addedPolicyIds": ["2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d"]}	**********		2025-08-30 18:26:32.366886+00	\N	\N	system	add_policy	write	sensitive
c68603a2-d918-4248-b750-7dc8575182f6	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:22:55.939Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:27:07.853Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 18:27:07.856986+00	\N	\N	admin	policy_update	write	sensitive
5a7c505b-79e2-4c58-a98d-45efea80d79c	00000000-0000-0000-0000-000000000000	ADD_POLICY	policy_group	d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf	\N	{"addedPolicyIds": ["2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d"]}	**********		2025-08-30 18:30:58.145065+00	\N	\N	system	add_policy	write	sensitive
95d58397-d09f-4c4b-9488-f614fb8291d3	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:27:07.853Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:31:10.023Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 18:31:10.028157+00	\N	\N	admin	policy_update	write	sensitive
3f4c473e-7c83-4ddc-bc55-0a09014f22ca	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:31:10.023Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:31:36.269Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 18:31:36.271941+00	\N	\N	admin	policy_update	write	sensitive
69e2b9c4-ef2d-44a0-86a6-7b78c14f3c69	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:31:36.269Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:32:23.307Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 18:32:23.312709+00	\N	\N	admin	policy_update	write	sensitive
c75b6c76-2ce7-48c5-87f7-3aba4cdc7a82	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:32:23.307Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p4", "version": 1, "blob_url": null, "category": "access_control", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "2bb2b4f3-843e-4f0e-b7c1-ed959591cf8d", "rego_code": null, "created_at": "2025-08-27T22:02:54.811Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "severity": "low", "allowed_roles": ["admin"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-30T18:33:02.124Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p4", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 18:33:02.131411+00	\N	\N	admin	policy_update	write	sensitive
1586d911-8b25-4190-9830-46d652a3d38a	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T17:44:53.060Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T18:50:11.520Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 18:50:11.52992+00	\N	\N	admin	policy_update	write	sensitive
84de39d3-2ea3-4eaf-a3e5-0d4c32e2b085	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T18:50:11.520Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T18:52:10.817Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 18:52:10.824518+00	\N	\N	admin	policy_update	write	sensitive
74647d0c-a114-4831-ba6a-7a8baba987f7	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T18:52:10.817Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T18:52:56.297Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 18:52:56.302218+00	\N	\N	admin	policy_update	write	sensitive
d05d05a2-4aed-43a1-b425-8e76c1ed1e90	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T18:52:56.297Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T18:53:10.695Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 18:53:10.698758+00	\N	\N	admin	policy_update	write	sensitive
f5e334b2-b5ba-434c-9a21-b24cfe393454	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	9340a076-0ac3-4c43-8845-6143b2bc032a	{"name": "p7", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "rego_code": null, "created_at": "2025-08-28T19:37:57.463Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": false, "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T17:55:36.022Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p7", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p7", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "rego_code": null, "created_at": "2025-08-28T19:37:57.463Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": false, "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T18:59:27.467Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p7", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 18:59:27.483355+00	\N	\N	admin	policy_update	write	sensitive
7c7f9fb4-36b7-41e6-a46b-c45c0a79676d	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T18:53:10.695Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T18:59:48.798Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 18:59:48.806598+00	\N	\N	admin	policy_update	write	sensitive
2071589d-682b-4b38-88db-9d9dba1fc522	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	9340a076-0ac3-4c43-8845-6143b2bc032a	{"name": "p7", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "rego_code": null, "created_at": "2025-08-28T19:37:57.463Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": false, "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T18:59:27.467Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p7", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p7", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "rego_code": null, "created_at": "2025-08-28T19:37:57.463Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": false, "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T19:07:09.127Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p7", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 19:07:09.1374+00	\N	\N	admin	policy_update	write	sensitive
3693cf3f-cca1-4655-a2be-592fed4430ee	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T18:59:48.798Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T19:07:23.055Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 19:07:23.059029+00	\N	\N	admin	policy_update	write	sensitive
ec64fcfe-9038-4cbc-a201-930b31a45ff9	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T19:07:23.055Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T19:09:14.525Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 19:09:14.53217+00	\N	\N	admin	policy_update	write	sensitive
0d39ae45-485c-4dbf-8b09-6f14ad27e4f3	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T19:09:14.525Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T19:19:11.170Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 19:19:11.177346+00	\N	\N	admin	policy_update	write	sensitive
ba21af86-9533-4200-8bb6-dcc50a220f52	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T19:19:11.170Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T19:20:10.101Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 19:20:10.108564+00	\N	\N	admin	policy_update	write	sensitive
34c4df9c-de77-4243-b1a7-fcd8ca9294f1	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	9340a076-0ac3-4c43-8845-6143b2bc032a	{"name": "p7", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "rego_code": null, "created_at": "2025-08-28T19:37:57.463Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": false, "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T19:07:09.127Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p7", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p7", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "rego_code": null, "created_at": "2025-08-28T19:37:57.463Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": false, "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T19:23:36.048Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p7", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 19:23:36.057794+00	\N	\N	admin	policy_update	write	sensitive
29e74c28-499b-49bd-aee5-3f2e65be3ed4	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T19:20:10.101Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T19:32:29.465Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 19:32:29.471569+00	\N	\N	admin	policy_update	write	sensitive
b95dc1dd-a821-4ce0-9b30-654798f5adc9	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T19:32:29.465Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T19:33:16.829Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 19:33:16.833735+00	\N	\N	admin	policy_update	write	sensitive
8305dae0-bc21-4076-8fa2-978db54f9075	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	9340a076-0ac3-4c43-8845-6143b2bc032a	{"name": "p7", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "rego_code": null, "created_at": "2025-08-28T19:37:57.463Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": false, "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T19:23:36.048Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p7", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p7", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "rego_code": null, "created_at": "2025-08-28T19:37:57.463Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T19:33:20.888Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p7", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 19:33:20.89325+00	\N	\N	admin	policy_update	write	sensitive
89c846f9-6640-4213-9ca2-485d266c0587	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	9340a076-0ac3-4c43-8845-6143b2bc032a	{"name": "p7", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "rego_code": null, "created_at": "2025-08-28T19:37:57.463Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T19:33:20.888Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p7", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p7", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "rego_code": null, "created_at": "2025-08-28T19:37:57.463Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T19:34:59.463Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p7", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 19:34:59.46875+00	\N	\N	admin	policy_update	write	sensitive
dc721a87-7322-424f-8116-8e21aa79cb1b	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	9340a076-0ac3-4c43-8845-6143b2bc032a	{"name": "p7", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "rego_code": null, "created_at": "2025-08-28T19:37:57.463Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T19:34:59.463Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p7", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p7", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "rego_code": null, "created_at": "2025-08-28T19:37:57.463Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T19:50:10.075Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p7", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 19:50:10.082224+00	\N	\N	admin	policy_update	write	sensitive
8b2ad9ac-68a8-40b4-baa7-635e622d860f	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	63ada6a9-1bfb-422f-82f7-d2a4f2607ce9	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T19:33:16.829Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p14", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "63ada6a9-1bfb-422f-82f7-d2a4f2607ce9", "rego_code": null, "created_at": "2025-08-29T12:53:27.892Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}, "deleted_at": null, "updated_at": "2025-08-30T19:50:15.679Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p14", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse", "admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 19:50:15.684013+00	\N	\N	admin	policy_update	write	sensitive
a5e402ec-4be1-44a2-8961-a2217d7948d6	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	9340a076-0ac3-4c43-8845-6143b2bc032a	{"name": "p7", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "low", "blob_path": null, "is_active": true, "policy_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "rego_code": null, "created_at": "2025-08-28T19:37:57.463Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T19:50:10.075Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p7", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p7", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "low", "blob_path": null, "is_active": false, "policy_id": "9340a076-0ac3-4c43-8845-6143b2bc032a", "rego_code": null, "created_at": "2025-08-28T19:37:57.463Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": false, "severity": "low", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 20}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T19:50:37.403Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p7", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 19:50:37.409308+00	\N	\N	admin	policy_update	write	sensitive
6536c1aa-6bb9-438a-b048-ec9701133fa3	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	bd7363f8-6104-449e-873a-db0539f0fa17	{"name": "p1", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "medium", "blob_path": null, "is_active": true, "policy_id": "bd7363f8-6104-449e-873a-db0539f0fa17", "rego_code": null, "created_at": "2025-08-27T18:11:04.643Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "severity": "medium", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-27T18:11:04.643Z", "updated_by": null, "description": "p1", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p1", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "medium", "blob_path": null, "is_active": false, "policy_id": "bd7363f8-6104-449e-873a-db0539f0fa17", "rego_code": null, "created_at": "2025-08-27T18:11:04.643Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": false, "severity": "medium", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T19:53:47.393Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p1", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 19:53:47.402779+00	\N	\N	admin	policy_update	write	sensitive
45a5fe24-6784-42f9-9180-680f76c86e66	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	bd7363f8-6104-449e-873a-db0539f0fa17	{"name": "p1", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "medium", "blob_path": null, "is_active": false, "policy_id": "bd7363f8-6104-449e-873a-db0539f0fa17", "rego_code": null, "created_at": "2025-08-27T18:11:04.643Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": false, "severity": "medium", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T19:53:47.393Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p1", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p1", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "medium", "blob_path": null, "is_active": true, "policy_id": "bd7363f8-6104-449e-873a-db0539f0fa17", "rego_code": null, "created_at": "2025-08-27T18:11:04.643Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "medium", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T19:54:11.359Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p1", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-30 19:54:11.365619+00	\N	\N	admin	policy_update	write	sensitive
dca3b806-d523-42c8-a0cd-7b461943adc7	00000000-0000-0000-0000-000000000000	CREATE	policy_group	71e6509d-15af-44de-be9d-3b3040f7d6ed	\N	{"name": "E2E Test Policy Group", "tags": ["testing", "e2e", "automation", "validation"], "status": "active", "version": "v1.0.0", "group_id": "71e6509d-15af-44de-be9d-3b3040f7d6ed", "severity": "high", "created_at": "2025-08-31T10:42:23.593Z", "created_by": null, "deleted_at": null, "updated_at": "2025-08-31T10:42:23.593Z", "description": "Policy group created for comprehensive end-to-end testing", "is_template": false}	**********		2025-08-31 10:42:23.602044+00	\N	\N	system	create	write	sensitive
42ed8f19-1e24-4b29-89ba-bec69f66f05f	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	11d40d3f-b0db-47c6-a33c-54fde89ab609	\N	{"name": "E2E Security Policy 1", "category": "security", "severity": "high", "is_active": true}	**********		2025-08-31 10:43:15.478467+00	\N	\N	admin	policy_creation	write	sensitive
3a368f1d-d81b-431d-aadd-ecda7301be1a	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	f0ce2942-67ea-4aae-b740-22e145fb7791	\N	{"name": "E2E Compliance Policy 1", "category": "compliance", "severity": "critical", "is_active": true}	**********		2025-08-31 10:43:22.557029+00	\N	\N	admin	policy_creation	write	sensitive
5cb4705d-ee81-414e-856e-196fb185d7d8	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	fb33044a-3bcc-4b23-9865-32131155c11c	\N	{"name": "E2E Emergency Policy 1", "category": "operational", "severity": "emergency", "is_active": false}	**********		2025-08-31 10:43:52.216947+00	\N	\N	admin	policy_creation	write	sensitive
c8a55fa8-dc10-4617-ab42-27398b3f5934	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	fb33044a-3bcc-4b23-9865-32131155c11c	{"name": "E2E Emergency Policy 1", "version": 1, "blob_url": null, "category": "operational", "severity": "emergency", "blob_path": null, "is_active": false, "policy_id": "fb33044a-3bcc-4b23-9865-32131155c11c", "rego_code": null, "created_at": "2025-08-31T10:43:52.203Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"tags": ["emergency", "operational"], "enabled": false, "priority": 100, "policy_type": "emergency", "access_control": {"max_sessions": 1, "allowed_roles": ["admin"]}, "severity_level": "emergency"}, "deleted_at": null, "updated_at": "2025-08-31T10:43:52.203Z", "updated_by": null, "description": "Emergency operational policy for testing", "policy_type": "e2e_comprehensive_test", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "E2E Emergency Policy 1", "version": 1, "blob_url": null, "category": "operational", "severity": "emergency", "blob_path": null, "is_active": true, "policy_id": "fb33044a-3bcc-4b23-9865-32131155c11c", "rego_code": null, "created_at": "2025-08-31T10:43:52.203Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"tags": ["emergency", "operational", "activated"], "enabled": true, "priority": 100, "policy_type": "emergency", "access_control": {"max_sessions": 1, "allowed_roles": ["admin"]}, "severity_level": "emergency"}, "deleted_at": null, "updated_at": "2025-08-31T10:44:13.705Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Emergency operational policy for testing", "policy_type": "e2e_comprehensive_test", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-31 10:44:13.713154+00	\N	\N	admin	policy_update	write	sensitive
1405f80f-a3e0-4ca9-80a2-d5abb84feb87	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	f0ce2942-67ea-4aae-b740-22e145fb7791	{"name": "E2E Compliance Policy 1", "version": 1, "blob_url": null, "category": "compliance", "severity": "critical", "blob_path": null, "is_active": true, "policy_id": "f0ce2942-67ea-4aae-b740-22e145fb7791", "rego_code": null, "created_at": "2025-08-31T10:43:22.553Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"tags": ["compliance", "audit", "regulatory"], "enabled": true, "priority": 95, "policy_type": "compliance", "access_control": {"max_sessions": 2, "allowed_roles": ["admin", "analyst", "manager"]}, "severity_level": "critical"}, "deleted_at": null, "updated_at": "2025-08-31T10:43:22.553Z", "updated_by": null, "description": "Compliance policy for regulatory requirements testing", "policy_type": "e2e_comprehensive_test", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "E2E Compliance Policy 1", "version": 1, "blob_url": null, "category": "compliance", "severity": "critical", "blob_path": null, "is_active": false, "policy_id": "f0ce2942-67ea-4aae-b740-22e145fb7791", "rego_code": null, "created_at": "2025-08-31T10:43:22.553Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"tags": ["compliance", "audit", "regulatory", "deactivated"], "enabled": false, "priority": 95, "policy_type": "compliance", "access_control": {"max_sessions": 2, "allowed_roles": ["admin", "analyst", "manager"]}, "severity_level": "critical"}, "deleted_at": null, "updated_at": "2025-08-31T10:44:27.846Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Compliance policy for regulatory requirements testing", "policy_type": "e2e_comprehensive_test", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-31 10:44:27.853311+00	\N	\N	admin	policy_update	write	sensitive
37e82747-ff59-4531-8136-0bfc83a97c04	00000000-0000-0000-0000-000000000000	BULK_POLICY_ASSIGN	agent	\N	\N	{"groupIds": ["71e6509d-15af-44de-be9d-3b3040f7d6ed"], "policyIds": ["11d40d3f-b0db-47c6-a33c-54fde89ab609", "f0ce2942-67ea-4aae-b740-22e145fb7791"], "targetAgentIds": ["3472aad8-c8cd-4714-84e0-63db0184a214", "2c41ebc6-08d7-40f0-94c0-1c88e6098013"]}	**********		2025-08-31 10:45:47.787055+00	\N	\N	system	bulk_policy_assign	write	sensitive
d40be8b4-8e35-440e-8b06-dc0c64f4e9e3	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	58c0b3b8-b63e-41d1-a265-4b7e8dbf3e4f	\N	{"name": "p18", "category": "e2e_comprehensive_test", "severity": "medium", "is_active": true}	**********		2025-08-31 15:20:26.850159+00	\N	\N	admin	policy_creation	write	sensitive
a20ba06e-9319-4e94-9e7b-d4e8420e1cbe	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	f6252c8e-fa21-4ed6-91e1-ccba1da4e741	\N	{"name": "p19", "category": "e2e_comprehensive_test", "severity": "medium", "is_active": true}	**********		2025-08-31 15:27:29.54649+00	\N	\N	admin	policy_creation	write	sensitive
cf844358-5d72-4632-9e08-64bc49f0462f	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	58c0b3b8-b63e-41d1-a265-4b7e8dbf3e4f	{"name": "p18", "version": 1, "blob_url": null, "category": "e2e_comprehensive_test", "severity": "medium", "blob_path": null, "is_active": true, "policy_id": "58c0b3b8-b63e-41d1-a265-4b7e8dbf3e4f", "rego_code": null, "created_at": "2025-08-31T15:20:26.837Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"tags": ["e2e"], "enabled": true, "priority": 50, "policy_type": "security", "access_control": {"max_sessions": 3, "allowed_roles": ["admin"]}, "severity_level": "emergency"}, "deleted_at": null, "updated_at": "2025-08-31T15:20:26.837Z", "updated_by": null, "description": "p18", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p18", "version": 1, "blob_url": null, "category": "e2e_comprehensive_test", "severity": "medium", "blob_path": null, "is_active": false, "policy_id": "58c0b3b8-b63e-41d1-a265-4b7e8dbf3e4f", "rego_code": null, "created_at": "2025-08-31T15:20:26.837Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"tags": ["e2e"], "enabled": false, "priority": 50, "policy_type": "security", "access_control": {"max_sessions": 3, "allowed_roles": ["admin"]}, "severity_level": "emergency"}, "deleted_at": null, "updated_at": "2025-08-31T15:29:30.916Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p18", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": [], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-08-31 15:29:30.930343+00	\N	\N	admin	policy_update	write	sensitive
ad34e6ed-769a-41c2-99c0-61b4236511f5	00000000-0000-0000-0000-000000000000	POLICY_CREATED	policy	a7516dae-bc33-4578-ae12-6ec66a721c70	\N	{"name": "p20", "category": "data_privacy", "severity": "medium", "is_active": true}	**********		2025-08-31 18:42:28.965966+00	\N	\N	admin	policy_creation	write	sensitive
153da1ed-f8b1-4f89-8a7d-4c07d13c390b	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	bd7363f8-6104-449e-873a-db0539f0fa17	{"name": "p1", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "medium", "blob_path": null, "is_active": true, "policy_id": "bd7363f8-6104-449e-873a-db0539f0fa17", "rego_code": null, "created_at": "2025-08-27T18:11:04.643Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "medium", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-08-30T19:54:11.359Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p1", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "p1", "version": 1, "blob_url": null, "category": "data_privacy", "severity": "medium", "blob_path": null, "is_active": false, "policy_id": "bd7363f8-6104-449e-873a-db0539f0fa17", "rego_code": null, "created_at": "2025-08-27T18:11:04.643Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "data_privacy", "enabled": true, "severity": "medium", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}, "deleted_at": null, "updated_at": "2025-09-01T02:36:23.019Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "p1", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["admin"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-09-01 02:36:23.035979+00	\N	\N	admin	policy_update	write	sensitive
d172a4c6-6ddc-4bed-9050-9e7282f338b9	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	00accc7f-7fd6-42cd-acbd-e9016b48eaec	{"name": "Test Database Schema Policy", "version": 1, "blob_url": null, "category": "Access Control", "severity": "medium", "blob_path": null, "is_active": null, "policy_id": "00accc7f-7fd6-42cd-acbd-e9016b48eaec", "rego_code": null, "created_at": "2025-08-27T22:17:01.522Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "enabled": true, "severity": "medium", "allowed_roles": ["admin", "user"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-27T22:17:01.522Z", "updated_by": null, "description": "Testing policy creation with database schemas", "policy_type": "access_control", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Test Database Schema Policy", "version": 1, "blob_url": null, "category": "Access Control", "severity": "medium", "blob_path": null, "is_active": true, "policy_id": "00accc7f-7fd6-42cd-acbd-e9016b48eaec", "rego_code": null, "created_at": "2025-08-27T22:17:01.522Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "enabled": true, "severity": "medium", "allowed_roles": ["admin", "user"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-09-01T04:10:43.595Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing policy creation with database schemas", "policy_type": "access_control", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-09-01 04:10:43.599313+00	\N	\N	admin	policy_update	write	sensitive
a5df2667-056e-4f33-b080-7d2a0bfc7f4b	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	3c7b0cb1-0c84-4c64-8ebb-bedc7c262e53	{"name": "Test DB Schema Policy 1756333061", "version": 1, "blob_url": null, "category": "Access Control", "severity": "medium", "blob_path": null, "is_active": null, "policy_id": "3c7b0cb1-0c84-4c64-8ebb-bedc7c262e53", "rego_code": null, "created_at": "2025-08-27T22:17:41.276Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "enabled": true, "severity": "medium", "allowed_roles": ["admin", "user"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-27T22:17:41.276Z", "updated_by": null, "description": "Testing policy creation with database schemas", "policy_type": "access_control", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Test DB Schema Policy 1756333061", "version": 1, "blob_url": null, "category": "Access Control", "severity": "medium", "blob_path": null, "is_active": true, "policy_id": "3c7b0cb1-0c84-4c64-8ebb-bedc7c262e53", "rego_code": null, "created_at": "2025-08-27T22:17:41.276Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "enabled": true, "severity": "medium", "allowed_roles": ["admin", "user"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-09-01T04:11:29.008Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing policy creation with database schemas", "policy_type": "access_control", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-09-01 04:11:29.012976+00	\N	\N	admin	policy_update	write	sensitive
770b7a44-1600-4f69-909d-b8621808ed62	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	4855b086-ab50-4488-9094-58f0be3f2cf3	{"name": "Test Medical Privacy Policy", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "high", "blob_path": null, "is_active": null, "policy_id": "4855b086-ab50-4488-9094-58f0be3f2cf3", "rego_code": null, "created_at": "2025-08-27T21:38:06.072Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "high", "allowed_roles": ["doctor", "nurse", "admin"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders", "medical_record_number"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-08-27T21:38:06.072Z", "updated_by": null, "description": "Testing complete policy lifecycle with database schemas", "policy_type": "medical_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Test Medical Privacy Policy", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "4855b086-ab50-4488-9094-58f0be3f2cf3", "rego_code": null, "created_at": "2025-08-27T21:38:06.072Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "high", "allowed_roles": ["doctor", "nurse", "admin"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders", "medical_record_number"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-09-01T04:11:33.146Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing complete policy lifecycle with database schemas", "policy_type": "medical_privacy", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-09-01 04:11:33.148796+00	\N	\N	admin	policy_update	write	sensitive
43982841-219d-4413-ab58-4b608d089d6c	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	f4c1a348-7f94-4ed5-908c-4aee04e82f37	{"name": "Test DB Schema Policy **********", "version": 1, "blob_url": null, "category": "Access Control", "severity": "medium", "blob_path": null, "is_active": null, "policy_id": "f4c1a348-7f94-4ed5-908c-4aee04e82f37", "rego_code": null, "created_at": "2025-08-27T22:18:03.518Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "enabled": true, "severity": "medium", "allowed_roles": ["admin", "user"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-08-27T22:18:03.518Z", "updated_by": null, "description": "Testing policy creation with database schemas", "policy_type": "access_control", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Test DB Schema Policy **********", "version": 1, "blob_url": null, "category": "Access Control", "severity": "medium", "blob_path": null, "is_active": true, "policy_id": "f4c1a348-7f94-4ed5-908c-4aee04e82f37", "rego_code": null, "created_at": "2025-08-27T22:18:03.518Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "enabled": true, "severity": "medium", "allowed_roles": ["admin", "user"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-09-01T04:11:38.569Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing policy creation with database schemas", "policy_type": "access_control", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-09-01 04:11:38.571695+00	\N	\N	admin	policy_update	write	sensitive
56f090e8-c99d-4ea1-b7be-0d4d70535a4f	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2	{"name": "Demo Medical Privacy Policy", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "high", "blob_path": null, "is_active": true, "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "rego_code": null, "created_at": "2025-08-28T04:27:16.865Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": true, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-08-29T22:14:23.982Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Test medical privacy policy for demo flow - UPDATED", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Demo Medical Privacy Policy", "version": 1, "blob_url": null, "category": "medical_privacy", "severity": "high", "blob_path": null, "is_active": false, "policy_id": "3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2", "rego_code": null, "created_at": "2025-08-28T04:27:16.865Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "medical_privacy", "enabled": false, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}, "deleted_at": null, "updated_at": "2025-09-01T04:11:52.514Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Test medical privacy policy for demo flow - UPDATED", "policy_type": "opa", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": ["doctor", "nurse"], "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-09-01 04:11:52.518558+00	\N	\N	admin	policy_update	write	sensitive
88d8cb5c-eb69-4090-a129-99f52c8de3a5	00000000-0000-0000-0000-000000000000	POLICY_UPDATED	policy	f4c1a348-7f94-4ed5-908c-4aee04e82f37	{"name": "Test DB Schema Policy **********", "version": 1, "blob_url": null, "category": "Access Control", "severity": "medium", "blob_path": null, "is_active": true, "policy_id": "f4c1a348-7f94-4ed5-908c-4aee04e82f37", "rego_code": null, "created_at": "2025-08-27T22:18:03.518Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "enabled": true, "severity": "medium", "allowed_roles": ["admin", "user"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-09-01T04:11:38.569Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing policy creation with database schemas", "policy_type": "access_control", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	{"name": "Test DB Schema Policy **********", "version": 1, "blob_url": null, "category": "Access Control", "severity": "medium", "blob_path": null, "is_active": false, "policy_id": "f4c1a348-7f94-4ed5-908c-4aee04e82f37", "rego_code": null, "created_at": "2025-08-27T22:18:03.518Z", "created_by": "00000000-0000-0000-0000-000000000000", "definition": {"type": "access_control", "enabled": false, "severity": "medium", "allowed_roles": ["admin", "user"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}, "deleted_at": null, "updated_at": "2025-09-01T04:11:55.191Z", "updated_by": "00000000-0000-0000-0000-000000000000", "description": "Testing policy creation with database schemas", "policy_type": "access_control", "guardrail_id": null, "rego_version": 1, "blob_container": "rego-policies", "opa_sync_status": "pending", "applies_to_roles": null, "rego_template_id": null, "original_policy_id": null, "last_rego_generation": null, "rego_generation_error": null, "cloned_from_policy_name": null}	**********		2025-09-01 04:11:55.193518+00	\N	\N	admin	policy_update	write	sensitive
83e9ef7d-9799-4f97-bb86-b7b93c9d8d19	00000000-0000-0000-0000-000000000000	DEPRECATE	policy_group	11a01996-cb91-40ef-88c1-8e71328ca7f4	\N	\N	**********		2025-09-01 04:46:00.207639+00	\N	\N	system	deprecate	write	sensitive
\.


--
-- Data for Name: mcp_chat_sessions; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.mcp_chat_sessions (session_id, user_id, status, created_at, updated_at, metadata) FROM stdin;
\.


--
-- Data for Name: chat_messages; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.chat_messages (message_id, session_id, role, content, original_content, policies_applied, is_filtered, created_at) FROM stdin;
\.


--
-- Data for Name: datasets; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.datasets (id, name, description, type, status, data, record_count, created_by, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: dataset_entries; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.dataset_entries (id, dataset_id, test_case_type, input, expected_output, context, retrieval_context, tools_called, expected_outcome, scenario, initial_context, entry_order, created_at) FROM stdin;
\.


--
-- Data for Name: documents; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.documents (document_id, title, content, document_type, metadata, file_path, is_sensitive, created_at, updated_at, created_by, updated_by) FROM stdin;
\.


--
-- Data for Name: enum_categories; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.enum_categories (category_id, name, description, policy_type, field_path, is_active, created_at, updated_at) FROM stdin;
1	Medical Roles	Roles that can access medical data	medical_privacy	allowed_roles	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
2	Medical Fields	Medical fields requiring special protection	medical_privacy	protected_fields	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
3	Data Privacy Roles	Roles that can access sensitive data	data_privacy	allowed_roles	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
4	Data Privacy Fields	Data fields requiring privacy protection	data_privacy	protected_fields	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
5	Access Control Roles	Roles for access control policies	access_control	allowed_roles	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
6	Compliance Roles	Roles for compliance policies	compliance	allowed_roles	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
7	Severity Levels	Policy severity levels	all	severity	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
8	e2e_policy_types	Policy types for E2E testing	e2e_comprehensive_test	policy_type	t	2025-08-31 10:42:10.855504+00	2025-08-31 10:42:10.855504+00
\.


--
-- Data for Name: enum_values; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.enum_values (value_id, category_id, value, display_name, description, sort_order, is_active, created_at, updated_at) FROM stdin;
1	1	doctor	Doctor	Medical doctors with full access	1	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
2	1	nurse	Nurse	Nursing staff with patient care access	2	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
3	1	admin	Administrator	System administrators	3	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
4	1	pharmacist	Pharmacist	Pharmacy staff with medication access	4	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
5	1	lab_tech	Lab Technician	Laboratory technicians	5	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
6	1	specialist	Specialist	Medical specialists	6	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
7	1	resident	Resident	Medical residents	7	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
8	2	diagnosis	Diagnosis	Patient diagnosis information	1	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
9	2	medication	Medication	Prescribed medications	2	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
10	2	lab_orders	Lab Orders	Laboratory test orders	3	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
11	2	medical_record_number	Medical Record Number	Patient record identifier	4	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
12	2	treatment_plan	Treatment Plan	Patient treatment plans	5	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
13	2	billing_info	Billing Information	Medical billing data	6	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
14	2	patient_notes	Patient Notes	Clinical notes and observations	7	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
15	2	prescriptions	Prescriptions	Medication prescriptions	8	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
16	2	vital_signs	Vital Signs	Patient vital signs	9	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
17	2	allergies	Allergies	Patient allergy information	10	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
18	2	family_history	Family History	Patient family medical history	11	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
19	2	immunizations	Immunizations	Patient immunization records	12	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
20	3	admin	Administrator	System administrators	1	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
21	3	manager	Manager	Department managers	2	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
22	3	analyst	Analyst	Data analysts	3	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
23	3	user	User	General users	4	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
24	3	viewer	Viewer	Read-only users	5	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
25	3	editor	Editor	Content editors	6	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
26	4	personal_info	Personal Information	Personal identification data	1	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
27	4	contact_info	Contact Information	Contact details	2	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
28	4	financial_data	Financial Data	Financial information	3	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
29	4	employment_data	Employment Data	Employment information	4	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
30	4	health_data	Health Data	Health-related information	5	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
31	5	admin	Administrator	System administrators	1	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
32	5	manager	Manager	Department managers	2	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
33	5	user	User	General users	3	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
34	5	guest	Guest	Temporary users	4	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
35	6	compliance_officer	Compliance Officer	Compliance monitoring staff	1	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
36	6	auditor	Auditor	Internal auditors	2	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
37	6	legal	Legal	Legal department staff	3	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
38	6	admin	Administrator	System administrators	4	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
39	7	low	Low	Low priority issues	1	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
40	7	medium	Medium	Medium priority issues	2	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
41	7	high	High	High priority issues	3	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
42	7	critical	Critical	Critical priority issues	4	t	2025-08-27 17:57:54.285266+00	2025-08-27 17:57:54.285266+00
\.


--
-- Data for Name: evaluation_metrics; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.evaluation_metrics (id, name, description, category, implementation_type, config, is_active, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: experiments; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.experiments (id, name, description, dataset_id, agent_config, execution_mode, status, progress, started_at, completed_at, created_by, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: evaluations; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.evaluations (id, evaluation_id, experiment_id, experiment_name, agent_name, dataset_name, evaluation_type, status, evaluations, summary, created_at, completed_at) FROM stdin;
\.


--
-- Data for Name: experiment_evaluations; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.experiment_evaluations (id, experiment_id, metric_id, status, score, details, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: guardrail_services; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.guardrail_services (id, service_id, name, version, type, endpoint, health_check_path, timeout_ms, capabilities, supported_content_types, status, last_health_check, metadata, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: integration_dlq; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.integration_dlq (id, tenant_id, destination, event_type, event_version, payload_json, attempts, last_error, dead_at, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: integration_outbox; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.integration_outbox (id, tenant_id, destination, event_type, event_version, payload_json, status, attempts, next_attempt_at, last_error, created_at, updated_at) FROM stdin;
18514264-c898-4ef6-9715-7beb4404ceee	\N	webhook	policy.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "3c7b0cb1-0c84-4c64-8ebb-bedc7c262e53", "resource_type": "policy"}}	pending	0	2025-09-01 04:42:28.431099+00	\N	2025-09-01 04:42:28.431099+00	2025-09-01 04:42:28.431099+00
fe02a614-8cc0-4fe1-9b0e-0476b9546216	\N	webhook	policy.deleted	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": "62124064-1825-4bfa-99be-580b1a1c625d", "resource_type": "policy"}}	pending	0	2025-09-01 04:04:05.306125+00	\N	2025-09-01 04:04:05.306125+00	2025-09-01 04:04:05.306125+00
fe7be4f2-ba3f-4bd3-b811-364bb1a6b7ea	\N	webhook	assignment.unlinked	1	{"data": {"change_type": "deleted"}, "subject": {"resource_id": {"role_id": "b1b2c3d4-e5f6-7890-abcd-444444444444", "agent_id": "00033e45-2454-42cc-b247-0c6e3d4e7e06", "group_id": "d1b2c3d4-e5f6-7890-abcd-111111111111", "policy_id": "9b3d812f-bde0-43e9-a211-5d48379f1320"}, "resource_type": "agent_role_policy"}}	pending	0	2025-09-01 04:04:05.306125+00	\N	2025-09-01 04:04:05.306125+00	2025-09-01 04:04:05.306125+00
71780871-1f7e-49d0-9d41-f83fb9f049b4	\N	webhook	policy.updated	1	{"data": {"change_type": "updated"}, "subject": {"resource_id": "3c7b0cb1-0c84-4c64-8ebb-bedc7c262e53", "resource_type": "policy"}}	pending	0	2025-09-01 04:38:21.870957+00	\N	2025-09-01 04:38:21.870957+00	2025-09-01 04:38:21.870957+00
\.


--
-- Data for Name: mcp_flow_steps; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.mcp_flow_steps (step_id, session_id, step_number, step_name, status, input_data, output_data, processing_time_ms, policies_applied, violations_detected, created_at, completed_at) FROM stdin;
\.


--
-- Data for Name: openai_api_calls; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.openai_api_calls (call_id, session_id, step_id, model_name, prompt_tokens, completion_tokens, total_tokens, cost_estimate, response_time_ms, status_code, created_at) FROM stdin;
\.


--
-- Data for Name: policy_executions; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policy_executions (execution_id, session_id, step_id, policy_id, execution_status, input_data, output_data, execution_time_ms, error_message, created_at) FROM stdin;
\.


--
-- Data for Name: policy_schemas; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policy_schemas (id, schema_name, schema_content, description, guardrail_id, is_active, created_at, updated_at, default_template, template_source) FROM stdin;
9a9f7702-998f-407a-8253-10f61d94000f	medical_privacy	{"type": "object", "title": "Medical Privacy Policy", "required": ["type", "severity", "allowed_roles", "protected_fields"], "properties": {"type": {"type": "string", "const": "medical_privacy", "description": "Policy type identifier"}, "enabled": {"type": "boolean", "default": true, "description": "Whether the policy is currently active"}, "severity": {"enum": ["low", "medium", "high", "critical"], "type": "string", "default": "medium", "description": "Policy severity level"}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "allowed_roles": {"type": "array", "items": {"enum": ["doctor", "nurse", "admin", "pharmacist", "lab_tech", "specialist", "resident"], "type": "string"}, "minItems": 1, "description": "Roles with access to medical data"}, "data_handling": {"type": "object", "properties": {"anonymization": {"type": "boolean", "default": false, "description": "Anonymize data for research purposes"}, "pseudonymization": {"type": "boolean", "default": true, "description": "Use pseudonyms for patient identification"}, "data_minimization": {"type": "boolean", "default": true, "description": "Collect only necessary data"}}}, "hipaa_compliance": {"type": "boolean", "default": true, "description": "Enforce HIPAA compliance requirements"}, "protected_fields": {"type": "array", "items": {"enum": ["diagnosis", "medication", "lab_orders", "medical_record_number", "treatment_plan", "billing_info", "patient_notes", "prescriptions", "vital_signs", "allergies", "family_history", "immunizations"], "type": "string"}, "minItems": 1, "description": "Medical fields requiring special protection"}, "audit_requirements": {"type": "object", "required": ["log_access", "retention_period"], "properties": {"log_access": {"type": "boolean", "default": true, "description": "Log all access to medical data"}, "access_timeout": {"type": "integer", "default": 30, "maximum": 60, "minimum": 5, "description": "Session timeout in minutes"}, "retention_period": {"type": "integer", "default": 7, "maximum": 10, "minimum": 1, "description": "Audit log retention period in years"}, "encryption_required": {"type": "boolean", "default": true, "description": "Require encryption for medical data"}}}}, "description": "Template for HIPAA-compliant medical privacy policies", "additionalProperties": false}	Template for HIPAA-compliant medical privacy policies	\N	t	2025-08-27 21:19:34.653695+00	2025-08-29 12:55:30.994116+00	{"type": "medical_privacy", "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan", "nn_test"]}	migrated_legacy
f8870964-9455-4164-8a81-ba811fffdc98	access_control	{"type": "object", "title": "Access Control Policy", "required": ["type", "severity", "allowed_roles"], "properties": {"type": {"type": "string", "const": "access_control", "description": "Policy type identifier"}, "enabled": {"type": "boolean", "default": true, "description": "Whether the policy is currently active"}, "severity": {"enum": ["low", "medium", "high", "critical"], "type": "string", "default": "medium", "description": "Policy severity level"}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "ip_whitelist": {"type": "array", "items": {"type": "string", "pattern": "^(?:[0-9]{1,3}\\\\.){3}[0-9]{1,3}(?:/[0-9]{1,2})?$"}, "description": "Allowed IP addresses or CIDR ranges"}, "allowed_roles": {"type": "array", "items": {"enum": ["admin", "manager", "user", "viewer", "editor", "guest", "moderator"], "type": "string"}, "minItems": 1, "description": "Roles with access to the resource"}, "time_restrictions": {"type": "object", "required": ["start_time", "end_time"], "properties": {"end_time": {"type": "string", "default": "17:00", "pattern": "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", "description": "End time for access (24-hour format)"}, "timezone": {"type": "string", "default": "UTC", "description": "Timezone for time restrictions"}, "start_time": {"type": "string", "default": "09:00", "pattern": "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", "description": "Start time for access (24-hour format)"}, "allowed_days": {"type": "array", "items": {"enum": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"], "type": "string"}, "default": ["monday", "tuesday", "wednesday", "thursday", "friday"], "description": "Days when access is allowed"}}}, "restricted_actions": {"type": "array", "items": {"enum": ["create", "read", "update", "delete", "export", "import", "share", "print"], "type": "string"}, "description": "Actions that are restricted for this resource"}, "session_management": {"type": "object", "required": ["max_session_duration", "inactivity_timeout"], "properties": {"inactivity_timeout": {"type": "integer", "default": 15, "maximum": 60, "minimum": 1, "description": "Inactivity timeout in minutes"}, "concurrent_sessions": {"type": "integer", "default": 3, "maximum": 10, "minimum": 1, "description": "Maximum concurrent sessions per user"}, "max_session_duration": {"type": "integer", "default": 60, "maximum": 480, "minimum": 5, "description": "Maximum session duration in minutes"}}}}, "description": "Template for role-based access control policies", "additionalProperties": false}	Template for role-based access control policies	\N	t	2025-08-27 21:19:34.681926+00	2025-08-29 12:01:08.784361+00	{"type": "access_control", "severity": "high", "ip_whitelist": ["***********/24"], "allowed_roles": ["admin", "manager"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00"}, "restricted_actions": ["delete", "export"]}	migrated_legacy
f7f9e2a0-f912-4c8c-a078-6545ed80513c	data_privacy	{"type": "object", "title": "Data Privacy Policy", "required": ["type", "severity", "allowed_roles", "data_classification", "protected_fields"], "properties": {"type": {"type": "string", "const": "data_privacy", "description": "Policy type identifier"}, "enabled": {"type": "boolean", "default": true, "description": "Whether the policy is currently active"}, "severity": {"enum": ["low", "medium", "high", "critical"], "type": "string", "default": "medium", "description": "Policy severity level"}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "allowed_roles": {"type": "array", "items": {"enum": ["admin", "manager", "analyst", "user", "viewer", "editor"], "type": "string"}, "minItems": 1, "description": "Roles with access to sensitive data"}, "data_retention": {"type": "object", "required": ["retention_period"], "properties": {"archive_after": {"type": "integer", "default": 12, "maximum": 60, "minimum": 1, "description": "Archive data after this many months"}, "auto_deletion": {"type": "boolean", "default": true, "description": "Automatically delete expired data"}, "retention_period": {"type": "integer", "default": 24, "maximum": 120, "minimum": 1, "description": "Data retention period in months"}}}, "protected_fields": {"type": "array", "items": {"enum": ["personal_info", "financial_data", "contact_details", "identification", "preferences", "behavioral_data", "location_data", "biometric_data"], "type": "string"}, "minItems": 1, "description": "Data fields requiring protection"}, "data_classification": {"enum": ["public", "internal", "confidential", "restricted", "secret"], "type": "string", "default": "confidential", "description": "Classification level of the data"}, "consent_requirements": {"type": "object", "required": ["explicit_consent"], "properties": {"consent_expiry": {"type": "integer", "default": 12, "maximum": 60, "minimum": 1, "description": "Consent validity period in months"}, "explicit_consent": {"type": "boolean", "default": true, "description": "Require explicit user consent"}, "withdrawal_allowed": {"type": "boolean", "default": true, "description": "Allow users to withdraw consent"}}}}, "description": "Template for general data privacy and protection policies", "additionalProperties": false}	Template for general data privacy and protection policies	\N	t	2025-08-27 21:19:34.667795+00	2025-08-29 12:01:08.784361+00	{"type": "data_privacy", "enabled": true, "severity": "medium", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}	auto_generated
328e67f3-eac3-4eb6-9f75-ae1bda8455ac	compliance	{"type": "object", "title": "Compliance Policy", "required": ["type", "severity", "allowed_roles", "regulatory_framework", "compliance_requirements"], "properties": {"type": {"type": "string", "const": "compliance", "description": "Policy type identifier"}, "enabled": {"type": "boolean", "default": true, "description": "Whether the policy is currently active"}, "severity": {"enum": ["low", "medium", "high", "critical"], "type": "string", "default": "medium", "description": "Policy severity level"}, "penalties": {"type": "object", "properties": {"monetary_fines": {"type": "boolean", "default": true, "description": "Apply monetary fines for violations"}, "max_fine_amount": {"type": "number", "minimum": 0, "description": "Maximum fine amount in currency units"}, "suspension_period": {"type": "integer", "maximum": 365, "minimum": 1, "description": "Account suspension period in days"}}}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "allowed_roles": {"type": "array", "items": {"enum": ["compliance_officer", "auditor", "admin", "manager", "supervisor"], "type": "string"}, "minItems": 1, "description": "Roles responsible for compliance"}, "audit_frequency": {"enum": ["monthly", "quarterly", "semi_annually", "annually"], "type": "string", "default": "quarterly", "description": "Frequency of compliance audits"}, "regulatory_framework": {"enum": ["gdpr", "ccpa", "sox", "hipaa", "pci_dss", "iso27001", "ferpa"], "type": "string", "description": "Regulatory framework this policy enforces"}, "reporting_requirements": {"type": "object", "required": ["incident_reporting", "reporting_timeframe"], "properties": {"incident_reporting": {"type": "boolean", "default": true, "description": "Require incident reporting"}, "reporting_timeframe": {"type": "integer", "default": 24, "maximum": 72, "minimum": 1, "description": "Hours to report incidents"}, "regulatory_notifications": {"type": "boolean", "default": true, "description": "Notify regulatory bodies of incidents"}}}, "compliance_requirements": {"type": "array", "items": {"enum": ["data_encryption", "access_logging", "audit_trails", "consent_management", "data_minimization", "right_to_forget", "breach_notification", "vendor_management"], "type": "string"}, "minItems": 1, "description": "Specific compliance requirements to enforce"}}, "description": "Template for regulatory compliance policies", "additionalProperties": false}	Template for regulatory compliance policies	\N	t	2025-08-27 21:19:34.692956+00	2025-08-29 17:58:54.786951+00	{"type": "compliance", "enabled": true, "severity": "medium", "penalties": {"monetary_fines": true}, "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}	auto_generated
\.


--
-- Data for Name: policy_templates; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policy_templates (template_id, name, description, category, template_definition, is_system_template, created_at, created_by, guardrail_id) FROM stdin;
15ad737f-41fe-4eaf-966c-3115d5322821	PII Masking Template	Template for masking personally identifiable information	data_masking	{"type": "data_masking", "severity": "critical", "mask_format": "XXX-XX-{last4}", "field_patterns": ["ssn", "social_security_number", "phone", "email"], "regex_patterns": ["\\\\\\\\b\\\\\\\\d{3}-\\\\\\\\d{2}-\\\\\\\\d{4}\\\\\\\\b", "\\\\\\\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\\\\\\\.[A-Z|a-z]{2,}\\\\\\\\b"]}	t	2025-08-27 17:57:54.326132+00	\N	\N
9e14d93e-4b8a-4b95-8187-bc8a96e26a4c	Phone Number Masking Template	Template for masking phone number fields	data_masking	{"type": "data_masking", "severity": "high", "mask_format": "XXX-XX-{last4}", "field_patterns": ["phone", "phone_number"], "regex_patterns": ["\\\\\\\\b\\\\\\\\d{3}-\\\\\\\\d{2}-\\\\\\\\d{4}\\\\\\\\b"]}	t	2025-08-27 17:57:54.326132+00	\N	\N
4e0dd1de-7a23-4fa7-860c-a4e2db5f12c9	Content Safety Template	Template for detecting harmful or inappropriate content	content_safety	{"type": "content_filtering", "action": "block_and_log", "severity": "high", "blocked_categories": ["hate_speech", "violence", "self_harm", "sexual"], "confidence_threshold": 0.7}	t	2025-08-27 17:57:54.326132+00	\N	\N
f5c4f2d9-6baa-4499-a20d-d7993a9ffe85	Medical Info Protection Template	Template for protecting sensitive medical information	medical_privacy	{"type": "medical_privacy", "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"]}	t	2025-08-27 17:57:54.326132+00	\N	\N
a3d39f57-2e2c-44de-9461-8043534ef287	Data Encryption Template	Template for encrypting sensitive data fields	data_encryption	{"type": "data_encryption", "severity": "critical", "fields_to_encrypt": ["credit_card", "bank_account", "ssn"], "key_rotation_days": 90, "encryption_algorithm": "AES-256"}	t	2025-08-27 17:57:54.326132+00	\N	\N
83736ee5-e550-4ea6-958b-ff9083fe0359	Access Logging Template	Template for comprehensive access logging	access_logging	{"type": "access_logging", "severity": "high", "log_level": "detailed", "log_fields": ["user_id", "timestamp", "resource", "action", "ip_address"], "retention_days": 2555, "hipaa_compliant": true}	t	2025-08-27 17:57:54.326132+00	\N	\N
01048e08-5df5-4a19-b3ce-20eaade78ddd	Data Retention Template	Template for data retention policies	data_retention	{"type": "data_retention", "severity": "critical", "data_types": ["patient_records", "medical_history", "billing_info"], "auto_deletion": true, "backup_retention_days": 365, "retention_period_days": 2555}	t	2025-08-27 17:57:54.326132+00	\N	\N
aaff4aed-e41d-4832-8b9e-2c45f631c902	Access Control Template	Template for role-based access control	access_control	{"type": "access_control", "severity": "high", "ip_whitelist": ["***********/24"], "allowed_roles": ["admin", "manager"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00"}, "restricted_actions": ["delete", "export"]}	t	2025-08-27 17:57:54.326132+00	\N	\N
9326875a-2a8d-4a7f-84cc-86995c8f4802	Compliance Template	Template for regulatory compliance policies	compliance	{"type": "compliance", "severity": "critical", "regulations": ["HIPAA", "GDPR", "SOX"], "penalty_severity": "high", "audit_requirements": true, "documentation_required": true}	t	2025-08-27 17:57:54.326132+00	\N	\N
e1b2c3d4-e5f6-7890-abcd-111111111111	HIPAA PHI Access Control Template	Template for controlling access to Protected Health Information	HIPAA Privacy Compliance	{"parameters": {"audit_redaction": {"type": "boolean", "default": true}, "redaction_rules": {"type": "object", "default": {"phone": "({area_code}) ***-****"}}, "redaction_fields": {"type": "array", "default": ["phone"]}, "emergency_override": {"type": "boolean", "default": true}, "redaction_required": {"type": "boolean", "default": true}, "external_sharing_allowed": {"type": "boolean", "default": false}, "sharing_authorized_roles": {"type": "array", "default": ["HIPAA_CLINICAL_REVIEWER", "HIPAA_MEDICAL_DIRECTOR"]}}}	f	2025-08-27 17:57:54.32967+00	a1b2c3d4-e5f6-7890-abcd-111111111111	\N
e1b2c3d4-e5f6-7890-abcd-222222222222	HIPAA Medical Record Sharing Template	Template for sharing medical records with PHI redaction capabilities	HIPAA Privacy Compliance	{"parameters": {"visibility_rules": {"type": "object", "default": {"HIPAA_CASE_MANAGER": ["contact_info", "insurance_info", "email"], "HIPAA_MEDICAL_DIRECTOR": ["all_fields"], "HIPAA_CLINICAL_REVIEWER": ["medical_info", "basic_demographics", "email"], "HIPAA_COMPLIANCE_OFFICER": ["access_logs", "audit_trails", "email"]}}, "minimum_data_only": {"type": "boolean", "default": true}, "redaction_methods": {"type": "object", "default": {"email": "****@****.com"}}, "purpose_limitation": {"type": "boolean", "default": true}, "pii_fields_to_redact": {"type": "array", "default": ["email"]}, "role_based_visibility": {"type": "boolean", "default": true}, "auto_redaction_enabled": {"type": "boolean", "default": true}}}	f	2025-08-27 17:57:54.32967+00	a1b2c3d4-e5f6-7890-abcd-111111111111	\N
e1b2c3d4-e5f6-7890-abcd-333333333333	HIPAA Patient Consent Management Template	Template for managing patient consent and authorization	HIPAA Privacy Compliance	{"parameters": {"visibility_rules": {"type": "object", "default": {"HIPAA_CASE_MANAGER": ["contact_info", "insurance_info", "email", "phone", "address", "dob", "insurance_id"], "HIPAA_MEDICAL_DIRECTOR": ["all_fields"], "HIPAA_CLINICAL_REVIEWER": ["medical_info", "basic_demographics", "email", "phone", "address", "dob", "insurance_id"], "HIPAA_COMPLIANCE_OFFICER": ["access_logs", "audit_trails", "email", "phone", "address", "dob", "insurance_id"]}}, "minimum_data_only": {"type": "boolean", "default": true}, "redaction_methods": {"type": "object", "default": {"dob": "{month}/{day}/****", "ssn": "***-**-{last_4}", "email": "****@****.com", "phone": "({area_code}) ***-****", "address": "{city}, {state} {zip}", "insurance_id": "{first_3}*****"}}, "purpose_limitation": {"type": "boolean", "default": true}, "pii_fields_to_redact": {"type": "array", "default": ["ssn", "address", "phone", "email", "dob", "insurance_id"]}, "role_based_visibility": {"type": "boolean", "default": true}, "auto_redaction_enabled": {"type": "boolean", "default": true}}}	f	2025-08-27 17:57:54.32967+00	a1b2c3d4-e5f6-7890-abcd-111111111111	\N
\.


--
-- Data for Name: policy_violations; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.policy_violations (violation_id, policy_id, user_id, violation_type, details, severity, resolved, created_at, resolved_at, resolved_by) FROM stdin;
\.


--
-- Data for Name: rego_templates; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.rego_templates (template_id, name, description, policy_category, template_content, variables, is_active, created_at, updated_at, created_by) FROM stdin;
\.


--
-- Data for Name: system_metrics; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.system_metrics (metric_id, metric_type, metric_name, metric_value, dimensions, created_at) FROM stdin;
\.


--
-- Data for Name: test_results; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.test_results (id, experiment_id, dataset_entry_id, test_case_type, input, expected_output, actual_output, context, retrieval_context, tools_called, expected_outcome, scenario, status, score, latency_ms, token_count, metadata, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: user_roles; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.user_roles (user_id, role_id) FROM stdin;
a1b2c3d4-e5f6-7890-abcd-111111111111	b1b2c3d4-e5f6-7890-abcd-**********55
a1b2c3d4-e5f6-7890-abcd-222222222222	b1b2c3d4-e5f6-7890-abcd-333333333333
a1b2c3d4-e5f6-7890-abcd-222222222222	b1b2c3d4-e5f6-7890-abcd-222222222222
a1b2c3d4-e5f6-7890-abcd-333333333333	b1b2c3d4-e5f6-7890-abcd-111111111111
************************************	b1b2c3d4-e5f6-7890-abcd-222222222222
a1b2c3d4-e5f6-7890-abcd-**********55	b1b2c3d4-e5f6-7890-abcd-444444444444
\.


--
-- Name: enum_categories_category_id_seq; Type: SEQUENCE SET; Schema: public; Owner: dbadmin
--

SELECT pg_catalog.setval('public.enum_categories_category_id_seq', 8, true);


--
-- Name: enum_values_value_id_seq; Type: SEQUENCE SET; Schema: public; Owner: dbadmin
--

SELECT pg_catalog.setval('public.enum_values_value_id_seq', 42, true);


--
-- Name: evaluations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: dbadmin
--

SELECT pg_catalog.setval('public.evaluations_id_seq', 1, false);


--
-- PostgreSQL database dump complete
--

\unrestrict 8ln3blCnfbFGB8aoquWfZr631LyOekyNcRyZQ1UOu5VGo8rHbmZ0wky15vxcinT

