--
-- PostgreSQL database dump
--

\restrict zhCwBKdxurd5eTTxihn9a8fbGaWJjbe427z3NhOZgDz6VNNfXcjlenBS2Yb87ut

-- Dumped from database version 15.14
-- Dumped by pg_dump version 15.14

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: access_level_enum; Type: TYPE; Schema: public; Owner: dbadmin
--

CREATE TYPE public.access_level_enum AS ENUM (
    'view',
    'manage'
);


ALTER TYPE public.access_level_enum OWNER TO dbadmin;

--
-- Name: agent_status_enum; Type: TYPE; Schema: public; Owner: dbadmin
--

CREATE TYPE public.agent_status_enum AS ENUM (
    'active',
    'pending',
    'maintenance',
    'deprecated'
);


ALTER TYPE public.agent_status_enum OWNER TO dbadmin;

--
-- Name: lifecycle_status_enum; Type: TYPE; Schema: public; Owner: dbadmin
--

CREATE TYPE public.lifecycle_status_enum AS ENUM (
    'draft',
    'active',
    'deprecated'
);


ALTER TYPE public.lifecycle_status_enum OWNER TO dbadmin;

--
-- Name: link_type_enum; Type: TYPE; Schema: public; Owner: dbadmin
--

CREATE TYPE public.link_type_enum AS ENUM (
    'direct',
    'via_group'
);


ALTER TYPE public.link_type_enum OWNER TO dbadmin;

--
-- Name: severity_level_enum; Type: TYPE; Schema: public; Owner: dbadmin
--

CREATE TYPE public.severity_level_enum AS ENUM (
    'low',
    'medium',
    'high',
    'critical'
);


ALTER TYPE public.severity_level_enum OWNER TO dbadmin;

--
-- Name: user_status_enum; Type: TYPE; Schema: public; Owner: dbadmin
--

CREATE TYPE public.user_status_enum AS ENUM (
    'active',
    'suspended',
    'pending'
);


ALTER TYPE public.user_status_enum OWNER TO dbadmin;

--
-- Name: clone_policy(uuid, character varying, text, uuid); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.clone_policy(p_original_policy_id uuid, p_new_name character varying, p_new_description text, p_cloned_by_user_id uuid) RETURNS uuid
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_new_policy_id UUID;
    v_original_policy RECORD;
BEGIN
    -- Get original policy data
    SELECT * INTO v_original_policy
    FROM policies
    WHERE policy_id = p_original_policy_id
    AND deleted_at IS NULL;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Original policy not found or deleted';
    END IF;
    
    -- Create new policy as clone
    INSERT INTO policies (
        name, description, category, policy_type, definition, version,
        is_active, severity, applies_to_roles, created_by, updated_by,
        original_policy_id, cloned_from_policy_name
    ) VALUES (
        p_new_name, p_new_description, v_original_policy.category,
        v_original_policy.policy_type, v_original_policy.definition, 1,
        false, v_original_policy.severity, v_original_policy.applies_to_roles,
        p_cloned_by_user_id, p_cloned_by_user_id,
        p_original_policy_id, v_original_policy.name
    ) RETURNING policy_id INTO v_new_policy_id;
    
    -- Log the cloning event
    PERFORM log_hipaa_audit_event(
        p_cloned_by_user_id,
        'POLICY_CLONED',
        'policy',
        v_new_policy_id,
        jsonb_build_object('original_policy_id', p_original_policy_id, 'original_policy_name', v_original_policy.name),
        jsonb_build_object('new_policy_name', p_new_name, 'new_policy_id', v_new_policy_id),
        NULL, NULL, 'admin', 'policy_cloning', 'write', 'sensitive'
    );
    
    RETURN v_new_policy_id;
END;
$$;


ALTER FUNCTION public.clone_policy(p_original_policy_id uuid, p_new_name character varying, p_new_description text, p_cloned_by_user_id uuid) OWNER TO dbadmin;

--
-- Name: generate_rego_for_policy(uuid); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.generate_rego_for_policy(policy_uuid uuid) RETURNS json
    LANGUAGE plpgsql
    AS $$
DECLARE
    policy_record policies%ROWTYPE;
    template_record rego_templates%ROWTYPE;
    generated_rego TEXT;
    blob_path_var VARCHAR(500);
    result JSON;
BEGIN
    -- Get policy details
    SELECT * INTO policy_record FROM policies WHERE policy_id = policy_uuid;
    IF NOT FOUND THEN
        RETURN json_build_object('success', false, 'error', 'Policy not found');
    END IF;
    -- Get template based on policy category
    SELECT * INTO template_record FROM rego_templates WHERE policy_category = policy_record.category AND is_active = true LIMIT 1;
    IF NOT FOUND THEN
        RETURN json_build_object('success', false, 'error', 'Template not found');
    END IF;
    -- Increment version before generating Rego
    UPDATE policies SET version = version + 1 WHERE policy_id = policy_uuid RETURNING * INTO policy_record;
    -- Simulate template rendering (actual implementation should use server-side code)
    generated_rego := template_record.template_content;
    blob_path_var := CONCAT('active/policy_', policy_record.policy_id, '_', policy_record.category, '_v', policy_record.version, '.rego');
    -- Update policy with generated Rego
    UPDATE policies SET rego_code = generated_rego, blob_path = blob_path_var, rego_template_id = template_record.template_id, last_rego_generation = CURRENT_TIMESTAMP, opa_sync_status = 'generated', rego_version = policy_record.version WHERE policy_id = policy_uuid;
    -- Log operation
    --PERFORM log_rego_operation('00000000-0000-0000-0000-000000000000', json_build_object('policy_id', policy_uuid, 'action', 'generate_rego', 'status', 'success'));
    RETURN json_build_object('success', true, 'rego_preview', generated_rego, 'blob_path', blob_path_var);
END;
$$;


ALTER FUNCTION public.generate_rego_for_policy(policy_uuid uuid) OWNER TO dbadmin;

--
-- Name: get_enum_fields_for_policy_type(character varying); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.get_enum_fields_for_policy_type(p_policy_type character varying) RETURNS TABLE(field_path character varying, category_name character varying, description text)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT ec.field_path, ec.name, ec.description
    FROM enum_categories ec
    WHERE ec.policy_type = p_policy_type
    AND ec.is_active = true
    ORDER BY ec.name;
END;
$$;


ALTER FUNCTION public.get_enum_fields_for_policy_type(p_policy_type character varying) OWNER TO dbadmin;

--
-- Name: get_enum_values(character varying, character varying); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.get_enum_values(p_policy_type character varying, p_field_path character varying) RETURNS TABLE(value character varying, display_name character varying, description text)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT ev.value, ev.display_name, ev.description
    FROM enum_values ev
    JOIN enum_categories ec ON ev.category_id = ec.category_id
    WHERE ec.policy_type = p_policy_type 
    AND ec.field_path = p_field_path
    AND ec.is_active = true 
    AND ev.is_active = true
    ORDER BY ev.sort_order, ev.value;
END;
$$;


ALTER FUNCTION public.get_enum_values(p_policy_type character varying, p_field_path character varying) OWNER TO dbadmin;

--
-- Name: get_policy_template_by_category(character varying); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.get_policy_template_by_category(p_category character varying) RETURNS TABLE(template_id uuid, name character varying, description text, template_definition jsonb)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT pt.template_id, pt.name, pt.description, pt.template_definition
    FROM policy_templates pt
    WHERE pt.category = p_category
    AND pt.is_system_template = true
    ORDER BY pt.created_at DESC
    LIMIT 1;
END;
$$;


ALTER FUNCTION public.get_policy_template_by_category(p_category character varying) OWNER TO dbadmin;

--
-- Name: integration_enqueue_event(text, jsonb); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.integration_enqueue_event(p_event_type text, p_payload jsonb) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  INSERT INTO integration_outbox (event_type, payload_json, status, attempts, next_attempt_at)
  VALUES (p_event_type, p_payload, 'pending', 0, CURRENT_TIMESTAMP);
END;
$$;


ALTER FUNCTION public.integration_enqueue_event(p_event_type text, p_payload jsonb) OWNER TO dbadmin;

--
-- Name: log_hipaa_audit_event(uuid, character varying, character varying, uuid, jsonb, jsonb, character varying, character varying, character varying, character varying, character varying, character varying); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.log_hipaa_audit_event(p_user_id uuid, p_action character varying, p_resource_type character varying, p_resource_id uuid, p_old_values jsonb, p_new_values jsonb, p_session_id character varying, p_request_id character varying, p_user_role character varying, p_resource_name character varying, p_access_level character varying, p_data_classification character varying) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO audit_log (
        user_id, action, resource_type, resource_id, old_values, new_values,
        ip_address, user_agent, timestamp, session_id, request_id, user_role,
        resource_name, access_level, data_classification
    ) VALUES (
        p_user_id, p_action, p_resource_type, p_resource_id, p_old_values, p_new_values,
        inet_client_addr(), current_setting('application_name', true), CURRENT_TIMESTAMP,
        p_session_id, p_request_id, p_user_role, p_resource_name, p_access_level, p_data_classification
    );
END;
$$;


ALTER FUNCTION public.log_hipaa_audit_event(p_user_id uuid, p_action character varying, p_resource_type character varying, p_resource_id uuid, p_old_values jsonb, p_new_values jsonb, p_session_id character varying, p_request_id character varying, p_user_role character varying, p_resource_name character varying, p_access_level character varying, p_data_classification character varying) OWNER TO dbadmin;

--
-- Name: log_rego_operation(uuid, jsonb); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.log_rego_operation(user_uuid uuid, operation_details jsonb) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO audit_log (user_id, action, resource_type, resource_id, new_values, timestamp)
    VALUES (user_uuid, 'policy_rego', 'policy_rego', operation_details->>'policy_id', operation_details, CURRENT_TIMESTAMP);
END;
$$;


ALTER FUNCTION public.log_rego_operation(user_uuid uuid, operation_details jsonb) OWNER TO dbadmin;

--
-- Name: rollback_rego_generation(uuid); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.rollback_rego_generation(policy_uuid uuid) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
BEGIN
    UPDATE policies SET rego_code = NULL, blob_path = NULL, rego_template_id = NULL, last_rego_generation = NULL, opa_sync_status = 'pending', rego_generation_error = NULL WHERE policy_id = policy_uuid;
    PERFORM log_rego_operation('00000000-0000-0000-0000-000000000000', json_build_object('policy_id', policy_uuid, 'action', 'rollback_rego', 'status', 'success'));
    RETURN FOUND;
END;
$$;


ALTER FUNCTION public.rollback_rego_generation(policy_uuid uuid) OWNER TO dbadmin;

--
-- Name: trg_agents_enqueue(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.trg_agents_enqueue() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'agent.created';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','agent','resource_id', NEW.agent_id),
      'data', jsonb_build_object('change_type','created')
    );
  ELSIF TG_OP = 'UPDATE' THEN
    v_event := 'agent.updated';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','agent','resource_id', NEW.agent_id),
      'data', jsonb_build_object('change_type','updated')
    );
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'agent.deleted';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','agent','resource_id', OLD.agent_id),
      'data', jsonb_build_object('change_type','deleted')
    );
  END IF;
  PERFORM integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$;


ALTER FUNCTION public.trg_agents_enqueue() OWNER TO dbadmin;

--
-- Name: trg_arp_enqueue(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.trg_arp_enqueue() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'assignment.linked';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','agent_role_policy','resource_id', jsonb_build_object('agent_id', NEW.agent_id, 'role_id', NEW.role_id, 'group_id', NEW.group_id, 'policy_id', NEW.policy_id)), 'data', jsonb_build_object('change_type','created'));
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'assignment.unlinked';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','agent_role_policy','resource_id', jsonb_build_object('agent_id', OLD.agent_id, 'role_id', OLD.role_id, 'group_id', OLD.group_id, 'policy_id', OLD.policy_id)), 'data', jsonb_build_object('change_type','deleted'));
  ELSE
    RETURN COALESCE(NEW, OLD);
  END IF;
  PERFORM integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$;


ALTER FUNCTION public.trg_arp_enqueue() OWNER TO dbadmin;

--
-- Name: trg_policies_enqueue(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.trg_policies_enqueue() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'policy.created';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','policy','resource_id', NEW.policy_id), 'data', jsonb_build_object('change_type','created'));
  ELSIF TG_OP = 'UPDATE' THEN
    v_event := 'policy.updated';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','policy','resource_id', NEW.policy_id), 'data', jsonb_build_object('change_type','updated'));
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'policy.deleted';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','policy','resource_id', OLD.policy_id), 'data', jsonb_build_object('change_type','deleted'));
  END IF;
  PERFORM integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$;


ALTER FUNCTION public.trg_policies_enqueue() OWNER TO dbadmin;

--
-- Name: trg_roles_enqueue(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.trg_roles_enqueue() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'role.created';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','role','resource_id', NEW.role_id), 'data', jsonb_build_object('change_type','created'));
  ELSIF TG_OP = 'UPDATE' THEN
    v_event := 'role.updated';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','role','resource_id', NEW.role_id), 'data', jsonb_build_object('change_type','updated'));
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'role.deleted';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','role','resource_id', OLD.role_id), 'data', jsonb_build_object('change_type','deleted'));
  END IF;
  PERFORM integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$;


ALTER FUNCTION public.trg_roles_enqueue() OWNER TO dbadmin;

--
-- Name: update_enum_categories_updated_at(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.update_enum_categories_updated_at() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_enum_categories_updated_at() OWNER TO dbadmin;

--
-- Name: update_enum_values_updated_at(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.update_enum_values_updated_at() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_enum_values_updated_at() OWNER TO dbadmin;

--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_updated_at_column() OWNER TO dbadmin;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: agent_access; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.agent_access (
    agent_id uuid NOT NULL,
    role_id uuid NOT NULL,
    access_level public.access_level_enum DEFAULT 'view'::public.access_level_enum,
    granted_by uuid
);


ALTER TABLE public.agent_access OWNER TO dbadmin;

--
-- Name: agent_policies; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.agent_policies (
    agent_id uuid NOT NULL,
    policy_id uuid NOT NULL,
    link_type public.link_type_enum NOT NULL
);


ALTER TABLE public.agent_policies OWNER TO dbadmin;

--
-- Name: agent_role_policies; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.agent_role_policies (
    agent_id uuid NOT NULL,
    role_id uuid NOT NULL,
    group_id uuid NOT NULL,
    policy_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.agent_role_policies OWNER TO dbadmin;

--
-- Name: agents; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.agents (
    agent_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    agent_type character varying(100) DEFAULT 'policy_engine'::character varying,
    endpoint_url character varying(500),
    is_active boolean DEFAULT true,
    configuration jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    vendor character varying(255),
    department character varying(255),
    risk_score numeric(4,2) DEFAULT 0.0,
    status public.agent_status_enum DEFAULT 'active'::public.agent_status_enum,
    deleted_at timestamp with time zone
);


ALTER TABLE public.agents OWNER TO dbadmin;

--
-- Name: alembic_version; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.alembic_version (
    version_num character varying(32) NOT NULL
);


ALTER TABLE public.alembic_version OWNER TO dbadmin;

--
-- Name: audit_log; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.audit_log (
    log_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid,
    action character varying(100) NOT NULL,
    resource_type character varying(100),
    resource_id uuid,
    old_values jsonb,
    new_values jsonb,
    ip_address inet,
    user_agent text,
    "timestamp" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    session_id character varying(255),
    request_id character varying(255),
    user_role character varying(50),
    resource_name character varying(255),
    access_level character varying(50),
    data_classification character varying(50)
);


ALTER TABLE public.audit_log OWNER TO dbadmin;

--
-- Name: chat_messages; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.chat_messages (
    message_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    session_id uuid,
    role character varying(20) NOT NULL,
    content text NOT NULL,
    original_content text,
    policies_applied uuid[] DEFAULT '{}'::uuid[],
    is_filtered boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.chat_messages OWNER TO dbadmin;

--
-- Name: dataset_entries; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.dataset_entries (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    dataset_id uuid,
    test_case_type character varying(20) NOT NULL,
    input jsonb NOT NULL,
    expected_output text,
    context text,
    retrieval_context text[],
    tools_called text[],
    expected_outcome character varying(50),
    scenario text,
    initial_context text,
    entry_order integer DEFAULT 0 NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.dataset_entries OWNER TO dbadmin;

--
-- Name: datasets; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.datasets (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    type character varying(50) DEFAULT 'custom'::character varying NOT NULL,
    status character varying(20) DEFAULT 'active'::character varying NOT NULL,
    data jsonb DEFAULT '[]'::jsonb NOT NULL,
    record_count integer DEFAULT 0 NOT NULL,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.datasets OWNER TO dbadmin;

--
-- Name: documents; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.documents (
    document_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    title character varying(500) NOT NULL,
    content text,
    document_type character varying(100),
    metadata jsonb,
    file_path character varying(1000),
    is_sensitive boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    updated_by uuid
);


ALTER TABLE public.documents OWNER TO dbadmin;

--
-- Name: enum_categories; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.enum_categories (
    category_id integer NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    policy_type character varying(50) NOT NULL,
    field_path character varying(200) NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.enum_categories OWNER TO dbadmin;

--
-- Name: enum_categories_category_id_seq; Type: SEQUENCE; Schema: public; Owner: dbadmin
--

CREATE SEQUENCE public.enum_categories_category_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.enum_categories_category_id_seq OWNER TO dbadmin;

--
-- Name: enum_categories_category_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dbadmin
--

ALTER SEQUENCE public.enum_categories_category_id_seq OWNED BY public.enum_categories.category_id;


--
-- Name: enum_values; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.enum_values (
    value_id integer NOT NULL,
    category_id integer,
    value character varying(200) NOT NULL,
    display_name character varying(200),
    description text,
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.enum_values OWNER TO dbadmin;

--
-- Name: enum_values_value_id_seq; Type: SEQUENCE; Schema: public; Owner: dbadmin
--

CREATE SEQUENCE public.enum_values_value_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.enum_values_value_id_seq OWNER TO dbadmin;

--
-- Name: enum_values_value_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dbadmin
--

ALTER SEQUENCE public.enum_values_value_id_seq OWNED BY public.enum_values.value_id;


--
-- Name: evaluation_metrics; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.evaluation_metrics (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    category character varying(50),
    implementation_type character varying(50),
    config jsonb,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.evaluation_metrics OWNER TO dbadmin;

--
-- Name: evaluations; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.evaluations (
    id integer NOT NULL,
    evaluation_id character varying(255) NOT NULL,
    experiment_id uuid NOT NULL,
    experiment_name character varying(255),
    agent_name character varying(255),
    dataset_name character varying(255),
    evaluation_type character varying(50),
    status character varying(50) DEFAULT 'pending'::character varying,
    evaluations jsonb,
    summary jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    completed_at timestamp without time zone
);


ALTER TABLE public.evaluations OWNER TO dbadmin;

--
-- Name: evaluations_id_seq; Type: SEQUENCE; Schema: public; Owner: dbadmin
--

CREATE SEQUENCE public.evaluations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.evaluations_id_seq OWNER TO dbadmin;

--
-- Name: evaluations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dbadmin
--

ALTER SEQUENCE public.evaluations_id_seq OWNED BY public.evaluations.id;


--
-- Name: experiment_evaluations; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.experiment_evaluations (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    experiment_id uuid NOT NULL,
    metric_id uuid NOT NULL,
    status character varying(20) DEFAULT 'pending'::character varying,
    score numeric(5,2),
    details jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.experiment_evaluations OWNER TO dbadmin;

--
-- Name: experiments; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.experiments (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    dataset_id uuid,
    agent_config jsonb,
    execution_mode character varying(20) DEFAULT 'automated'::character varying NOT NULL,
    status character varying(20) DEFAULT 'pending'::character varying NOT NULL,
    progress integer DEFAULT 0 NOT NULL,
    started_at timestamp with time zone,
    completed_at timestamp with time zone,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.experiments OWNER TO dbadmin;

--
-- Name: guardrail_services; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.guardrail_services (
    id uuid NOT NULL,
    service_id character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    version character varying(50) DEFAULT '1.0.0'::character varying NOT NULL,
    type character varying(50) DEFAULT 'modifier'::character varying NOT NULL,
    endpoint character varying(500) NOT NULL,
    health_check_path character varying(255) DEFAULT '/health'::character varying,
    timeout_ms integer DEFAULT 100,
    capabilities character varying[] DEFAULT '{}'::character varying[],
    supported_content_types character varying[] DEFAULT '{text/plain,application/json}'::character varying[],
    status character varying(50) DEFAULT 'unknown'::character varying,
    last_health_check timestamp with time zone,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.guardrail_services OWNER TO dbadmin;

--
-- Name: integration_dlq; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.integration_dlq (
    id uuid NOT NULL,
    tenant_id uuid,
    destination text NOT NULL,
    event_type text NOT NULL,
    event_version integer NOT NULL,
    payload_json jsonb NOT NULL,
    attempts integer NOT NULL,
    last_error text,
    dead_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone
);


ALTER TABLE public.integration_dlq OWNER TO dbadmin;

--
-- Name: integration_outbox; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.integration_outbox (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    tenant_id uuid,
    destination text DEFAULT 'webhook'::text NOT NULL,
    event_type text NOT NULL,
    event_version integer DEFAULT 1 NOT NULL,
    payload_json jsonb NOT NULL,
    status text DEFAULT 'pending'::text NOT NULL,
    attempts integer DEFAULT 0 NOT NULL,
    next_attempt_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    last_error text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.integration_outbox OWNER TO dbadmin;

--
-- Name: mcp_chat_sessions; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.mcp_chat_sessions (
    session_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid,
    status character varying(20) DEFAULT 'active'::character varying,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    metadata jsonb DEFAULT '{}'::jsonb
);


ALTER TABLE public.mcp_chat_sessions OWNER TO dbadmin;

--
-- Name: mcp_flow_steps; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.mcp_flow_steps (
    step_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    session_id uuid,
    step_number integer NOT NULL,
    step_name character varying(100) NOT NULL,
    status character varying(20) DEFAULT 'pending'::character varying,
    input_data jsonb,
    output_data jsonb,
    processing_time_ms integer,
    policies_applied uuid[] DEFAULT '{}'::uuid[],
    violations_detected uuid[] DEFAULT '{}'::uuid[],
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    completed_at timestamp with time zone
);


ALTER TABLE public.mcp_flow_steps OWNER TO dbadmin;

--
-- Name: openai_api_calls; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.openai_api_calls (
    call_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    session_id uuid,
    step_id uuid,
    model_name character varying(50),
    prompt_tokens integer,
    completion_tokens integer,
    total_tokens integer,
    cost_estimate numeric(10,6),
    response_time_ms integer,
    status_code integer,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.openai_api_calls OWNER TO dbadmin;

--
-- Name: policies; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policies (
    policy_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    category character varying(100) NOT NULL,
    policy_type character varying(50) DEFAULT 'opa'::character varying,
    definition jsonb NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    is_active boolean DEFAULT false,
    severity character varying(20) DEFAULT 'medium'::character varying,
    applies_to_roles text[],
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    updated_by uuid,
    rego_code text,
    blob_container character varying(255) DEFAULT 'rego-policies'::character varying,
    blob_path character varying(500),
    blob_url character varying(1000),
    rego_template_id character varying(100),
    opa_sync_status character varying(50) DEFAULT 'pending'::character varying,
    last_rego_generation timestamp with time zone,
    rego_generation_error text,
    rego_version integer DEFAULT 1,
    original_policy_id uuid,
    cloned_from_policy_name character varying(255),
    deleted_at timestamp with time zone,
    guardrail_id uuid
);


ALTER TABLE public.policies OWNER TO dbadmin;

--
-- Name: policy_executions; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policy_executions (
    execution_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    session_id uuid,
    step_id uuid,
    policy_id uuid,
    execution_status character varying(20),
    input_data jsonb,
    output_data jsonb,
    execution_time_ms integer,
    error_message text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.policy_executions OWNER TO dbadmin;

--
-- Name: policy_group_policies; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policy_group_policies (
    group_id uuid NOT NULL,
    policy_id uuid NOT NULL
);


ALTER TABLE public.policy_group_policies OWNER TO dbadmin;

--
-- Name: policy_groups; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policy_groups (
    group_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    is_template boolean DEFAULT false,
    severity public.severity_level_enum DEFAULT 'medium'::public.severity_level_enum,
    status public.lifecycle_status_enum DEFAULT 'active'::public.lifecycle_status_enum,
    version character varying(20) DEFAULT 'v1.0.0'::character varying,
    tags text[] DEFAULT '{}'::text[],
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    deleted_at timestamp with time zone
);


ALTER TABLE public.policy_groups OWNER TO dbadmin;

--
-- Name: policy_schemas; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policy_schemas (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    schema_name character varying(255) NOT NULL,
    schema_content jsonb NOT NULL,
    description text,
    guardrail_id uuid,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    default_template jsonb,
    template_source character varying(20) DEFAULT 'auto_generated'::character varying,
    CONSTRAINT policy_schemas_template_source_check CHECK (((template_source)::text = ANY ((ARRAY['auto_generated'::character varying, 'manual_override'::character varying, 'external_provided'::character varying, 'migrated_legacy'::character varying])::text[])))
);


ALTER TABLE public.policy_schemas OWNER TO dbadmin;

--
-- Name: policy_templates; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policy_templates (
    template_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    category character varying(100) NOT NULL,
    template_definition jsonb NOT NULL,
    is_system_template boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    guardrail_id character varying(255)
);


ALTER TABLE public.policy_templates OWNER TO dbadmin;

--
-- Name: policy_violations; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.policy_violations (
    violation_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    policy_id uuid,
    user_id uuid,
    violation_type character varying(100) NOT NULL,
    details jsonb,
    severity character varying(20),
    resolved boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    resolved_at timestamp with time zone,
    resolved_by uuid
);


ALTER TABLE public.policy_violations OWNER TO dbadmin;

--
-- Name: rego_templates; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.rego_templates (
    template_id character varying(100) NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    policy_category character varying(100) NOT NULL,
    template_content text NOT NULL,
    variables jsonb DEFAULT '[]'::jsonb,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid
);


ALTER TABLE public.rego_templates OWNER TO dbadmin;

--
-- Name: roles; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.roles (
    role_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    code character varying(100) NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    is_system_role boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    permissions text[] DEFAULT '{}'::text[]
);


ALTER TABLE public.roles OWNER TO dbadmin;

--
-- Name: system_metrics; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.system_metrics (
    metric_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    metric_type character varying(100) NOT NULL,
    metric_name character varying(255) NOT NULL,
    metric_value numeric(15,4),
    dimensions jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.system_metrics OWNER TO dbadmin;

--
-- Name: test_results; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.test_results (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    experiment_id uuid,
    dataset_entry_id uuid,
    test_case_type character varying(20) NOT NULL,
    input jsonb NOT NULL,
    expected_output text,
    actual_output text NOT NULL,
    context text,
    retrieval_context text[],
    tools_called text[],
    expected_outcome character varying(50),
    scenario text,
    status character varying(20) DEFAULT 'pending'::character varying NOT NULL,
    score numeric(5,2),
    latency_ms integer,
    token_count integer,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.test_results OWNER TO dbadmin;

--
-- Name: user_roles; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_roles (
    user_id uuid NOT NULL,
    role_id uuid NOT NULL,
    granted_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    granted_by uuid,
    expires_at timestamp with time zone
);

ALTER TABLE public.user_roles OWNER TO dbadmin;

--
-- Name: users; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.users (
    user_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    azure_ad_id character varying(255) NOT NULL,
    email character varying(255) NOT NULL,
    first_name character varying(100),
    last_name character varying(100),
    role character varying(50) DEFAULT 'user'::character varying NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    updated_by uuid,
    department character varying(255),
    status public.user_status_enum DEFAULT 'active'::public.user_status_enum,
    risk_score numeric(4,2) DEFAULT 0.0,
    last_login timestamp with time zone,
    two_factor_enabled boolean DEFAULT false
);


ALTER TABLE public.users OWNER TO dbadmin;

--
-- Name: enum_categories category_id; Type: DEFAULT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.enum_categories ALTER COLUMN category_id SET DEFAULT nextval('public.enum_categories_category_id_seq'::regclass);


--
-- Name: enum_values value_id; Type: DEFAULT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.enum_values ALTER COLUMN value_id SET DEFAULT nextval('public.enum_values_value_id_seq'::regclass);


--
-- Name: evaluations id; Type: DEFAULT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.evaluations ALTER COLUMN id SET DEFAULT nextval('public.evaluations_id_seq'::regclass);


--
-- Name: agent_access agent_access_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_access
    ADD CONSTRAINT agent_access_pkey PRIMARY KEY (agent_id, role_id);


--
-- Name: agent_access agent_access_unique; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_access
    ADD CONSTRAINT agent_access_unique UNIQUE (agent_id, role_id);


--
-- Name: agent_policies agent_policies_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_policies
    ADD CONSTRAINT agent_policies_pkey PRIMARY KEY (agent_id, policy_id);


--
-- Name: agent_policies agent_policies_unique; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_policies
    ADD CONSTRAINT agent_policies_unique UNIQUE (agent_id, policy_id, link_type);


--
-- Name: agent_role_policies agent_role_policies_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_role_policies
    ADD CONSTRAINT agent_role_policies_pkey PRIMARY KEY (agent_id, role_id, group_id, policy_id);


--
-- Name: agents agents_name_unique; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agents
    ADD CONSTRAINT agents_name_unique UNIQUE (name);


--
-- Name: agents agents_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agents
    ADD CONSTRAINT agents_pkey PRIMARY KEY (agent_id);


--
-- Name: alembic_version alembic_version_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.alembic_version
    ADD CONSTRAINT alembic_version_pkey PRIMARY KEY (version_num);


--
-- Name: audit_log audit_log_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.audit_log
    ADD CONSTRAINT audit_log_pkey PRIMARY KEY (log_id);


--
-- Name: chat_messages chat_messages_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.chat_messages
    ADD CONSTRAINT chat_messages_pkey PRIMARY KEY (message_id);


--
-- Name: dataset_entries dataset_entries_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dataset_entries
    ADD CONSTRAINT dataset_entries_pkey PRIMARY KEY (id);


--
-- Name: datasets datasets_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.datasets
    ADD CONSTRAINT datasets_pkey PRIMARY KEY (id);


--
-- Name: documents documents_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_pkey PRIMARY KEY (document_id);


--
-- Name: enum_categories enum_categories_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.enum_categories
    ADD CONSTRAINT enum_categories_pkey PRIMARY KEY (category_id);


--
-- Name: enum_categories enum_categories_policy_type_field_path_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.enum_categories
    ADD CONSTRAINT enum_categories_policy_type_field_path_key UNIQUE (policy_type, field_path);


--
-- Name: enum_values enum_values_category_id_value_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.enum_values
    ADD CONSTRAINT enum_values_category_id_value_key UNIQUE (category_id, value);


--
-- Name: enum_values enum_values_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.enum_values
    ADD CONSTRAINT enum_values_pkey PRIMARY KEY (value_id);


--
-- Name: evaluation_metrics evaluation_metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.evaluation_metrics
    ADD CONSTRAINT evaluation_metrics_pkey PRIMARY KEY (id);


--
-- Name: evaluations evaluations_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.evaluations
    ADD CONSTRAINT evaluations_pkey PRIMARY KEY (id);


--
-- Name: experiment_evaluations experiment_evaluations_experiment_id_metric_id_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.experiment_evaluations
    ADD CONSTRAINT experiment_evaluations_experiment_id_metric_id_key UNIQUE (experiment_id, metric_id);


--
-- Name: experiment_evaluations experiment_evaluations_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.experiment_evaluations
    ADD CONSTRAINT experiment_evaluations_pkey PRIMARY KEY (id);


--
-- Name: experiments experiments_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.experiments
    ADD CONSTRAINT experiments_pkey PRIMARY KEY (id);


--
-- Name: guardrail_services guardrail_services_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.guardrail_services
    ADD CONSTRAINT guardrail_services_pkey PRIMARY KEY (id);


--
-- Name: guardrail_services guardrail_services_service_id_version_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.guardrail_services
    ADD CONSTRAINT guardrail_services_service_id_version_key UNIQUE (service_id, version);


--
-- Name: integration_dlq integration_dlq_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.integration_dlq
    ADD CONSTRAINT integration_dlq_pkey PRIMARY KEY (id);


--
-- Name: integration_outbox integration_outbox_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.integration_outbox
    ADD CONSTRAINT integration_outbox_pkey PRIMARY KEY (id);


--
-- Name: mcp_chat_sessions mcp_chat_sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.mcp_chat_sessions
    ADD CONSTRAINT mcp_chat_sessions_pkey PRIMARY KEY (session_id);


--
-- Name: mcp_flow_steps mcp_flow_steps_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.mcp_flow_steps
    ADD CONSTRAINT mcp_flow_steps_pkey PRIMARY KEY (step_id);


--
-- Name: openai_api_calls openai_api_calls_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.openai_api_calls
    ADD CONSTRAINT openai_api_calls_pkey PRIMARY KEY (call_id);


--
-- Name: policies policies_name_version_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_name_version_key UNIQUE (name, version);


--
-- Name: policies policies_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_pkey PRIMARY KEY (policy_id);


--
-- Name: policy_executions policy_executions_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_executions
    ADD CONSTRAINT policy_executions_pkey PRIMARY KEY (execution_id);


--
-- Name: policy_group_policies policy_group_policies_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_group_policies
    ADD CONSTRAINT policy_group_policies_pkey PRIMARY KEY (group_id, policy_id);


--
-- Name: policy_group_policies policy_group_policies_unique; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_group_policies
    ADD CONSTRAINT policy_group_policies_unique UNIQUE (group_id, policy_id);


--
-- Name: policy_groups policy_groups_name_unique; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_groups
    ADD CONSTRAINT policy_groups_name_unique UNIQUE (name);


--
-- Name: policy_groups policy_groups_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_groups
    ADD CONSTRAINT policy_groups_pkey PRIMARY KEY (group_id);


--
-- Name: policy_schemas policy_schemas_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_schemas
    ADD CONSTRAINT policy_schemas_pkey PRIMARY KEY (id);


--
-- Name: policy_schemas policy_schemas_schema_name_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_schemas
    ADD CONSTRAINT policy_schemas_schema_name_key UNIQUE (schema_name);


--
-- Name: policy_templates policy_templates_name_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_templates
    ADD CONSTRAINT policy_templates_name_key UNIQUE (name);


--
-- Name: policy_templates policy_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_templates
    ADD CONSTRAINT policy_templates_pkey PRIMARY KEY (template_id);


--
-- Name: policy_violations policy_violations_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_violations
    ADD CONSTRAINT policy_violations_pkey PRIMARY KEY (violation_id);


--
-- Name: rego_templates rego_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.rego_templates
    ADD CONSTRAINT rego_templates_pkey PRIMARY KEY (template_id);


--
-- Name: roles roles_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_code_key UNIQUE (code);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (role_id);


--
-- Name: system_metrics system_metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.system_metrics
    ADD CONSTRAINT system_metrics_pkey PRIMARY KEY (metric_id);


--
-- Name: test_results test_results_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.test_results
    ADD CONSTRAINT test_results_pkey PRIMARY KEY (id);


--
-- Name: user_roles user_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_pkey PRIMARY KEY (user_id, role_id);


--
-- Name: user_roles user_roles_user_role_unique; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_user_role_unique UNIQUE (user_id, role_id);


--
-- Name: users users_azure_ad_id_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_azure_ad_id_key UNIQUE (azure_ad_id);


--
-- Name: users users_email_unique; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_unique UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (user_id);


--
-- Name: idx_agent_access_role; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_agent_access_role ON public.agent_access USING btree (role_id);


--
-- Name: idx_agent_policies_agent; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_agent_policies_agent ON public.agent_policies USING btree (agent_id);


--
-- Name: idx_agent_policies_policy; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_agent_policies_policy ON public.agent_policies USING btree (policy_id);


--
-- Name: idx_agents_active; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_agents_active ON public.agents USING btree (is_active);


--
-- Name: idx_agents_type; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_agents_type ON public.agents USING btree (agent_type);


--
-- Name: idx_arp_agent; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_arp_agent ON public.agent_role_policies USING btree (agent_id);


--
-- Name: idx_arp_agent_role; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_arp_agent_role ON public.agent_role_policies USING btree (agent_id, role_id);


--
-- Name: idx_arp_group; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_arp_group ON public.agent_role_policies USING btree (group_id);


--
-- Name: idx_arp_policy; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_arp_policy ON public.agent_role_policies USING btree (policy_id);


--
-- Name: idx_audit_action; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_action ON public.audit_log USING btree (action);


--
-- Name: idx_audit_log_request_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_log_request_id ON public.audit_log USING btree (request_id);


--
-- Name: idx_audit_log_session_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_log_session_id ON public.audit_log USING btree (session_id);


--
-- Name: idx_audit_log_user_role; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_log_user_role ON public.audit_log USING btree (user_role);


--
-- Name: idx_audit_resource; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_resource ON public.audit_log USING btree (resource_type, resource_id);


--
-- Name: idx_audit_timestamp; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_timestamp ON public.audit_log USING btree ("timestamp");


--
-- Name: idx_audit_user_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_audit_user_id ON public.audit_log USING btree (user_id);


--
-- Name: idx_chat_messages_session; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_chat_messages_session ON public.chat_messages USING btree (session_id);


--
-- Name: idx_dataset_entries_dataset; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_dataset_entries_dataset ON public.dataset_entries USING btree (dataset_id);


--
-- Name: idx_datasets_created_at; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_datasets_created_at ON public.datasets USING btree (created_at DESC);


--
-- Name: idx_datasets_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_datasets_status ON public.datasets USING btree (status);


--
-- Name: idx_documents_sensitive; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_documents_sensitive ON public.documents USING btree (is_sensitive);


--
-- Name: idx_documents_type; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_documents_type ON public.documents USING btree (document_type);


--
-- Name: idx_evaluations_experiment; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_evaluations_experiment ON public.evaluations USING btree (experiment_id);


--
-- Name: idx_experiment_evaluations_experiment; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_experiment_evaluations_experiment ON public.experiment_evaluations USING btree (experiment_id);


--
-- Name: idx_experiments_dataset; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_experiments_dataset ON public.experiments USING btree (dataset_id);


--
-- Name: idx_experiments_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_experiments_status ON public.experiments USING btree (status);


--
-- Name: idx_integration_outbox_created_at; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_integration_outbox_created_at ON public.integration_outbox USING btree (created_at);


--
-- Name: idx_integration_outbox_next_attempt; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_integration_outbox_next_attempt ON public.integration_outbox USING btree (next_attempt_at);


--
-- Name: idx_integration_outbox_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_integration_outbox_status ON public.integration_outbox USING btree (status);


--
-- Name: idx_integration_outbox_status_next; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_integration_outbox_status_next ON public.integration_outbox USING btree (status, next_attempt_at);


--
-- Name: idx_mcp_chat_sessions_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_mcp_chat_sessions_user ON public.mcp_chat_sessions USING btree (user_id);


--
-- Name: idx_mcp_flow_steps_session; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_mcp_flow_steps_session ON public.mcp_flow_steps USING btree (session_id);


--
-- Name: idx_mcp_flow_steps_session_step; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE UNIQUE INDEX idx_mcp_flow_steps_session_step ON public.mcp_flow_steps USING btree (session_id, step_number);


--
-- Name: idx_openai_api_calls_session; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_openai_api_calls_session ON public.openai_api_calls USING btree (session_id);


--
-- Name: idx_pg_policies_policy; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_pg_policies_policy ON public.policy_group_policies USING btree (policy_id);


--
-- Name: idx_policies_active; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_active ON public.policies USING btree (is_active);


--
-- Name: idx_policies_blob_path; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_blob_path ON public.policies USING btree (blob_path);


--
-- Name: idx_policies_category; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_category ON public.policies USING btree (category);


--
-- Name: idx_policies_last_generation; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_last_generation ON public.policies USING btree (last_rego_generation);


--
-- Name: idx_policies_original_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_original_id ON public.policies USING btree (original_policy_id);


--
-- Name: idx_policies_rego_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_rego_status ON public.policies USING btree (opa_sync_status);


--
-- Name: idx_policies_roles; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_roles ON public.policies USING gin (applies_to_roles);


--
-- Name: idx_policies_severity; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policies_severity ON public.policies USING btree (severity);


--
-- Name: idx_policy_executions_policy; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policy_executions_policy ON public.policy_executions USING btree (policy_id);


--
-- Name: idx_policy_executions_session; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policy_executions_session ON public.policy_executions USING btree (session_id);


--
-- Name: idx_policy_groups_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policy_groups_status ON public.policy_groups USING btree (status);


--
-- Name: idx_policy_schemas_active; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policy_schemas_active ON public.policy_schemas USING btree (schema_name) WHERE (is_active = true);


--
-- Name: idx_policy_schemas_guardrail; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policy_schemas_guardrail ON public.policy_schemas USING btree (guardrail_id) WHERE (guardrail_id IS NOT NULL);


--
-- Name: idx_policy_schemas_template_source; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_policy_schemas_template_source ON public.policy_schemas USING btree (template_source) WHERE (is_active = true);


--
-- Name: idx_system_metrics_type_name; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_system_metrics_type_name ON public.system_metrics USING btree (metric_type, metric_name);


--
-- Name: idx_test_results_experiment; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_test_results_experiment ON public.test_results USING btree (experiment_id);


--
-- Name: idx_test_results_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_test_results_status ON public.test_results USING btree (status);


--
-- Name: idx_users_azure_ad_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_users_azure_ad_id ON public.users USING btree (azure_ad_id);


--
-- Name: idx_users_email; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_users_email ON public.users USING btree (email);


--
-- Name: idx_users_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_users_status ON public.users USING btree (status);


--
-- Name: idx_violations_policy_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_violations_policy_id ON public.policy_violations USING btree (policy_id);


--
-- Name: idx_violations_resolved; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_violations_resolved ON public.policy_violations USING btree (resolved);


--
-- Name: idx_violations_user_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_violations_user_id ON public.policy_violations USING btree (user_id);


--
-- Name: agents trg_integration_agents; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER trg_integration_agents AFTER INSERT OR DELETE OR UPDATE ON public.agents FOR EACH ROW EXECUTE FUNCTION public.trg_agents_enqueue();


--
-- Name: agent_role_policies trg_integration_arp; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER trg_integration_arp AFTER INSERT OR DELETE ON public.agent_role_policies FOR EACH ROW EXECUTE FUNCTION public.trg_arp_enqueue();


--
-- Name: integration_outbox trg_outbox_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER trg_outbox_updated_at BEFORE UPDATE ON public.integration_outbox FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: policies trg_policies_integration; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER trg_policies_integration AFTER INSERT OR DELETE OR UPDATE ON public.policies FOR EACH ROW EXECUTE FUNCTION public.trg_policies_enqueue();


--
-- Name: roles trg_roles_integration; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER trg_roles_integration AFTER INSERT OR DELETE OR UPDATE ON public.roles FOR EACH ROW EXECUTE FUNCTION public.trg_roles_enqueue();


--
-- Name: documents update_documents_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON public.documents FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: enum_categories update_enum_categories_updated_at_trigger; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_enum_categories_updated_at_trigger BEFORE UPDATE ON public.enum_categories FOR EACH ROW EXECUTE FUNCTION public.update_enum_categories_updated_at();


--
-- Name: enum_values update_enum_values_updated_at_trigger; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_enum_values_updated_at_trigger BEFORE UPDATE ON public.enum_values FOR EACH ROW EXECUTE FUNCTION public.update_enum_values_updated_at();


--
-- Name: mcp_chat_sessions update_mcp_chat_sessions_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_mcp_chat_sessions_updated_at BEFORE UPDATE ON public.mcp_chat_sessions FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: policies update_policies_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_policies_updated_at BEFORE UPDATE ON public.policies FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: policy_groups update_policy_groups_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_policy_groups_updated_at BEFORE UPDATE ON public.policy_groups FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: policy_schemas update_policy_schemas_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_policy_schemas_updated_at BEFORE UPDATE ON public.policy_schemas FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: rego_templates update_rego_templates_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_rego_templates_updated_at BEFORE UPDATE ON public.rego_templates FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: roles update_roles_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON public.roles FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: users update_users_updated_at; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: agent_access agent_access_agent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_access
    ADD CONSTRAINT agent_access_agent_id_fkey FOREIGN KEY (agent_id) REFERENCES public.agents(agent_id) ON DELETE CASCADE;


--
-- Name: agent_access agent_access_granted_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_access
    ADD CONSTRAINT agent_access_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES public.users(user_id);


--
-- Name: agent_access agent_access_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_access
    ADD CONSTRAINT agent_access_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(role_id) ON DELETE CASCADE;


--
-- Name: agent_policies agent_policies_agent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_policies
    ADD CONSTRAINT agent_policies_agent_id_fkey FOREIGN KEY (agent_id) REFERENCES public.agents(agent_id) ON DELETE CASCADE;


--
-- Name: agent_policies agent_policies_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_policies
    ADD CONSTRAINT agent_policies_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id) ON DELETE CASCADE;


--
-- Name: agent_role_policies agent_role_policies_agent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_role_policies
    ADD CONSTRAINT agent_role_policies_agent_id_fkey FOREIGN KEY (agent_id) REFERENCES public.agents(agent_id) ON DELETE CASCADE;


--
-- Name: agent_role_policies agent_role_policies_group_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_role_policies
    ADD CONSTRAINT agent_role_policies_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.policy_groups(group_id) ON DELETE CASCADE;


--
-- Name: agent_role_policies agent_role_policies_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_role_policies
    ADD CONSTRAINT agent_role_policies_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id) ON DELETE CASCADE;


--
-- Name: agent_role_policies agent_role_policies_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_role_policies
    ADD CONSTRAINT agent_role_policies_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(role_id) ON DELETE CASCADE;


--
-- Name: agents agents_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agents
    ADD CONSTRAINT agents_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: audit_log audit_log_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.audit_log
    ADD CONSTRAINT audit_log_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id);


--
-- Name: chat_messages chat_messages_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.chat_messages
    ADD CONSTRAINT chat_messages_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.mcp_chat_sessions(session_id) ON DELETE CASCADE;


--
-- Name: dataset_entries dataset_entries_dataset_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dataset_entries
    ADD CONSTRAINT dataset_entries_dataset_id_fkey FOREIGN KEY (dataset_id) REFERENCES public.datasets(id) ON DELETE CASCADE;


--
-- Name: documents documents_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: documents documents_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: enum_values enum_values_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.enum_values
    ADD CONSTRAINT enum_values_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.enum_categories(category_id) ON DELETE CASCADE;


--
-- Name: evaluations evaluations_experiment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.evaluations
    ADD CONSTRAINT evaluations_experiment_id_fkey FOREIGN KEY (experiment_id) REFERENCES public.experiments(id) ON DELETE CASCADE;


--
-- Name: experiment_evaluations experiment_evaluations_experiment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.experiment_evaluations
    ADD CONSTRAINT experiment_evaluations_experiment_id_fkey FOREIGN KEY (experiment_id) REFERENCES public.experiments(id) ON DELETE CASCADE;


--
-- Name: experiment_evaluations experiment_evaluations_metric_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.experiment_evaluations
    ADD CONSTRAINT experiment_evaluations_metric_id_fkey FOREIGN KEY (metric_id) REFERENCES public.evaluation_metrics(id);


--
-- Name: experiments experiments_dataset_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.experiments
    ADD CONSTRAINT experiments_dataset_id_fkey FOREIGN KEY (dataset_id) REFERENCES public.datasets(id) ON DELETE CASCADE;


--
-- Name: agent_role_policies fk_group_policy_pair; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.agent_role_policies
    ADD CONSTRAINT fk_group_policy_pair FOREIGN KEY (group_id, policy_id) REFERENCES public.policy_group_policies(group_id, policy_id) ON DELETE CASCADE;


--
-- Name: mcp_chat_sessions mcp_chat_sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.mcp_chat_sessions
    ADD CONSTRAINT mcp_chat_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id);


--
-- Name: mcp_flow_steps mcp_flow_steps_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.mcp_flow_steps
    ADD CONSTRAINT mcp_flow_steps_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.mcp_chat_sessions(session_id) ON DELETE CASCADE;


--
-- Name: openai_api_calls openai_api_calls_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.openai_api_calls
    ADD CONSTRAINT openai_api_calls_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.mcp_chat_sessions(session_id) ON DELETE CASCADE;


--
-- Name: openai_api_calls openai_api_calls_step_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.openai_api_calls
    ADD CONSTRAINT openai_api_calls_step_id_fkey FOREIGN KEY (step_id) REFERENCES public.mcp_flow_steps(step_id) ON DELETE CASCADE;


--
-- Name: policies policies_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: policies policies_original_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_original_policy_id_fkey FOREIGN KEY (original_policy_id) REFERENCES public.policies(policy_id);


--
-- Name: policies policies_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policies
    ADD CONSTRAINT policies_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: policy_executions policy_executions_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_executions
    ADD CONSTRAINT policy_executions_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id);


--
-- Name: policy_executions policy_executions_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_executions
    ADD CONSTRAINT policy_executions_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.mcp_chat_sessions(session_id) ON DELETE CASCADE;


--
-- Name: policy_executions policy_executions_step_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_executions
    ADD CONSTRAINT policy_executions_step_id_fkey FOREIGN KEY (step_id) REFERENCES public.mcp_flow_steps(step_id) ON DELETE CASCADE;


--
-- Name: policy_group_policies policy_group_policies_group_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_group_policies
    ADD CONSTRAINT policy_group_policies_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.policy_groups(group_id) ON DELETE CASCADE;


--
-- Name: policy_group_policies policy_group_policies_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_group_policies
    ADD CONSTRAINT policy_group_policies_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id) ON DELETE CASCADE;


--
-- Name: policy_groups policy_groups_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_groups
    ADD CONSTRAINT policy_groups_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: policy_templates policy_templates_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_templates
    ADD CONSTRAINT policy_templates_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: policy_violations policy_violations_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_violations
    ADD CONSTRAINT policy_violations_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id);


--
-- Name: policy_violations policy_violations_resolved_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_violations
    ADD CONSTRAINT policy_violations_resolved_by_fkey FOREIGN KEY (resolved_by) REFERENCES public.users(user_id);


--
-- Name: policy_violations policy_violations_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.policy_violations
    ADD CONSTRAINT policy_violations_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id);


--
-- Name: rego_templates rego_templates_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.rego_templates
    ADD CONSTRAINT rego_templates_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: roles roles_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: test_results test_results_dataset_entry_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.test_results
    ADD CONSTRAINT test_results_dataset_entry_id_fkey FOREIGN KEY (dataset_entry_id) REFERENCES public.dataset_entries(id) ON DELETE CASCADE;


--
-- Name: test_results test_results_experiment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.test_results
    ADD CONSTRAINT test_results_experiment_id_fkey FOREIGN KEY (experiment_id) REFERENCES public.experiments(id) ON DELETE CASCADE;


--
-- Name: user_roles user_roles_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(role_id) ON DELETE CASCADE;


--
-- Name: user_roles user_roles_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE;


--
-- Name: users users_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: users users_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- PostgreSQL database dump complete
--

\unrestrict zhCwBKdxurd5eTTxihn9a8fbGaWJjbe427z3NhOZgDz6VNNfXcjlenBS2Yb87ut

