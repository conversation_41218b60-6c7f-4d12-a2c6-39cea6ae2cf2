#!/bin/bash
set -e

# =============================================================================
# Testing and Observability Platform Setup Script
# =============================================================================
# Purpose: Initialize database with schema and sample data for testing platform
# Usage: ./setup-testing-observability.sh [OPTIONS]
# 
# Options:
#   --container <name>   PostgreSQL container name (default: pilot-postgres)
#   --database <name>    Database name (default: vitea_db)
#   --user <name>        Database user (default: dbadmin)
#   --clean              Drop and recreate all testing tables
#   --sample-data        Load sample data after schema creation
#   --help               Show this help message
# =============================================================================

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TESTING_PLATFORM_DIR="$(cd "${SCRIPT_DIR}/../.." && pwd)/testing-observability-platform"

# Default configuration for testing-observability-platform
CONTAINER_NAME="eval-postgres"
DB_NAME="evaluation_db"
DB_USER="dbadmin"
DB_PASSWORD="postgres123"
DB_PORT="5433"
CLEAN_MODE=false
LOAD_SAMPLE_DATA=false

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo ""
    echo -e "${BLUE}📋 STEP: $1${NC}"
    echo "----------------------------------------"
}

# Help function
show_help() {
    echo "Testing and Observability Platform Setup Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --container <name>   PostgreSQL container name (default: eval-postgres)"
    echo "  --database <name>    Database name (default: evaluation_db)"
    echo "  --user <name>        Database user (default: dbadmin)"
    echo "  --clean              Drop and recreate all testing tables"
    echo "  --sample-data        Load sample data after schema creation"
    echo "  --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                              # Create schema only"
    echo "  $0 --sample-data                # Create schema and load sample data"
    echo "  $0 --clean --sample-data        # Clean install with sample data"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --container)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        --database)
            DB_NAME="$2"
            shift 2
            ;;
        --user)
            DB_USER="$2"
            shift 2
            ;;
        --clean)
            CLEAN_MODE=true
            shift
            ;;
        --sample-data)
            LOAD_SAMPLE_DATA=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Load environment variables from testing-observability-platform
load_environment() {
    # Load environment variables from testing-observability-platform .env if it exists
    if [[ -f "${TESTING_PLATFORM_DIR}/.env" ]]; then
        log_info "Loading environment from ${TESTING_PLATFORM_DIR}/.env"
        source "${TESTING_PLATFORM_DIR}/.env" 2>/dev/null || true
        # Override with values from .env if they exist
        CONTAINER_NAME="${CONTAINER_NAME:-eval-postgres}"
        DB_NAME="${DB_NAME:-evaluation_db}"
        DB_USER="${DB_USER:-dbadmin}"
        DB_PASSWORD="${DB_PASSWORD:-postgres123}"
        DB_PORT="${DB_PORT:-5433}"
    else
        log_info "No .env file found at ${TESTING_PLATFORM_DIR}/.env, using defaults"
    fi
}

# Check if Docker container is running
check_container() {
    log_step "Checking Docker container"
    
    if ! docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        log_error "Container '${CONTAINER_NAME}' is not running"
        log_info "Available containers:"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | head -10
        echo ""
        log_info "To start the testing-observability-platform:"
        log_info "  cd ${TESTING_PLATFORM_DIR}"
        log_info "  docker-compose up -d postgres"
        log_info "Or start the container directly:"
        log_info "  docker start ${CONTAINER_NAME}"
        exit 1
    fi
    
    # Check container health
    local health_status=$(docker inspect --format='{{.State.Health.Status}}' "${CONTAINER_NAME}" 2>/dev/null || echo "none")
    if [[ "$health_status" == "healthy" ]]; then
        log_success "Container '${CONTAINER_NAME}' is running and healthy"
    elif [[ "$health_status" == "unhealthy" ]]; then
        log_warning "Container '${CONTAINER_NAME}' is running but unhealthy"
        log_info "Container logs (last 10 lines):"
        docker logs --tail 10 "${CONTAINER_NAME}" 2>&1 | sed 's/^/  /'
    else
        log_success "Container '${CONTAINER_NAME}' is running"
    fi
}

# Test database connection
test_connection() {
    log_step "Testing database connection"
    
    log_info "Connection details:"
    log_info "  Container: ${CONTAINER_NAME}"
    log_info "  Database: ${DB_NAME}"
    log_info "  User: ${DB_USER}"
    log_info "  External Port: ${DB_PORT}"
    echo ""
    
    # Test container database connection
    log_info "Testing connection via container..."
    if docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1; then
        log_success "Container database connection successful"
    else
        log_error "Failed to connect to database via container"
        log_info "Attempting to diagnose the issue..."
        
        # Check if the database exists
        log_info "Checking available databases..."
        docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d postgres -c "\\l" 2>&1 | grep -E "(Name|${DB_NAME}|template|postgres)" | head -10
        
        # Check if user exists
        log_info "Checking if user '${DB_USER}' exists..."
        docker exec "$CONTAINER_NAME" psql -U postgres -d postgres -c "SELECT rolname FROM pg_roles WHERE rolname='${DB_USER}';" 2>&1 | head -5
        
        exit 1
    fi
    
    # Test external connection if port is exposed
    log_info "Testing external connection via localhost:${DB_PORT}..."
    if command -v psql >/dev/null 2>&1; then
        if PGPASSWORD="$DB_PASSWORD" psql -h localhost -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1; then
            log_success "External database connection successful"
        else
            log_warning "External connection failed (this might be normal if port is not exposed)"
        fi
    else
        log_info "psql command not available on host, skipping external connection test"
    fi
    
    # Get database version and basic info
    log_info "Database information:"
    docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -c "SELECT version();" 2>/dev/null | grep -E "(PostgreSQL|version)" | head -2 | sed 's/^/  /'
}

# Clean existing testing tables if requested
clean_tables() {
    if [[ "$CLEAN_MODE" == "true" ]]; then
        log_step "Cleaning existing testing tables"
        
        # Drop tables in reverse dependency order
        local tables=(
            "experiment_evaluations"
            "test_results"
            "evaluations"
            "experiments"
            "dataset_entries"
            "datasets"
            "evaluation_metrics"
        )
        
        for table in "${tables[@]}"; do
            log_info "Dropping table: $table"
            docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" \
                -c "DROP TABLE IF EXISTS $table CASCADE;" 2>/dev/null || true
        done
        
        log_success "Existing tables cleaned"
    fi
}

# Create testing and observability schema
create_schema() {
    log_step "Creating testing and observability schema"
    
    # Check if schema file exists
    SCHEMA_FILE="${SCRIPT_DIR}/create-complete-schema.sql"
    if [[ ! -f "$SCHEMA_FILE" ]]; then
        log_error "Schema file not found: $SCHEMA_FILE"
        exit 1
    fi
    
    # Apply schema
    log_info "Applying database schema..."
    cat "$SCHEMA_FILE" | docker exec -i "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" 2>&1 | \
        grep -E "(ERROR|NOTICE|CREATE)" | tail -10
    
    # Verify tables were created
    local table_count=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t \
        -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_name IN ('datasets', 'experiments', 'evaluations', 'test_results', 'evaluation_metrics');")
    
    if [[ $table_count -ge 5 ]]; then
        log_success "Testing and observability tables created successfully"
    else
        log_warning "Some tables may not have been created. Found $table_count tables."
    fi
}

# Load sample data
load_sample_data() {
    if [[ "$LOAD_SAMPLE_DATA" == "true" ]]; then
        log_step "Loading sample data"
        
        # Check if sample data file exists
        SAMPLE_DATA_FILE="${SCRIPT_DIR}/testing-observability-sample-data.sql"
        if [[ ! -f "$SAMPLE_DATA_FILE" ]]; then
            log_error "Sample data file not found: $SAMPLE_DATA_FILE"
            exit 1
        fi
        
        # Load sample data
        log_info "Loading testing and observability sample data..."
        cat "$SAMPLE_DATA_FILE" | docker exec -i "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" 2>&1 | \
            grep -E "(NOTICE|INSERT|UPDATE)" | tail -15
        
        log_success "Sample data loaded successfully"
    fi
}

# Display summary
display_summary() {
    log_step "Setup Summary"
    
    # Get counts
    local dataset_count=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t \
        -c "SELECT COUNT(*) FROM datasets WHERE status = 'active';" 2>/dev/null || echo "0")
    local experiment_count=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t \
        -c "SELECT COUNT(*) FROM experiments;" 2>/dev/null || echo "0")
    local metric_count=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t \
        -c "SELECT COUNT(*) FROM evaluation_metrics;" 2>/dev/null || echo "0")
    
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}Testing Platform Setup Complete!${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo ""
    echo "Database: ${DB_NAME} on ${CONTAINER_NAME}"
    echo "Datasets: $(echo $dataset_count | xargs)"
    echo "Experiments: $(echo $experiment_count | xargs)"
    echo "Evaluation Metrics: $(echo $metric_count | xargs)"
    echo ""
    
    if [[ "$LOAD_SAMPLE_DATA" == "true" ]]; then
        echo "Sample data includes:"
        echo "  • Healthcare Q&A test suite"
        echo "  • Tool usage evaluation suite"
        echo "  • RAG retrieval accuracy tests"
        echo "  • Edge case handling tests"
        echo "  • 4 sample experiments with results"
        echo ""
    fi
    
    echo "Next steps:"
    echo "  1. Start the testing platform services:"
    echo "     cd ${TESTING_PLATFORM_DIR}"
    echo "     docker-compose up -d"
    echo ""
    echo "  2. Access the testing features:"
    echo "     • Testing API: http://localhost:9000"
    echo "     • Admin UI (with testing features): http://localhost:3000"
    echo "     • Database: localhost:5433"
    echo ""
    echo "  3. View datasets and create new experiments"
    echo "  4. Run evaluations and view results"
    echo ""
}

# Main execution flow
main() {
    log_info "🧪 Testing and Observability Platform Setup"
    log_info "Platform Directory: ${TESTING_PLATFORM_DIR}"
    
    # Check if testing platform directory exists
    if [[ ! -d "$TESTING_PLATFORM_DIR" ]]; then
        log_error "Testing platform directory not found: $TESTING_PLATFORM_DIR"
        log_info "Expected to find testing-observability-platform directory alongside pilot"
        exit 1
    fi
    
    # Load environment variables
    load_environment
    
    log_info "Container: $CONTAINER_NAME"
    log_info "Database: $DB_NAME"
    echo "=========================================="
    
    check_container
    test_connection
    clean_tables
    create_schema
    load_sample_data
    display_summary
    
    log_success "✅ Setup completed successfully!"
}

# Execute main function
main "$@"