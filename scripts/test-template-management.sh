#!/bin/bash

# ==============================================================================
# Template Management System Test Script
# ==============================================================================
# This script tests the new template management system functionality
# including auto-generation, manual override, and API endpoints
# ==============================================================================

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
API_BASE="${API_BASE:-http://localhost:8001/api/v1}"
ADMIN_TOKEN="${ADMIN_TOKEN:-admin-token}"

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# ==============================================================================
# Helper Functions
# ==============================================================================

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

test_pass() {
    echo -e "${GREEN}✓${NC} $1"
    ((TESTS_PASSED++))
}

test_fail() {
    echo -e "${RED}✗${NC} $1"
    ((TESTS_FAILED++))
}

# ==============================================================================
# Pre-flight Checks
# ==============================================================================

log_info "Starting Template Management System Tests"
echo "=============================================="

# Check if API is running
log_info "Checking if API is accessible..."
if curl -s -f "http://localhost:8001/health" > /dev/null 2>&1; then
    test_pass "API is running at localhost:8001"
else
    test_fail "API is not accessible at localhost:8001"
    log_error "Please ensure the enhanced-api-project is running"
    exit 1
fi

# ==============================================================================
# Test 1: Get Template for Existing Schema
# ==============================================================================

echo ""
log_info "Test 1: Get Template for Existing Schema"
echo "----------------------------------------"

RESPONSE=$(curl -s -X GET \
    "${API_BASE}/schemas/medical_privacy/template" \
    -H "Authorization: Bearer ${ADMIN_TOKEN}")

if echo "$RESPONSE" | grep -q '"schema_name":"medical_privacy"'; then
    test_pass "Successfully retrieved template for medical_privacy schema"
    echo "Template source: $(echo "$RESPONSE" | grep -o '"source":"[^"]*"' | cut -d'"' -f4)"
else
    test_fail "Failed to retrieve template for medical_privacy schema"
    echo "Response: $RESPONSE"
fi

# ==============================================================================
# Test 2: Get Template for Non-existent Schema
# ==============================================================================

echo ""
log_info "Test 2: Get Template for Non-existent Schema"
echo "--------------------------------------------"

RESPONSE=$(curl -s -X GET \
    "${API_BASE}/schemas/non_existent_schema/template" \
    -H "Authorization: Bearer ${ADMIN_TOKEN}")

if echo "$RESPONSE" | grep -q "error"; then
    test_pass "Correctly returned error for non-existent schema"
else
    test_fail "Should have returned error for non-existent schema"
    echo "Response: $RESPONSE"
fi

# ==============================================================================
# Test 3: Manual Template Override
# ==============================================================================

echo ""
log_info "Test 3: Manual Template Override"
echo "--------------------------------"

# Create a custom template
CUSTOM_TEMPLATE='{
  "template": {
    "type": "medical_privacy",
    "severity": "critical",
    "custom_field": "test_value",
    "allowed_roles": ["admin", "doctor"],
    "test_timestamp": "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'"
  },
  "source": "manual_override"
}'

RESPONSE=$(curl -s -X PUT \
    "${API_BASE}/schemas/medical_privacy/template" \
    -H "Authorization: Bearer ${ADMIN_TOKEN}" \
    -H "Content-Type: application/json" \
    -d "$CUSTOM_TEMPLATE")

if echo "$RESPONSE" | grep -q '"success":true'; then
    test_pass "Successfully updated template with manual override"
    
    # Verify the override persisted
    VERIFY_RESPONSE=$(curl -s -X GET \
        "${API_BASE}/schemas/medical_privacy/template" \
        -H "Authorization: Bearer ${ADMIN_TOKEN}")
    
    if echo "$VERIFY_RESPONSE" | grep -q '"custom_field":"test_value"'; then
        test_pass "Manual override persisted correctly"
    else
        test_fail "Manual override did not persist"
    fi
else
    test_fail "Failed to update template with manual override"
    echo "Response: $RESPONSE"
fi

# ==============================================================================
# Test 4: Reset Template to Auto-generated
# ==============================================================================

echo ""
log_info "Test 4: Reset Template to Auto-generated"
echo "----------------------------------------"

RESPONSE=$(curl -s -X DELETE \
    "${API_BASE}/schemas/medical_privacy/template" \
    -H "Authorization: Bearer ${ADMIN_TOKEN}")

if echo "$RESPONSE" | grep -q '"success":true'; then
    test_pass "Successfully reset template to auto-generated"
    
    # Verify custom field is gone
    VERIFY_RESPONSE=$(curl -s -X GET \
        "${API_BASE}/schemas/medical_privacy/template" \
        -H "Authorization: Bearer ${ADMIN_TOKEN}")
    
    if ! echo "$VERIFY_RESPONSE" | grep -q '"custom_field"'; then
        test_pass "Custom fields removed after reset"
    else
        test_fail "Custom fields still present after reset"
    fi
else
    test_fail "Failed to reset template"
    echo "Response: $RESPONSE"
fi

# ==============================================================================
# Test 5: Get Template Status
# ==============================================================================

echo ""
log_info "Test 5: Get Template Status"
echo "---------------------------"

RESPONSE=$(curl -s -X GET \
    "${API_BASE}/schemas/templates/status" \
    -H "Authorization: Bearer ${ADMIN_TOKEN}")

if echo "$RESPONSE" | grep -q '"summary"'; then
    test_pass "Successfully retrieved template status"
    
    # Extract and display summary
    TOTAL=$(echo "$RESPONSE" | grep -o '"total":[0-9]*' | cut -d':' -f2)
    WITH_TEMPLATES=$(echo "$RESPONSE" | grep -o '"with_templates":[0-9]*' | cut -d':' -f2)
    WITHOUT_TEMPLATES=$(echo "$RESPONSE" | grep -o '"without_templates":[0-9]*' | cut -d':' -f2)
    
    echo "  Total schemas: $TOTAL"
    echo "  With templates: $WITH_TEMPLATES"
    echo "  Without templates: $WITHOUT_TEMPLATES"
else
    test_fail "Failed to retrieve template status"
    echo "Response: $RESPONSE"
fi

# ==============================================================================
# Test 6: Test Deprecated Endpoints
# ==============================================================================

echo ""
log_info "Test 6: Test Deprecated Endpoints"
echo "---------------------------------"

RESPONSE=$(curl -s -I -X GET \
    "${API_BASE}/policies/templates" \
    -H "Authorization: Bearer ${ADMIN_TOKEN}")

if echo "$RESPONSE" | grep -q "X-Deprecated: true"; then
    test_pass "Deprecated endpoint returns deprecation header"
else
    test_fail "Deprecated endpoint should return X-Deprecated header"
fi

# ==============================================================================
# Test 7: Schema Update Triggers Template Generation
# ==============================================================================

echo ""
log_info "Test 7: Schema Update Triggers Template Generation"
echo "--------------------------------------------------"

# Create a test schema with defaults
TEST_SCHEMA='{
  "schema_content": {
    "type": "object",
    "properties": {
      "type": {
        "type": "string",
        "const": "test_policy"
      },
      "severity": {
        "type": "string",
        "enum": ["low", "medium", "high"],
        "default": "low"
      },
      "enabled": {
        "type": "boolean",
        "default": true
      },
      "test_array": {
        "type": "array",
        "items": {
          "type": "string",
          "enum": ["option1", "option2"]
        },
        "minItems": 1
      }
    },
    "required": ["type", "severity", "test_array"]
  },
  "description": "Test schema for template generation"
}'

RESPONSE=$(curl -s -X PUT \
    "${API_BASE}/schemas/test_policy" \
    -H "Authorization: Bearer ${ADMIN_TOKEN}" \
    -H "Content-Type: application/json" \
    -d "$TEST_SCHEMA")

if echo "$RESPONSE" | grep -q '"template_updated":true'; then
    test_pass "Schema update triggered template generation"
    
    # Verify the auto-generated template
    TEMPLATE_RESPONSE=$(curl -s -X GET \
        "${API_BASE}/schemas/test_policy/template" \
        -H "Authorization: Bearer ${ADMIN_TOKEN}")
    
    if echo "$TEMPLATE_RESPONSE" | grep -q '"severity":"low"' && \
       echo "$TEMPLATE_RESPONSE" | grep -q '"enabled":true'; then
        test_pass "Auto-generated template has correct default values"
    else
        test_fail "Auto-generated template does not have expected defaults"
        echo "Template: $(echo "$TEMPLATE_RESPONSE" | jq -c '.template' 2>/dev/null || echo "$TEMPLATE_RESPONSE")"
    fi
else
    test_fail "Schema update did not trigger template generation"
    echo "Response: $RESPONSE"
fi

# ==============================================================================
# Test 8: Regenerate All Templates
# ==============================================================================

echo ""
log_info "Test 8: Regenerate All Templates"
echo "--------------------------------"

RESPONSE=$(curl -s -X POST \
    "${API_BASE}/schemas/regenerate-templates?overrideManual=false" \
    -H "Authorization: Bearer ${ADMIN_TOKEN}")

if echo "$RESPONSE" | grep -q '"success":true'; then
    test_pass "Successfully triggered bulk template regeneration"
    MESSAGE=$(echo "$RESPONSE" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
    echo "  Result: $MESSAGE"
else
    test_fail "Failed to regenerate templates"
    echo "Response: $RESPONSE"
fi

# ==============================================================================
# Test 9: Frontend API Compatibility
# ==============================================================================

echo ""
log_info "Test 9: Frontend API Compatibility"
echo "----------------------------------"

# Test the frontend-friendly endpoint without auth (if allowed)
RESPONSE=$(curl -s -X GET "${API_BASE}/schemas/medical_privacy/template")

if [ $? -eq 0 ]; then
    if echo "$RESPONSE" | grep -q "template"; then
        test_pass "Frontend can access template endpoint"
    else
        log_warning "Frontend endpoint accessible but may require authentication"
        test_pass "Frontend endpoint is accessible"
    fi
else
    test_fail "Frontend cannot access template endpoint"
fi

# ==============================================================================
# Test 10: Performance Test - Cache Behavior
# ==============================================================================

echo ""
log_info "Test 10: Performance Test - Cache Behavior"
echo "------------------------------------------"

# Time first request (uncached)
START_TIME=$(date +%s%N)
curl -s -X GET \
    "${API_BASE}/schemas/data_privacy/template" \
    -H "Authorization: Bearer ${ADMIN_TOKEN}" > /dev/null
END_TIME=$(date +%s%N)
FIRST_TIME=$((($END_TIME - $START_TIME) / 1000000))

# Time second request (should be cached)
START_TIME=$(date +%s%N)
curl -s -X GET \
    "${API_BASE}/schemas/data_privacy/template" \
    -H "Authorization: Bearer ${ADMIN_TOKEN}" > /dev/null
END_TIME=$(date +%s%N)
SECOND_TIME=$((($END_TIME - $START_TIME) / 1000000))

echo "  First request: ${FIRST_TIME}ms"
echo "  Second request (cached): ${SECOND_TIME}ms"

if [ $SECOND_TIME -lt $FIRST_TIME ]; then
    test_pass "Cache is working (second request faster)"
else
    log_warning "Cache may not be working optimally"
    test_pass "Performance test completed"
fi

# ==============================================================================
# Summary
# ==============================================================================

echo ""
echo "=============================================="
log_info "Test Summary"
echo "=============================================="
echo -e "${GREEN}Tests Passed:${NC} $TESTS_PASSED"
echo -e "${RED}Tests Failed:${NC} $TESTS_FAILED"

if [ $TESTS_FAILED -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 All tests passed successfully!${NC}"
    echo "The template management system is working correctly."
    exit 0
else
    echo ""
    echo -e "${RED}⚠️  Some tests failed.${NC}"
    echo "Please review the failures above and check the system logs."
    exit 1
fi