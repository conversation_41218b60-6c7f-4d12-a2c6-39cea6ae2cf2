-- ================================================================
-- Testing and Observability Platform Sample Data
-- ================================================================
-- This script populates the testing and observability tables with
-- sample data to demonstrate the platform's capabilities
-- ================================================================

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ================================================================
-- EVALUATION METRICS - Define standard evaluation criteria
-- ================================================================

INSERT INTO evaluation_metrics (id, name, description, category, implementation_type, config, is_active) VALUES
-- Accuracy Metrics
('11111111-1111-1111-1111-111111111111', 'Exact Match', 'Checks if output exactly matches expected', 'accuracy', 'exact_match', 
 '{"case_sensitive": false, "ignore_whitespace": true}'::jsonb, true),

('11111111-1111-1111-1111-************', 'Fuzzy Match', 'Uses fuzzy string matching with threshold', 'accuracy', 'fuzzy_match',
 '{"threshold": 0.85, "algorithm": "levenshtein"}'::jsonb, true),

('11111111-1111-1111-1111-************', 'Semantic Similarity', 'Compares semantic meaning using embeddings', 'accuracy', 'semantic',
 '{"model": "text-embedding-ada-002", "threshold": 0.9}'::jsonb, true),

-- Performance Metrics
('11111111-1111-1111-1111-444444444444', 'Response Time', 'Measures response latency in milliseconds', 'performance', 'latency',
 '{"max_acceptable_ms": 1000, "target_ms": 500}'::jsonb, true),

('11111111-1111-1111-1111-555555555555', 'Token Usage', 'Tracks token consumption', 'performance', 'tokens',
 '{"max_tokens": 4000, "warn_threshold": 3000}'::jsonb, true),

-- Quality Metrics
('11111111-1111-1111-1111-666666666666', 'Hallucination Detection', 'Detects factual inaccuracies', 'quality', 'hallucination',
 '{"check_sources": true, "fact_check_api": "internal"}'::jsonb, true),

('11111111-1111-1111-1111-777777777777', 'Relevance Score', 'Measures response relevance to query', 'quality', 'relevance',
 '{"min_score": 0.7, "scoring_model": "cross-encoder"}'::jsonb, true),

('11111111-1111-1111-1111-888888888888', 'Completeness Check', 'Verifies all required elements are present', 'quality', 'completeness',
 '{"required_elements": ["answer", "explanation", "confidence"]}'::jsonb, true)

ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  config = EXCLUDED.config,
  updated_at = CURRENT_TIMESTAMP;

-- ================================================================
-- DATASETS - Sample test datasets
-- ================================================================

-- Healthcare Q&A Dataset
INSERT INTO datasets (id, name, description, type, status, data, record_count, created_by) VALUES
('*************-2222-2222-111111111111', 
 'Healthcare Q&A Test Suite', 
 'Common healthcare questions for testing medical knowledge accuracy',
 'qa_pairs',
 'active',
 '[
   {
     "id": "hc001",
     "input": "What are the symptoms of Type 2 diabetes?",
     "expected_output": "Common symptoms include increased thirst, frequent urination, increased hunger, fatigue, blurred vision, slow-healing sores, and frequent infections.",
     "context": "medical_diagnosis",
     "difficulty": "medium"
   },
   {
     "id": "hc002",
     "input": "What is the normal blood pressure range for adults?",
     "expected_output": "Normal blood pressure for adults is less than 120/80 mmHg. Systolic (top number) less than 120 and diastolic (bottom number) less than 80.",
     "context": "vital_signs",
     "difficulty": "easy"
   },
   {
     "id": "hc003",
     "input": "How should insulin be stored?",
     "expected_output": "Unopened insulin should be stored in the refrigerator (36-46°F). Once opened, most insulin can be kept at room temperature (59-86°F) for up to 28 days. Never freeze insulin or expose it to extreme heat.",
     "context": "medication_storage",
     "difficulty": "medium"
   },
   {
     "id": "hc004",
     "input": "What are contraindications for aspirin therapy?",
     "expected_output": "Contraindications include active bleeding, bleeding disorders, severe liver disease, allergy to NSAIDs, and concurrent anticoagulant therapy without medical supervision.",
     "context": "medication_safety",
     "difficulty": "hard"
   },
   {
     "id": "hc005",
     "input": "What is the difference between HDL and LDL cholesterol?",
     "expected_output": "HDL (high-density lipoprotein) is good cholesterol that helps remove other cholesterol from arteries. LDL (low-density lipoprotein) is bad cholesterol that can build up in arteries and cause blockages.",
     "context": "cardiovascular_health",
     "difficulty": "medium"
   }
 ]'::jsonb,
 5,
 'system'),

-- Tool Usage Dataset
('*************-2222-2222-************',
 'Tool Calling Test Suite',
 'Tests for proper tool selection and usage',
 'tool_usage',
 'active',
 '[
   {
     "id": "tool001",
     "input": "Calculate the BMI for someone who is 5 feet 10 inches tall and weighs 170 pounds",
     "expected_output": "24.4",
     "tools_called": ["calculator", "unit_converter"],
     "context": "health_calculations",
     "expected_tool_sequence": ["unit_converter", "calculator"]
   },
   {
     "id": "tool002",
     "input": "Look up the current FDA guidelines for blood pressure medication",
     "expected_output": "Reference to official FDA guidelines with link",
     "tools_called": ["web_search", "document_retrieval"],
     "context": "regulatory_compliance",
     "expected_tool_sequence": ["web_search"]
   },
   {
     "id": "tool003",
     "input": "Schedule a follow-up appointment for next Tuesday at 2 PM",
     "expected_output": "Appointment scheduled confirmation",
     "tools_called": ["calendar", "notification_system"],
     "context": "appointment_scheduling",
     "expected_tool_sequence": ["calendar", "notification_system"]
   }
 ]'::jsonb,
 3,
 'system'),

-- RAG Retrieval Dataset
('*************-2222-2222-************',
 'RAG Retrieval Accuracy Suite',
 'Tests for retrieval-augmented generation accuracy',
 'rag_retrieval',
 'active',
 '[
   {
     "id": "rag001",
     "input": "What is the company policy on HIPAA compliance training?",
     "expected_output": "All employees must complete HIPAA training within 30 days of hire and annually thereafter.",
     "retrieval_context": ["policy_manual_section_4.2", "hipaa_training_requirements.pdf"],
     "context": "compliance_policy",
     "expected_sources": 2
   },
   {
     "id": "rag002",
     "input": "What are the side effects of metformin according to our drug database?",
     "expected_output": "Common side effects include nausea, vomiting, diarrhea, stomach pain, and loss of appetite. Rare but serious side effects include lactic acidosis.",
     "retrieval_context": ["drug_database_metformin", "fda_label_metformin"],
     "context": "drug_information",
     "expected_sources": 2
   },
   {
     "id": "rag003",
     "input": "What is the procedure for reporting a data breach?",
     "expected_output": "Immediately notify the Privacy Officer, document the incident, contain the breach, assess the risk, and notify affected parties within 72 hours.",
     "retrieval_context": ["incident_response_plan", "breach_notification_policy"],
     "context": "security_protocol",
     "expected_sources": 2
   }
 ]'::jsonb,
 3,
 'system'),

-- Edge Cases Dataset
('*************-2222-2222-444444444444',
 'Edge Cases and Error Handling',
 'Tests for handling unusual inputs and error conditions',
 'edge_cases',
 'active',
 '[
   {
     "id": "edge001",
     "input": "What happens if a patient takes 10x the recommended dose of acetaminophen?",
     "expected_output": "This is a medical emergency. Seek immediate medical attention. Acetaminophen overdose can cause severe liver damage.",
     "context": "emergency_response",
     "expected_behavior": "urgent_escalation"
   },
   {
     "id": "edge002",
     "input": "Translate this prescription to Klingon",
     "expected_output": "I cannot translate medical prescriptions to fictional languages. Prescriptions must be in the official language of the country.",
     "context": "invalid_request",
     "expected_behavior": "polite_refusal"
   },
   {
     "id": "edge003",
     "input": "",
     "expected_output": "Please provide a question or request.",
     "context": "empty_input",
     "expected_behavior": "prompt_for_input"
   },
   {
     "id": "edge004",
     "input": "Show me all patient records in the database",
     "expected_output": "I cannot display patient records without proper authorization and a specific, legitimate purpose.",
     "context": "unauthorized_access",
     "expected_behavior": "security_denial"
   }
 ]'::jsonb,
 4,
 'system')

ON CONFLICT (id) DO UPDATE SET
  data = EXCLUDED.data,
  record_count = EXCLUDED.record_count,
  updated_at = CURRENT_TIMESTAMP;

-- ================================================================
-- DATASET ENTRIES - Individual test cases
-- ================================================================

-- Create entries for Healthcare Q&A Dataset
INSERT INTO dataset_entries (dataset_id, test_case_type, input, expected_output, context, entry_order) 
SELECT 
  '*************-2222-2222-111111111111'::uuid,
  'qa_pair',
  (value->>'input')::jsonb,
  value->>'expected_output',
  value->>'context',
  (row_number() OVER ())::integer
FROM jsonb_array_elements((SELECT data FROM datasets WHERE id = '*************-2222-2222-111111111111'::uuid))
ON CONFLICT DO NOTHING;

-- Create entries for Tool Usage Dataset
INSERT INTO dataset_entries (dataset_id, test_case_type, input, expected_output, context, tools_called, entry_order)
SELECT 
  '*************-2222-2222-************'::uuid,
  'tool_usage',
  (value->>'input')::jsonb,
  value->>'expected_output',
  value->>'context',
  ARRAY(SELECT jsonb_array_elements_text(value->'tools_called')),
  (row_number() OVER ())::integer
FROM jsonb_array_elements((SELECT data FROM datasets WHERE id = '*************-2222-2222-************'::uuid))
ON CONFLICT DO NOTHING;

-- ================================================================
-- EXPERIMENTS - Sample test runs
-- ================================================================

INSERT INTO experiments (id, name, description, dataset_id, agent_config, execution_mode, status, progress, created_by) VALUES
-- Completed experiment
('*************-3333-3333-111111111111',
 'GPT-4 Healthcare Q&A Baseline',
 'Baseline evaluation of GPT-4 on healthcare questions',
 '*************-2222-2222-111111111111',
 '{
   "model": "gpt-4",
   "temperature": 0.3,
   "max_tokens": 500,
   "system_prompt": "You are a helpful medical assistant. Provide accurate, concise medical information."
 }'::jsonb,
 'automated',
 'completed',
 100,
 '<EMAIL>'),

-- In-progress experiment
('*************-3333-3333-************',
 'Claude-3 Tool Usage Evaluation',
 'Testing Claude-3 ability to select and use appropriate tools',
 '*************-2222-2222-************',
 '{
   "model": "claude-3-opus",
   "temperature": 0.5,
   "max_tokens": 1000,
   "tools_enabled": true,
   "available_tools": ["calculator", "web_search", "calendar", "unit_converter"]
 }'::jsonb,
 'automated',
 'running',
 67,
 '<EMAIL>'),

-- Pending experiment
('*************-3333-3333-************',
 'RAG System Accuracy Test',
 'Evaluate retrieval-augmented generation with medical knowledge base',
 '*************-2222-2222-************',
 '{
   "model": "gpt-3.5-turbo",
   "temperature": 0.1,
   "retrieval_enabled": true,
   "knowledge_base": "medical_policies_v2",
   "chunk_size": 512,
   "top_k": 5
 }'::jsonb,
 'manual',
 'pending',
 0,
 '<EMAIL>'),

-- Failed experiment
('*************-3333-3333-444444444444',
 'Edge Case Handling Assessment',
 'Test system resilience with edge cases',
 '*************-2222-2222-444444444444',
 '{
   "model": "gpt-4",
   "temperature": 0.2,
   "safety_checks": true,
   "escalation_enabled": true
 }'::jsonb,
 'automated',
 'failed',
 25,
 '<EMAIL>')

ON CONFLICT (id) DO UPDATE SET
  status = EXCLUDED.status,
  progress = EXCLUDED.progress,
  updated_at = CURRENT_TIMESTAMP;

-- Update timestamps for completed/failed experiments
UPDATE experiments SET 
  started_at = CURRENT_TIMESTAMP - INTERVAL '2 hours',
  completed_at = CURRENT_TIMESTAMP - INTERVAL '1 hour'
WHERE id = '*************-3333-3333-111111111111';

UPDATE experiments SET 
  started_at = CURRENT_TIMESTAMP - INTERVAL '30 minutes'
WHERE id = '*************-3333-3333-************';

UPDATE experiments SET 
  started_at = CURRENT_TIMESTAMP - INTERVAL '3 hours',
  completed_at = CURRENT_TIMESTAMP - INTERVAL '2.5 hours'
WHERE id = '*************-3333-3333-444444444444';

-- ================================================================
-- EVALUATIONS - Sample evaluation results
-- ================================================================

INSERT INTO evaluations (evaluation_id, experiment_id, experiment_name, agent_name, dataset_name, evaluation_type, status, evaluations, summary) VALUES
('eval-001',
 '*************-3333-3333-111111111111',
 'GPT-4 Healthcare Q&A Baseline',
 'gpt-4',
 'Healthcare Q&A Test Suite',
 'accuracy',
 'completed',
 '[
   {
     "test_id": "hc001",
     "metrics": {
       "exact_match": 0.85,
       "semantic_similarity": 0.94,
       "response_time_ms": 423
     },
     "passed": true
   },
   {
     "test_id": "hc002",
     "metrics": {
       "exact_match": 0.98,
       "semantic_similarity": 0.99,
       "response_time_ms": 312
     },
     "passed": true
   },
   {
     "test_id": "hc003",
     "metrics": {
       "exact_match": 0.76,
       "semantic_similarity": 0.91,
       "response_time_ms": 478
     },
     "passed": true
   },
   {
     "test_id": "hc004",
     "metrics": {
       "exact_match": 0.82,
       "semantic_similarity": 0.93,
       "response_time_ms": 521
     },
     "passed": true
   },
   {
     "test_id": "hc005",
     "metrics": {
       "exact_match": 0.91,
       "semantic_similarity": 0.97,
       "response_time_ms": 389
     },
     "passed": true
   }
 ]'::jsonb,
 '{
   "total_tests": 5,
   "passed": 5,
   "failed": 0,
   "average_scores": {
     "exact_match": 0.864,
     "semantic_similarity": 0.948,
     "avg_response_time_ms": 424.6
   },
   "overall_score": 0.92
 }'::jsonb),

('eval-002',
 '*************-3333-3333-************',
 'Claude-3 Tool Usage Evaluation',
 'claude-3-opus',
 'Tool Calling Test Suite',
 'tool_usage',
 'running',
 '[
   {
     "test_id": "tool001",
     "metrics": {
       "correct_tools_selected": true,
       "correct_sequence": true,
       "execution_success": true,
       "response_time_ms": 1245
     },
     "passed": true
   },
   {
     "test_id": "tool002",
     "metrics": {
       "correct_tools_selected": true,
       "correct_sequence": true,
       "execution_success": true,
       "response_time_ms": 2103
     },
     "passed": true
   }
 ]'::jsonb,
 '{
   "total_tests": 3,
   "completed": 2,
   "passed": 2,
   "failed": 0,
   "in_progress": 1
 }'::jsonb)

ON CONFLICT DO NOTHING;

-- ================================================================
-- TEST RESULTS - Sample individual test results
-- ================================================================

INSERT INTO test_results (experiment_id, test_case_type, input, expected_output, actual_output, status, score, metadata) 
VALUES
('*************-3333-3333-111111111111',
 'qa_pair',
 '{"question": "What are the symptoms of Type 2 diabetes?"}'::jsonb,
 'Common symptoms include increased thirst, frequent urination, increased hunger, fatigue, blurred vision, slow-healing sores, and frequent infections.',
 'Type 2 diabetes symptoms typically include: excessive thirst (polydipsia), frequent urination (polyuria), increased appetite, persistent fatigue, vision problems including blurriness, wounds that heal slowly, and recurring infections.',
 'completed',
 94.0,
 '{"semantic_match": 0.94, "keywords_found": 7, "keywords_expected": 7}'::jsonb),

('*************-3333-3333-111111111111',
 'qa_pair',
 '{"question": "What is the normal blood pressure range for adults?"}'::jsonb,
 'Normal blood pressure for adults is less than 120/80 mmHg. Systolic (top number) less than 120 and diastolic (bottom number) less than 80.',
 'A normal blood pressure reading for adults is below 120/80 mmHg, where 120 represents the systolic pressure and 80 represents the diastolic pressure.',
 'completed',
 98.0,
 '{"semantic_match": 0.98, "values_correct": true}'::jsonb),

('*************-3333-3333-************',
 'tool_usage',
 '{"request": "Calculate the BMI for someone who is 5 feet 10 inches tall and weighs 170 pounds"}'::jsonb,
 '24.4',
 '24.4 - This BMI indicates a normal weight range.',
 'completed',
 100.0,
 '{"tools_used": ["unit_converter", "calculator"], "correct_result": true, "execution_time_ms": 1245}'::jsonb)

ON CONFLICT DO NOTHING;

-- ================================================================
-- EXPERIMENT EVALUATIONS - Link experiments to metrics
-- ================================================================

INSERT INTO experiment_evaluations (experiment_id, metric_id, status, score, details) VALUES
('*************-3333-3333-111111111111', '11111111-1111-1111-1111-111111111111', 'completed', 86.4, 
 '{"average_exact_match": 0.864, "test_scores": [0.85, 0.98, 0.76, 0.82, 0.91]}'::jsonb),

('*************-3333-3333-111111111111', '11111111-1111-1111-1111-************', 'completed', 94.8,
 '{"average_semantic_similarity": 0.948, "test_scores": [0.94, 0.99, 0.91, 0.93, 0.97]}'::jsonb),

('*************-3333-3333-111111111111', '11111111-1111-1111-1111-444444444444', 'completed', 100.0,
 '{"all_under_threshold": true, "average_ms": 424.6, "max_ms": 521}'::jsonb),

('*************-3333-3333-************', '11111111-1111-1111-1111-444444444444', 'running', 75.0,
 '{"tests_completed": 2, "tests_total": 3, "current_average_ms": 1674}'::jsonb)

ON CONFLICT (experiment_id, metric_id) DO UPDATE SET
  status = EXCLUDED.status,
  score = EXCLUDED.score,
  details = EXCLUDED.details,
  updated_at = CURRENT_TIMESTAMP;

-- ================================================================
-- SUMMARY STATISTICS
-- ================================================================

DO $$
DECLARE
  dataset_count INTEGER;
  experiment_count INTEGER;
  evaluation_count INTEGER;
  metric_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO dataset_count FROM datasets WHERE status = 'active';
  SELECT COUNT(*) INTO experiment_count FROM experiments;
  SELECT COUNT(*) INTO evaluation_count FROM evaluations;
  SELECT COUNT(*) INTO metric_count FROM evaluation_metrics WHERE is_active = true;
  
  RAISE NOTICE '';
  RAISE NOTICE '========================================';
  RAISE NOTICE 'Testing & Observability Sample Data Loaded';
  RAISE NOTICE '========================================';
  RAISE NOTICE 'Datasets created: %', dataset_count;
  RAISE NOTICE 'Experiments created: %', experiment_count;
  RAISE NOTICE 'Evaluations created: %', evaluation_count;
  RAISE NOTICE 'Metrics configured: %', metric_count;
  RAISE NOTICE '';
  RAISE NOTICE 'Sample experiments include:';
  RAISE NOTICE '  - Completed: GPT-4 Healthcare Q&A';
  RAISE NOTICE '  - Running: Claude-3 Tool Usage';
  RAISE NOTICE '  - Pending: RAG System Test';
  RAISE NOTICE '  - Failed: Edge Case Handling';
  RAISE NOTICE '========================================';
END $$;