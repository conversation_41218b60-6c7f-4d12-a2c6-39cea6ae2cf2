#!/bin/bash

# ==============================================================================
# Template Management System - Post-Deployment Validation Script
# ==============================================================================
# This script validates the successful deployment of the template management
# system by running comprehensive checks and generating a validation report
# ==============================================================================

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"5432"}
DB_NAME=${DB_NAME:-"vitea_db"}
DB_USER=${DB_USER:-"dbadmin"}
API_HOST=${API_HOST:-"localhost"}
API_PORT=${API_PORT:-"8001"}
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
VALIDATION_LOG="validation_${TIMESTAMP}.log"

# Validation counters
CHECKS_PASSED=0
CHECKS_FAILED=0
WARNINGS=0

# ==============================================================================
# Helper Functions
# ==============================================================================

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1" | tee -a "$VALIDATION_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$VALIDATION_LOG"
    ((CHECKS_FAILED++))
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$VALIDATION_LOG"
    ((WARNINGS++))
}

log_success() {
    echo -e "${GREEN}✓${NC} $1" | tee -a "$VALIDATION_LOG"
    ((CHECKS_PASSED++))
}

log_section() {
    echo -e "\n${BLUE}==============================================================================
$1
==============================================================================${NC}" | tee -a "$VALIDATION_LOG"
}

# ==============================================================================
# Database Validation
# ==============================================================================

log_section "DATABASE STRUCTURE VALIDATION"

# Check columns exist
log_info "Checking database schema modifications..."

COLUMNS_CHECK=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_name = 'policy_schemas' 
    AND column_name IN ('default_template', 'template_source');
" | tr -d ' ')

if [ "$COLUMNS_CHECK" = "2" ]; then
    log_success "Database columns added successfully"
else
    log_error "Missing database columns (found $COLUMNS_CHECK/2)"
fi

# Check indexes
log_info "Checking database indexes..."

INDEX_CHECK=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
    SELECT COUNT(*) 
    FROM pg_indexes 
    WHERE tablename = 'policy_schemas' 
    AND indexname LIKE '%template%';
" | tr -d ' ')

if [ "$INDEX_CHECK" -gt 0 ]; then
    log_success "Template indexes created"
else
    log_warning "No template indexes found (may impact performance)"
fi

# Check function exists
log_info "Checking PL/pgSQL function..."

FUNCTION_CHECK=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
    SELECT COUNT(*) 
    FROM pg_proc 
    WHERE proname = 'generate_default_template';
" | tr -d ' ')

if [ "$FUNCTION_CHECK" = "1" ]; then
    log_success "Template generation function exists"
else
    log_error "Template generation function not found"
fi

# ==============================================================================
# Data Integrity Validation
# ==============================================================================

log_section "DATA INTEGRITY VALIDATION"

# Check template population
log_info "Checking template population..."

TEMPLATE_STATS=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
    SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE default_template IS NOT NULL) as with_templates,
        COUNT(DISTINCT template_source) as sources
    FROM policy_schemas 
    WHERE is_active = true;
")

read TOTAL WITH_TEMPLATES SOURCES <<< "$TEMPLATE_STATS"

if [ "$WITH_TEMPLATES" -gt 0 ]; then
    log_success "Templates populated: $WITH_TEMPLATES/$TOTAL schemas have templates"
    log_info "Template sources found: $SOURCES distinct source types"
else
    log_error "No templates found in active schemas"
fi

# Check for orphaned templates
log_info "Checking for orphaned templates..."

OLD_TEMPLATES=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
    SELECT COUNT(*) FROM information_schema.tables 
    WHERE table_name = 'policy_templates';
" | tr -d ' ')

if [ "$OLD_TEMPLATES" = "0" ]; then
    log_success "Old policy_templates table removed"
else
    log_warning "Old policy_templates table still exists (should be removed after verification)"
fi

# ==============================================================================
# API Endpoint Validation
# ==============================================================================

log_section "API ENDPOINT VALIDATION"

# Test template retrieval
log_info "Testing template retrieval endpoint..."

HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" \
    -X GET "http://${API_HOST}:${API_PORT}/api/v1/schemas/medical_privacy/template" \
    -H "Authorization: Bearer admin-token")

if [ "$HTTP_STATUS" = "200" ]; then
    log_success "Template retrieval endpoint working (HTTP $HTTP_STATUS)"
else
    log_error "Template retrieval failed (HTTP $HTTP_STATUS)"
fi

# Test template update
log_info "Testing template update endpoint..."

HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" \
    -X PUT "http://${API_HOST}:${API_PORT}/api/v1/schemas/medical_privacy/template" \
    -H "Authorization: Bearer admin-token" \
    -H "Content-Type: application/json" \
    -d '{"template": {"test": "validation"}, "source": "manual_override"}')

if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "201" ]; then
    log_success "Template update endpoint working (HTTP $HTTP_STATUS)"
else
    log_error "Template update failed (HTTP $HTTP_STATUS)"
fi

# Test status endpoint
log_info "Testing template status endpoint..."

STATUS_RESPONSE=$(curl -s -X GET \
    "http://${API_HOST}:${API_PORT}/api/v1/schemas/templates/status" \
    -H "Authorization: Bearer admin-token")

if echo "$STATUS_RESPONSE" | grep -q '"summary"'; then
    log_success "Template status endpoint working"
else
    log_error "Template status endpoint failed"
fi

# ==============================================================================
# Deprecation Validation
# ==============================================================================

log_section "DEPRECATION VALIDATION"

# Check deprecated endpoints
log_info "Checking deprecated endpoint headers..."

DEPRECATED_HEADER=$(curl -s -I -X GET \
    "http://${API_HOST}:${API_PORT}/api/v1/policies/templates" \
    -H "Authorization: Bearer admin-token" | grep -i "x-deprecated")

if [ -n "$DEPRECATED_HEADER" ]; then
    log_success "Deprecated endpoints properly marked"
else
    log_warning "Deprecated endpoints may not have proper headers"
fi

# ==============================================================================
# Performance Validation
# ==============================================================================

log_section "PERFORMANCE VALIDATION"

log_info "Testing template generation performance..."

# Measure template retrieval time
TOTAL_TIME=0
ITERATIONS=10

for i in $(seq 1 $ITERATIONS); do
    START=$(date +%s%N)
    curl -s -X GET "http://${API_HOST}:${API_PORT}/api/v1/schemas/medical_privacy/template" \
        -H "Authorization: Bearer admin-token" > /dev/null
    END=$(date +%s%N)
    TIME=$((($END - $START) / 1000000))
    TOTAL_TIME=$((TOTAL_TIME + TIME))
done

AVG_TIME=$((TOTAL_TIME / ITERATIONS))

if [ $AVG_TIME -lt 100 ]; then
    log_success "Excellent performance: ${AVG_TIME}ms average response time"
elif [ $AVG_TIME -lt 500 ]; then
    log_success "Good performance: ${AVG_TIME}ms average response time"
else
    log_warning "Performance may need optimization: ${AVG_TIME}ms average response time"
fi

# ==============================================================================
# Integration Validation
# ==============================================================================

log_section "INTEGRATION VALIDATION"

# Test policy creation with template
log_info "Testing policy creation with template..."

POLICY_RESPONSE=$(curl -s -X POST \
    "http://${API_HOST}:${API_PORT}/api/v1/policies" \
    -H "Authorization: Bearer admin-token" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Validation Test Policy",
        "description": "Testing template integration",
        "policy_type": "medical_privacy",
        "category": "Test",
        "definition": {}
    }')

if echo "$POLICY_RESPONSE" | grep -q '"id"'; then
    log_success "Policy creation with template working"
    
    # Clean up test policy
    POLICY_ID=$(echo "$POLICY_RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
    curl -s -X DELETE "http://${API_HOST}:${API_PORT}/api/v1/policies/$POLICY_ID" \
        -H "Authorization: Bearer admin-token" > /dev/null
else
    log_error "Policy creation with template failed"
fi

# ==============================================================================
# Security Validation
# ==============================================================================

log_section "SECURITY VALIDATION"

# Test unauthorized access
log_info "Testing security controls..."

HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" \
    -X PUT "http://${API_HOST}:${API_PORT}/api/v1/schemas/medical_privacy/template" \
    -H "Content-Type: application/json" \
    -d '{"template": {"test": "unauthorized"}}')

if [ "$HTTP_STATUS" = "401" ] || [ "$HTTP_STATUS" = "403" ]; then
    log_success "Security controls working (unauthorized rejected)"
else
    log_error "Security issue: unauthorized access not properly rejected"
fi

# ==============================================================================
# Audit Trail Validation
# ==============================================================================

log_section "AUDIT TRAIL VALIDATION"

log_info "Checking audit logs..."

AUDIT_LOGS=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
    SELECT COUNT(*) 
    FROM audit_log 
    WHERE action LIKE '%TEMPLATE%' 
    AND created_at > NOW() - INTERVAL '1 hour';
" | tr -d ' ')

if [ "$AUDIT_LOGS" -gt 0 ]; then
    log_success "Audit logging working: $AUDIT_LOGS template operations logged"
else
    log_warning "No recent template operations in audit log"
fi

# ==============================================================================
# Validation Summary
# ==============================================================================

log_section "VALIDATION SUMMARY"

TOTAL_CHECKS=$((CHECKS_PASSED + CHECKS_FAILED))

echo ""
cat << EOF | tee -a "$VALIDATION_LOG"

VALIDATION REPORT
================
Deployment validated at: $(date)

Results:
--------
✓ Checks Passed: $CHECKS_PASSED
✗ Checks Failed: $CHECKS_FAILED
⚠ Warnings: $WARNINGS
Total Checks: $TOTAL_CHECKS

Success Rate: $(( (CHECKS_PASSED * 100) / TOTAL_CHECKS ))%

EOF

if [ $CHECKS_FAILED -eq 0 ]; then
    echo -e "${GREEN}VALIDATION SUCCESSFUL${NC}" | tee -a "$VALIDATION_LOG"
    echo "The template management system is fully operational."
    echo ""
    echo "Next Steps:"
    echo "1. Monitor system for 24-48 hours"
    echo "2. Review performance metrics"
    echo "3. Schedule cleanup of deprecated components"
    exit 0
else
    echo -e "${RED}VALIDATION FAILED${NC}" | tee -a "$VALIDATION_LOG"
    echo "Please review the failures above and take corrective action."
    echo ""
    echo "Troubleshooting:"
    echo "1. Check deployment logs: deployment_*.log"
    echo "2. Review API logs: docker logs pilot-api"
    echo "3. Check database state: psql -U $DB_USER -d $DB_NAME"
    echo "4. Consider rollback if critical issues found"
    exit 1
fi