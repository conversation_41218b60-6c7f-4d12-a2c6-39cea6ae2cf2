# End-to-End Test Suite

## Overview

This directory contains comprehensive end-to-end tests for the Vitea.ai Policy Management System.

## Test Scripts

### `e2e-policy-lifecycle-test.js`

A comprehensive end-to-end test that covers the complete policy management lifecycle:

#### Core Test Scenarios:
1. **Schema Management**
   - Create policy schema with JSON Schema validation
   - Verify schema retrieval and metadata
   - Test schema validation with valid/invalid data
   - Prevent duplicate schema names
   - Reject invalid JSON Schema content

2. **Policy Group Management**
   - Create policy groups
   - Assign policies to groups
   - Test deactivation and cascading effects

3. **Policy Management**
   - Create policies based on schemas with validation
   - Test policy creation with non-existent schemas (should fail)
   - Assign policies to groups
   - Verify policy deactivation removes assignments

4. **Agent & Role Management**
   - Create agents and roles
   - Assign policies to agents via roles and groups
   - Verify assignments appear in policy modal
   - Test agent deactivation

5. **Data Integrity**
   - Verify cascading deactivation effects
   - Check that deactivated resources are properly removed from assignments
   - Ensure data consistency after all operations

#### Extended Test Scenarios:
- Duplicate name prevention
- Invalid schema content rejection
- Orphaned policy prevention
- Error handling validation
- Resource cleanup verification

## Prerequisites

1. **API Server Running**: The API server must be running on `localhost:8000`
2. **Database Access**: Clean test environment with database access
3. **Authentication**: Valid admin token configured
4. **Node.js**: Node.js 14+ with npm installed

## Dependencies

```bash
# Install required dependencies
npm install axios assert
```

## Usage

### Run Complete Test Suite
```bash
# From the project root
node tests/e2e-policy-lifecycle-test.js

# Or make it executable and run directly
chmod +x tests/e2e-policy-lifecycle-test.js
./tests/e2e-policy-lifecycle-test.js
```

### Configure Test Settings

Edit the configuration in the test file:

```javascript
const CONFIG = {
  BASE_URL: 'http://localhost:8000',
  AUTH_TOKEN: 'admin-token',
  TEST_TIMEOUT: 30000,
  CLEANUP_ON_FAILURE: true
};
```

### Test Data Naming

The test uses timestamped names to allow multiple runs without conflicts:

```
e2e_test_1693225200000_healthcare_compliance
e2e_test_1693225200000_healthcare_group
e2e_test_1693225200000_patient_data_protection
...
```

## Test Output

### Success Example
```
[2025-08-28T12:00:00.000Z] [START] 🚀 Starting E2E Policy Lifecycle Test Suite (e2e_test_1693225200000)
[2025-08-28T12:00:00.100Z] [TEST] Starting test: Create Policy Schema
[2025-08-28T12:00:00.200Z] [PASS] ✅ PASSED: Create Policy Schema
[2025-08-28T12:00:00.300Z] [TEST] Starting test: Verify Schema Retrieval
[2025-08-28T12:00:00.400Z] [PASS] ✅ PASSED: Verify Schema Retrieval
...
[2025-08-28T12:05:00.000Z] [SUCCESS] 🎉 ALL TESTS PASSED! 🎉
```

### Failure Example
```
[2025-08-28T12:00:00.000Z] [TEST] Starting test: Create Policy Schema
[2025-08-28T12:00:00.100Z] [FAIL] ❌ FAILED: Create Policy Schema - Schema creation should succeed
[2025-08-28T12:00:00.200Z] [CLEANUP] Starting cleanup of test resources...
[2025-08-28T12:00:00.300Z] [ERROR] 💥 1 TESTS FAILED 💥
```

## Cleanup

The test suite automatically cleans up all created resources:
- Schemas are deactivated
- Policy groups are deactivated  
- Policies are deactivated
- Agents are deactivated
- Roles are deleted
- All assignments are removed

Cleanup occurs:
- After successful completion of all tests
- On test failure (if `CLEANUP_ON_FAILURE` is true)
- Can be run manually by calling the `cleanup()` method

## Troubleshooting

### Common Issues

1. **Connection Refused**
   ```
   Error: connect ECONNREFUSED 127.0.0.1:8000
   ```
   - Ensure the API server is running on port 8000
   - Check that the database is accessible

2. **Authentication Failed**
   ```
   Status: 401 - Unauthorized
   ```
   - Verify the `AUTH_TOKEN` in the configuration
   - Ensure the token has admin privileges

3. **Test Data Conflicts**
   ```
   Status: 409 - Conflict
   ```
   - Clean up previous test data
   - The test should auto-generate unique names

4. **Timeout Errors**
   ```
   Error: timeout of 30000ms exceeded
   ```
   - Increase `TEST_TIMEOUT` in configuration
   - Check database performance

### Manual Cleanup

If automatic cleanup fails, manually clean up test resources:

```sql
-- Clean up test schemas
UPDATE policy_schemas SET is_active = false WHERE schema_name LIKE 'e2e_test_%';

-- Clean up test policy groups
UPDATE policy_groups SET is_active = false WHERE name LIKE 'e2e_test_%';

-- Clean up test policies
UPDATE policies SET deleted_at = CURRENT_TIMESTAMP WHERE name LIKE 'e2e_test_%';

-- Clean up test agents
UPDATE agents SET is_active = false WHERE name LIKE 'e2e_test_%';

-- Clean up test roles
DELETE FROM roles WHERE name LIKE 'e2e_test_%';
```

## Extending Tests

### Add New Test Scenarios

```javascript
await this.runTest('My Custom Test', async () => {
  // Your test logic here
  const result = await this.api.someMethod();
  assert(result.success, 'Custom test should pass');
  this.log('Custom test completed successfully');
});
```

### Add New API Methods

```javascript
// Add to APIClient class
async myCustomApiMethod(data) {
  const response = await this.client.post('/api/v1/custom-endpoint', data);
  return response.data;
}
```

### Custom Test Configuration

Create a custom configuration file:

```javascript
// custom-test-config.js
module.exports = {
  BASE_URL: 'https://staging.vitea.ai',
  AUTH_TOKEN: process.env.STAGING_AUTH_TOKEN,
  TEST_TIMEOUT: 60000,
  CLEANUP_ON_FAILURE: false
};
```

## CI/CD Integration

### GitHub Actions Example

```yaml
name: E2E Tests
on: [push, pull_request]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
        
      - name: Start API server
        run: npm start &
        
      - name: Wait for API
        run: sleep 30
        
      - name: Run E2E tests
        run: node tests/e2e-policy-lifecycle-test.js
        env:
          AUTH_TOKEN: ${{ secrets.TEST_AUTH_TOKEN }}
```

### Docker Integration

```dockerfile
# Add to your test Dockerfile
COPY tests/ /app/tests/
RUN npm install axios assert
CMD ["node", "tests/e2e-policy-lifecycle-test.js"]
```

## Reporting

The test suite generates detailed reports including:
- Individual test results
- Resource creation/cleanup logs
- Error details and stack traces
- Performance timing information
- Summary statistics

Results can be integrated with CI/CD reporting tools or exported to various formats.